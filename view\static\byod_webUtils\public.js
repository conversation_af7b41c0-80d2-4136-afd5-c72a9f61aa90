function goIndex() {
  // 判断购物车是否有数据,提示是否清空
  let { btnTxtForCancel, btnTxtForConfirm, backSelectTableTip } = app.systemLanguage
  let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []

  if (shopCartList.length == 0) {
    isCheckPassword().then(res => {})
  } else {
    layer.alert(backSelectTableTip, {
      title: false,
      skin: "defaultLayer",
      btn: [btnTxtForConfirm],
      yes: i => {
        layer.close(i)
        isCheckPassword().then(res => {
          sessionStorage.removeItem("shopCartList") // 清空购物车数据
        })
      },
      cancel: () => {}
    })
  }
}
function JumpFun() {
  let url = sessionStorage.getItem("indexPageUrl")
  localStorage.setItem("language", app.openTable.language)
  localStorage.setItem("indexPageUrl", url)
  window.location.href = url
}

function isCheckPassword() {
  return new Promise((resolve, reject) => {
    let { initialTableNum, assistMode: { exitUsingPassword } = {} } = app.openTable
    if (["AssistMode", "EnhAssistMode"].includes(initialTableNum) && exitUsingPassword) {
      let { btnTxtForConfirm, logoutTitle, statusCode406, passwordInputPh } = app.systemLanguage
      //layui弹窗输入密码
      let content = `
      <div class="checkPassword-form-warp">
        <div class="checkPassword-form-item">
          <label class="passwordModel-label">
            <img alt="" class="passWordIcon" src="../static/img/newImage/passWord.jpg" />
          </label>
          <div class="checkPasswordInput layui-input-inline">
            <input  class="layui-input"  type="password" id="checkPasswordInput" placeholder="${passwordInputPh}" />
            <a
              class="eye-warp icon-eye-close"
              href="#"
              id="passwordEye"
            ></a>
          </div>
        </div>
        <div class="checkPasswordButton" >
          <button id="submitButton"  class="layui-btn layui-btn-normal">${btnTxtForConfirm}</button>
        </div>
      </div>`

      layer.open({
        type: 1,
        shade: [0.1, "#fff"],
        content: content,
        title: logoutTitle,
        skin: "backCheckPasswordLayer",
        success: function (layero, index) {
          const submitButton = $("#submitButton")
          const passwordInput = $("#checkPasswordInput")
          //自动聚焦
          passwordInput.focus()
          // 监听眼睛图标的点击事件
          $("#passwordEye").on("click", function () {
            //判断是否是密码框
            if (passwordInput.attr("type") == "password") {
              passwordInput.attr("type", "text")
              $(this).removeClass("icon-eye-close").addClass("icon-eye-open")
            } else {
              passwordInput.attr("type", "password")
              $(this).removeClass("icon-eye-open").addClass("icon-eye-close")
            }
          })
          // 监听提交按钮的点击事件
          submitButton.on("click", function () {
            const inputValue = passwordInput.val()
            $.post({
              url: "../uiConfig/checkPassword",
              data: {
                domain: sessionStorage.getItem("domain"),
                storeNumber: app.openTable.storeNumber,
                mode: app.openTable.initialTableNum,
                password: inputValue
              },
              dataType: "json",
              success: res => {
                if (res.statusCode == 200) {
                  layer.close(index, function () {
                    JumpFun()
                  })
                  resolve(true)
                } else {
                  reject(false)
                  layer.msg(statusCode406)
                }
              },
              error: error => {
                layer.msg("The request failed, please try again")
                reject(error)
              }
            })
          })
        }
      })
    } else {
      JumpFun()
      resolve(true)
    }
  })
}

function goMenuPage() {
  window.location.href = "../order/menuPage"
}

// prams, errorRes,
function timeOutPromptPop(prompt, page) {
  let content = ""
  if (page == "index") {
    content = $(".timeOutPromptDia")
  } else {
    content = prompt
  }
  layui.use("layer", function () {
    var layer = layui.layer
    layer.open({
      type: 1,
      shade: false,
      closeBtn: 1,
      area: ["78%"],
      skin: "timeOutPrompt-Pop",
      title: false, //是否显示标题
      content: content //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相对元素所影响
    })
  })
}
//计算对应rem的px值
function remToPx(rem) {
  // 获取根元素的字体大小
  var rootFontSize = parseFloat(window.getComputedStyle(document.documentElement).fontSize)

  // 计算rem的实际像素值
  var px = rem * rootFontSize

  return px
}
//判断是否提示下单等待时间或者爆仓暂停下单
function checkOrderWait(domain, storeNumber, page = "menu", firstRequest = false) {
  return new Promise((resolve, reject) => {
    if (
      (page == "index" && !app.initConfigObj.salesControl) ||
      (page != "index" && !app.openTable.salesControl)
    ) {
      return resolve("missingConfiguration")
    }
    getSalesCount(domain, storeNumber, page, firstRequest)
      .then(({ salesCount, lastUpdatedTime }) => {
        // let mockRes = {
        //   countScope: 60,
        //   maxCount: 100,
        //   estimatedTime: 30,
        //   rule: [
        //     {
        //       count: 1,
        //       minute: 3
        //     },
        //     {
        //       count: 3,
        //       minute: 5
        //     },
        //     {
        //       count: 5,
        //       minute: 10
        //     },
        //     {
        //       count: 10,
        //       minute: 25
        //     },
        //     {
        //       count: 20,
        //       minute: 40
        //     }
        //   ]
        // }
        const salesControl =
          page === "index" ? app.initConfigObj.salesControl : app.openTable.salesControl
        if (salesControl) return resolve("missingConfiguration")
        let {
          maxCount, //最大下单数量(超过就暂停下单)
          estimatedTime, //估计禁止下单等待时间
          rule = [] //规则
        } = salesControl
        let isOrderStop = false
        let isOrderWait = false
        let orderWaitObj = {
          waitTxt: "",
          waitTime: []
        }
        let queueTime = 0
        if (salesCount >= maxCount) {
          isOrderStop = true
          queueTime = estimatedTime
        } else if (page != "index") {
          if (!rule) return { isOrderStop, isOrderWait, orderWaitObj } //没有规则直接返回
          let sortRule = rule.sort((a, b) => a.count - b.count) //升序
          let isLessThanMin = rule.every(item => salesCount < item.count)
          if (!isLessThanMin) {
            //salesCount大于规则最小值
            let ruleItem = rule.find(item => item.count == salesCount)
            if (ruleItem) {
              //刚好等于规则中的数字显示orderWaitTimeTip
              isOrderWait = true
              queueTime = ruleItem.minute
              orderWaitObj = {
                waitTxt: "orderWaitTimeTip",
                waitTime: [queueTime]
              }
              // orderWaitTimeTip.replace("#min", spanDom(ruleItem.minute))
            } else {
              let copySortRule = JSON.parse(JSON.stringify(sortRule))
              console.log(JSON.parse(JSON.stringify(sortRule)), "sortRule")
              let reverseRule = copySortRule.reverse() //倒序(从大到小用于寻找最小值)
              console.log(JSON.parse(JSON.stringify(reverseRule)), "reverseRule")
              //判断是否在规则范围内
              let minItem = reverseRule.find(item => salesCount > item.count)
              let maxItem = rule.find(item => salesCount < item.count)
              console.log("🚀 ~ checkOrderWait ~ maxItem:", maxItem)
              if (minItem && maxItem) {
                isOrderWait = true
                orderWaitObj = {
                  waitTxt: "orderWaitRangeTip",
                  waitTime: [minItem.minute, maxItem.minute]
                }
                // orderWaitRangeTip
                //   .replace("#minMinutes", spanDom(minItem.minute))
                //   .replace("#maxMinutes", spanDom(maxItem.minute))
                queueTime = `${minItem.minute}~${maxItem.minute}`
              } else if (minItem || maxItem) {
                isOrderWait = true
                let item = minItem ? minItem : maxItem
                orderWaitObj = {
                  waitTxt: "orderWaitTimeTip",
                  waitTime: [item.minute]
                }
                // orderWaitTimeTip.replace("#min", spanDom(item.minute))
                queueTime = item.minute
              } else {
                //不在规则范围内
              }
            }
          } else {
            //salesCount小于规则最小值不做任何处理
          }
        }
        app.queuedObj = { isOrderStop, isOrderWait, orderWaitObj, queueTime, lastUpdatedTime }
        console.log(JSON.parse(JSON.stringify(app.queuedObj)), "app.queuedObj")
        // let isInitStopDia = app.$refs.orderStopDia ? true : false
        if (isOrderStop && !app.$refs.orderStopDia) {
          app.$nextTick(() => {
            layer.open({
              title: false,
              closeBtn: 0,
              type: 1,
              skin: "orderStopDia",
              shade: [0.1, "#fff"],
              area: ["100%", "100%"],
              content: $(".queue-app"),
              success: (layero, index) => {
                app.$refs.orderStopDia.layerIndex = index
              },
              yes: (index, layero) => {},
              end: () => {
                if (page == "index") {
                  app.openTable(app.initTableNumber)
                }
              }
            })
          })
        }
      })
      .finally(() => {
        resolve()
      })
  })

  // return { isOrderStop, isOrderWait, orderWaitObj, queueTime }
}

function getSalesCount(domain, storeNumber, currentPage, firstRequest) {
  let requestLoading = null
  if (!firstRequest) requestLoading = layer.load(2)
  return new Promise((resolve, reject) => {
    // setTimeout(() => {}, 1000)
    $.post({
      url: (currentPage == "index" ? "./" : "../") + "salesControl/getSalesCount",
      dataType: "json",
      data: {
        domain: domain,
        storeNumber: storeNumber
      },
      dataType: "json",
      success: res => {
        // console.log(res, "getSalesCount")
        if (res.statusCode != 200) {
          reject(res)
        }
        resolve({ salesCount: res.salesCount, lastUpdatedTime: moment().format("HH:mm:ss") })
      },
      error: error => {
        reject(error)
      },
      complete: () => {
        if (requestLoading) layer.close(requestLoading) //關閉加載層
      }
    })
  })
}
//判断价格是否需要补零
//o:用于判断的价格,n:需要保留小数的位数
function isMeetLength(o, n) {
  //s:Array => 用小数点分割的数组,预期长度为2
  const s = o.toString().split(".")
  //如果s长度为2,判断s[1]的长度是否满足保留长度,满足则不补零\
  //反之直接满足补零需求
  return s.length === 2 ? s[1].length <= n : true
}
//保留有效小数
function retainSignificantDecimals(price) {
  let openTable = sessionStorage.getItem("openTable")
  if (openTable) {
    openTable = JSON.parse(openTable)
  } else {
    openTable = {}
  }
  const { keepDecimals: decimal } = openTable
  if (decimal) {
    price = typeof price !== "number" ? Number(price) : price
    //n:保留的位数,t:是否补零
    let { significantDigits: n, zeroPadding: t } = decimal
    if (price || price === 0) {
      if (isMeetLength(price, n)) {
        return t ? price.toFixed(n) : price
      }
      return price.toFixed(n)
    } else {
      return price
    }
  }
  return price
}
function toSorted(compareFn) {
  if (!Array.isArray(this)) {
    throw new TypeError("toSorted() requires an array as the first argument")
  }
  const sortedArray = [...this]
  if (!compareFn) {
    compareFn = (a, b) => {
      if (a === undefined) return 1
      if (b === undefined) return -1
      return a.toString().localeCompare(b.toString())
    }
  }
  sortedArray.sort(compareFn)
  return sortedArray
}
if (!Array.prototype.toSorted) {
  Array.prototype.toSorted = toSorted
}
if (typeof Element.prototype.clearChildren === "undefined") {
  Object.defineProperty(Element.prototype, "clearChildren", {
    configurable: true,
    enumerable: false,
    value: function () {
      while (this.firstChild) this.removeChild(this.lastChild)
    }
  })
}
/**
 * 递归地将对象键排序，生成一个稳定的 JSON 字符串，可以安全地用于哈希。
 * @param {any} obj 要进行字符串化的 JavaScript 对象或值。
 * @returns {string} 一个稳定的、键已排序的 JSON 字符串。
 */
function stableStringify(obj) {
  // 基本类型或 null，直接使用 JSON.stringify
  if (obj === null || typeof obj !== "object") {
    return JSON.stringify(obj)
  }

  // 如果是数组，则递归地对数组中的每个元素应用 stableStringify
  if (Array.isArray(obj)) {
    const arrayStr = obj.map(item => stableStringify(item)).join(",")
    return `[${arrayStr}]`
  }

  // 处理核心问题：对象
  // 1. 获取所有 key 并排序
  const keys = Object.keys(obj).sort()

  // 2. 遍历排序后的 key，构建 key-value 字符串对
  const keyValuePairs = keys
    .map(key => {
      const value = obj[key]

      // JSON.stringify 会忽略 undefined, function, Symbol 值的属性
      if (value === undefined || typeof value === "function" || typeof value === "symbol") {
        return null
      }

      const stringifiedValue = stableStringify(value)
      return `"${key}":${stringifiedValue}`
    })
    .filter(pair => pair !== null) // 过滤掉被忽略的属性

  // 3. 拼接成最终的对象字符串
  return `{${keyValuePairs.join(",")}}`
}

function goHistoryAfterOrder(performType) {
  if (performType == 1) {
    window.location.href = `../order/historyIndex`
  } else {
    window.location.href = `../order/payOrderPage.html`
  }
}
