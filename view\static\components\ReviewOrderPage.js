const OrderFoodLayout = {
  props: { list: [], source: "history" },
  template: `
    <div class="food-item-layout">  
      <div v-for="(food,idx) in formatFoodList" :key="idx" class="food-item">
        <div class="food-info">
          <div class="food-title">{{inListTitle(food)}}</div>
          <div class="food-sub-item">
            <p v-for="subFood in food.newOrderItemFoodList" :key="subFood.fCode">
              {{inListTitle(subFood)}}{{showXiPrice(subFood.price||subFood.upa1)}}
            </p>
          </div>
        </div>
        <div class="food-price-qty">
          <div class="food-price">{{calcPrice(food,idx)}}</div>
          <div class="food-qty">x{{food.qty1}}</div>
        </div>
      </div>
    </div>

  `,
  computed: {
    formatFoodList() {
      return this.list.map(it => {
        return {
          ...it,
          newOrderItemFoodList: this.formatted(it)
        }
      })
    },
    isCart() {
      return this.source === "cart"
    },
    currencyWay() {
      return app.currencyWay || "$"
    }
  },
  methods: {
    formatted(foodItem) {
      app = app || this.$parent.$root
      return app.flatAllXi(foodItem)
    },
    inListTitle(...arg) {
      app = app || this.$parent.$root
      return app.inListTitle(...arg)
    },
    // 细项价钱显示
    showXiPrice(price = 0) {
      app = app || this.$parent.$root
      if (!price) return ""
      return `(${app.showXiPrice(price)})`
    },
    showFoodPrice(item) {
      let oneFoodPrice = this.calculatedTotalOne(item)
      if (oneFoodPrice > 0) {
        return `${this.currencyWay}${retainSignificantDecimals(oneFoodPrice)}`
      } else {
        let absPrice = Math.abs(oneFoodPrice)
        return `-${this.currencyWay}${retainSignificantDecimals(absPrice)}`
      }
    },
    myXiPriceArry(e) {
      let arry = []
      if (e.newOrderItemFoodList && e.newOrderItemFoodList.length !== 0) {
        let res = this.myXiPriceArry(e.newOrderItemFoodList)
        arry.push(...e.newOrderItemFoodList, ...res)
      }
      if (e.newOrderItemMListList && e.newOrderItemMListList.length !== 0) {
        let res = this.myXiPriceArry(e.newOrderItemMListList)
        arry.push(...e.newOrderItemMListList, ...res)
      }
      return arry
    },
    calculatedTotalOne(item) {
      let baseFoodPrice = floatMultiply(item.upa1 || 0, item.qty1)
      let arry = [],
        baseXiPrice = 0
      let historyNewFoodList = item.newOrderItemFoodList
      let historyNewMList = item.newOrderItemMListList
      if (historyNewFoodList && historyNewFoodList.length !== 0) {
        historyNewFoodList.forEach(e => {
          let myXiarry = this.myXiPriceArry(e)
          if (myXiarry.length !== 0) arry.push(...myXiarry)
          arry.push(e)
        })
      }
      if (historyNewMList && historyNewMList.length !== 0) {
        historyNewMList.forEach(e => {
          let myXiarry = this.myXiPriceArry(e)
          if (myXiarry.length !== 0) arry.push(...myXiarry)
          arry.push(e)
        })
      }
      // 兼容pos下单金钱计算
      arry.forEach(priceItem => {
        let qty1 = priceItem.qty1 || 1
        let price = 0
        if (priceItem.upa1 && priceItem.upa1 != 0) {
          price = priceItem.upa1
        } else if (priceItem.price && priceItem.price != 0) {
          price = priceItem.price
        }
        baseXiPrice = floatAdd(baseXiPrice, floatMultiply(price, qty1))
      })
      return floatAdd(baseFoodPrice, baseXiPrice)
    },
    calcPrice(data, index) {
      const price = this.calculatedTotalOne(this.list[index])
      if (!price) return ""
      if (this.isCart) {
        app = app || this.$parent.$root
        return app.showCardPrice(this.list[index])
      }
      return this.showFoodPrice(this.list[index])
    }
  }
}

const ReviewOrderPage = {
  template: `
    <div id="review-order-view" v-if="!!Object.keys(orders).length">
       <div class="main">
          <div class="card">
            <div class="past-orders">
              <div class="card-title">{{systemLanguage.historicalLan}}</div>
              <template v-for="(list,time) in orders" >
                <div class="create-order-time">
                  <i></i>
                  <p>{{time}}</p>
                </div>
                <order-food-layout :list="list"></order-food-layout>
              </template>
            </div>
          </div>
       </div>
    </div>
  `,

  props: {
    orders: [],
    openTable: JSON.parse(sessionStorage.getItem("openTable")) || {},
    systemLanguage: {}
  },
  components: { navHeaderTable, OrderFoodLayout },
  mounted() {},
  computed: {
    allPrice() {
      app = app || this.$parent.$root
      return app.discountGroup.length || app.taxesGroup.length
        ? app.discountedPrice
        : app.allPriceCache
    },
    moneySymbol() {
      return this.openTable.currencyWay
    }
  },
  methods: {
    confirmOrder() {
      app = app || this.$parent.$root
      app.onSubOrderBtn()
    },

    handleError() {}
  }
}
