<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <style>
      [v-cloak] {
        display: none;
      }
    </style>
    <!-- rem布局 -->
    <script src="../static/js/page/lib-flexible.js"></script>
    <!-- 懒加载 -->
    <script src="../static/js/page/lazy.js"></script>
    <link rel="stylesheet" type="text/css" href="../static/css/page/menuPage.css" />
    <!-- I18n国际化 -->
    <script src="../static/I18n/initI18n.js"></script>
    <script src="../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../static/vue/vue.min.js"></script>
    <script src="../static/moment/moment.js"></script>
    <!-- 预加载 -->
    <link rel="preload" href="../static/layui/css/modules/layer/default/icon.png" as="image" />

    <!-- 语言下拉菜单 -->
    <link
      href="../static/lanSelect/jquery.sweet-dropdown.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <script src="../static/lanSelect/jquery.sweet-dropdown.min.js"></script>
    <!-- vconsole 真机测试 -->
    <!-- <script src="https://cdn.bootcss.com/vConsole/3.3.4/vconsole.min.js"></script> -->
    <!-- 引入 layui.css/layui.js -->
    <link href="../static/layui/css/layui.css" rel="stylesheet" type="text/css" />
    <script src="../static/layui/layui.js"></script>
    <!-- 引入封装js -->
    <script src="../static/byod_webUtils/public.js"></script>

    <script src="../static/js/index/useOpenTableInterface.js"></script>

    <!-- 防抖节流 -->
    <script src="../static/js/debounce.js"></script>
    <!--<script src="../static/js/lodash.js"></script> -->
    <!--工具函数 -->
    <script src="../static/byod_webUtils/menu.js"></script>
    <!-- 图片预览 -->
    <link rel="stylesheet" href="../static/JQviewer/viewer.css" />
    <script src="../static/JQviewer/viewer.js"></script>
    <script src="../static/JQviewer/jquery-viewer.js"></script>
    <!-- math.js 数学计算属性 -->
    <script src="../static/js/page/math.js"></script>
    <!-- 支付依赖 -->
    <!-- <script
      src="https://sandbox-library-checkout.spiralplatform.com/js/v2/spiralpg.min.js"
      async
    ></script> -->
    <!-- 组件 -->
    <script src="../static/components/bootomNav.js"></script>
    <script src="../static/components/navHeaderTable.js"></script>
    <script src="../static/components/TakeAwayStoreInfo.js"></script>
    <!-- <script src="../static/components/navHeaderTableu.js"></script> -->
    <!-- js-cookie -->

    <script src="../static/js-cookie/index.js"></script>

    <!--时间选择器  -->
    <script src="../static/js/map/delivery-time-picker.js"></script>
    <link rel="stylesheet" href="../static/css/map/delivery-time-picker.css" />
    <!-- PadModel平板模式 -->
    <!--  <link rel="stylesheet" href="../static/css/page/menuPage-PadModel.css">-->
    <!-- version 版本      -->
    <script src="../static/js/versionTag.js"></script>
    <!--   use input 组件  input (包含错误提示)   -->
    <script src="../static/js/useInput.js"></script>
    <!--   vuetify ui -->
    <link rel="stylesheet" href="../static/vuetify/vuetify.min.css" />
    <script src="../static/vuetify/vuetify.min.js"></script>
    <!--     SSE相关mixins:售罄/购物车同步 -->
    <script src="../static/js/page/SSEControlMixin.js"></script>
    <!--  送单页面 form 表单 mixins  -->
    <script src="../static/js/page/SendOrderFormMixins.js"></script>
    <!-- 账号相关 mixins -->
    <script src="../static/js/page/accountMixin.js"></script>
    <script src="../static/js/page/registerAndResetMixin.js"></script>
    <script src="../static/js/page/globalStoreMixin.js"></script>
    <!--  vuetify snackBar mixins  -->
    <script src="../static/vuetify/SnackBar.js"></script>
    <!-- 購物車 底部 總價格   -->
    <script src="../static/js/page/TotalAmount.js"></script>
    <link rel="stylesheet" href="../static/vendor/animate.min.css" />
    <script src="../static/js/index/utils.js"></script>
    <script src="../static/QRcode/qrcode.min.js"></script>

    <link rel="stylesheet" href="../static/css/bottomSheet.css" />
    <link rel="stylesheet" href="../static/css/couponPage.css" />
  </head>

  <body>
    <script src="../static/js/index/device.js"></script>
    <div id="app" :class="{'fixed': fixed}" v-cloak>
      <!-- loading蒙层 -->
      <template>
        <div class="loader loader-3" v-show="loading">
          <div class="dot dot1"></div>
          <div class="dot dot2"></div>
          <div class="dot dot3"></div>
        </div>
      </template>
      <!--       <div class='container_border'> -->
      <!--         &lt;!&ndash; 标识 区别版本标识   &ndash;&gt; -->
      <!--         <div class='version_diff_warp' v-if='openTable.versionNumber'> -->
      <!--           {{versionTag}} -->
      <!--         </div> -->
      <!--       </div> -->
      <version-tag></version-tag>
      <!-- FoodCourt模式选择店铺列表  -->
      <template>
        <transition
          :name="hasProperty('animation','page_transition')?'slide':''"
          v-if="openTable.foodCourt"
        >
          <section class="food_court" v-show="showFoodCourtView" v-cloak ref="food_court">
            <div class="mask_layer" v-if="reOpenTLoading"></div>
            <div class="header_warp" key="foodCourt">
              <div class="table_info">
                <img
                  src="../static/img/newImage/home.jpg"
                  class="homeIcon"
                  @click="goIndex"
                  v-if="isShowGoHomeBtn"
                />

                <!-- 头部右边icon -->
                <div class="table_info_leftIcon">
                  <img
                    src="../static/img/newImage/fontSizeIcon.jpg"
                    class="navHeaderIcon"
                    data-dropdown="#dropdown-fontSize-foodCourt"
                    v-if="fontSizeOption.length>0"
                  />
                  <!-- 尺寸下拉框 -->
                  <div
                    class="dropdown-menu dropdown-anchor-top-right dropdown-has-anchor menuSelect"
                    id="dropdown-fontSize-foodCourt"
                  >
                    <ul
                      class="selectUl"
                      style="font-size: 0.35rem; border-radius: 0.1rem; padding: 0.1rem 0"
                    >
                      <li
                        v-for="(fsItem,fsIndex) in fontSizeOption"
                        :key="fsIndex"
                        @click="setTextZoom(fsItem)"
                      >
                        <a href="#" class="changeTxt" style="line-height: 0.5rem">
                          {{showFontSizeOption(fsItem)}}
                        </a>
                      </li>
                    </ul>
                  </div>
                  <!-- 新版切换语言Icon -->
                  <img
                    src="../static/img/newImage/indexSwitchLan.jpg"
                    alt=""
                    class="navHeaderIcon"
                    data-dropdown="#dropdown-standard-foodcourt"
                    data-add-anchor-x="5"
                    v-if="openTable.allowSwitchLanguage"
                  />
                  <!-- 语言下拉框 -->
                  <div
                    class="dropdown-menu dropdown-anchor-top-right dropdown-has-anchor menuSelect"
                    id="dropdown-standard-foodcourt"
                  >
                    <ul
                      class="selectUl"
                      style="font-size: 0.35rem; border-radius: 0.1rem; padding: 0.1rem 0"
                    >
                      <li
                        v-for="item in lanSele"
                        :key="item.value"
                        @click="onLanSelect(item.value)"
                      >
                        <a href="#" class="changeTxt" style="line-height: 0.5rem">
                          {{languageLabel(item.label)}}
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div class="food_court_store">
              <ul :class="['store_list',foodCourtColumnClassName]">
                <template v-if="foodCourtColumnClassName==='one_column'">
                  <li
                    class="store_item"
                    v-for="(store,i) in courtStoreList"
                    :key="store.storeNumber"
                    @click="onSelectStoreInCourt(store)"
                  >
                    <div class="store_item_header">
                      <img
                        src="../static/img/page/shop-store-black.jpg"
                        class="store_min_logo"
                        alt="store_min_logo"
                        width="20"
                        height="20"
                      />

                      <strong class="store_name">{{store.storeName[openTable.language]}}</strong>
                      <div class="store_cart_info">
                        <img
                          src="../static/img/page/shop-cart-black.jpg"
                          alt="store_cart_icon"
                          width="20"
                          height="20"
                        />
                        <div class="store_cart_count">{{getStoreFoodCount(store)}}</div>
                      </div>
                    </div>

                    <div class="hr gradient"></div>

                    <div class="store_item_details">
                      <img
                        :src="store.storeLogoUrl"
                        class="store_item_logo"
                        onerror="this.src='../static/img/newImage/timg.jpg';this.onerror=null"
                      />
                      <div class="img_placeholder store_item_logo"></div>
                      <div class="store_item_desc">{{store.storeDesc[openTable.language]}}</div>
                      <!--                       <button class="into_store__btn" @click="onSelectStoreInCourt(store)"> -->
                      <!--                         <img -->
                      <!--                           width="100%" -->
                      <!--                           src="../static/img/page/arrow-right-circle.jpg" -->
                      <!--                           alt="into_store_icon" -->
                      <!--                         /> -->
                      <!--                       </button> -->
                    </div>
                  </li>
                </template>
                <!--                 二三栏 -->
                <template v-else>
                  <li
                    class="store_item"
                    v-for="(store,i) in courtStoreList"
                    :key="store.storeNumber"
                    @click="onSelectStoreInCourt(store)"
                  >
                    <div class="store_item_logo_wrap">
                      <img
                        :src="store.storeLogoUrl||'../static/img/newImage/timg.jpg'"
                        class="store_item_logo"
                        onerror="this.src='../static/img/newImage/timg.jpg';this.onerror=null"
                      />
                    </div>
                    <div class="store_item_header">
                      <img
                        src="../static/img/page/shop-store-black.jpg"
                        class="store_min_logo"
                        alt="store_min_logo"
                      />
                      <strong class="store_name">{{store.storeName[openTable.language]}}</strong>
                    </div>

                    <div class="store_cart_info">
                      <img src="../static/img/page/shop-cart-black.jpg" alt="store_cart_icon" />
                      <div class="store_cart_count">{{getStoreFoodCount(store)}}</div>
                    </div>
                    <template v-if="foodCourtColumnClassName==='two_column'">
                      <div class="hr gradient" style="margin: 5px auto; width: 88%"></div>
                      <div class="store_item_desc">{{store.storeDesc[openTable.language]}}</div>
                    </template>
                  </li>
                </template>
              </ul>
            </div>
            <!-- fty页底部 -->
            <footer-Nav
              :open-Table="openTable"
              :system-Language="systemLanguage"
              :allshopl-Number="allshoplNumber"
            ></footer-Nav>
          </section>
        </transition>
      </template>
      <!-- 主fty页 -->
      <template>
        <transition
          :name="hasProperty('animation','page_transition')?'slide-left':''"
          v-if="openTable.ftyPage"
        >
          <div class="app_ftyPage" v-show="ftSwitch">
            <!-- 重构再复用头部 -->
            <div class="header_warp">
              <div class="table_info ftyHeader">
                <img
                  src="../static/img/newImage/home.jpg"
                  class="homeIcon"
                  @click="goIndex"
                  v-if="isShowGoHomeBtn"
                />
                <!-- 个人中心 -->
                <div class="user-icon-content" @click="showUserPopup" v-if="showCRM">
                  <img src="../static/img/newImage/userIcon.jpg" class="navHeaderIcon user-icon" />
                  <span>{{systemLanguage.memberText}}</span>
                </div>

                <img
                  src="../static/img/page/store-icon.jpg"
                  class="food-court-icon"
                  @click="toggleCourtListDisplay(true)"
                  v-if="isFoodCourtMode"
                />
                <nav-header-table
                  :open-Table="openTable"
                  :system-Language="systemLanguage"
                ></nav-header-table>
                <!-- 头部右边icon -->
                <div class="table_info_leftIcon">
                  <!-- 搜索ICon -->
                  <img
                    src="../static/img/newImage/searchIcon.jpg"
                    class="navHeaderIcon"
                    @click="showSearchPopup"
                  />
                  <img
                    src="../static/img/newImage/fontSizeIcon.jpg"
                    class="navHeaderIcon"
                    data-dropdown="#dropdown-fontSize"
                    v-if="fontSizeOption.length>0"
                  />
                  <!-- 尺寸下拉框 -->
                  <div
                    class="dropdown-menu dropdown-anchor-top-right dropdown-has-anchor menuSelect"
                    id="dropdown-fontSize"
                  >
                    <ul
                      class="selectUl"
                      style="font-size: 0.35rem; border-radius: 0.1rem; padding: 0.1rem 0"
                    >
                      <li
                        v-for="(fsItem,fsIndex) in fontSizeOption"
                        :key="fsIndex"
                        @click="setTextZoom(fsItem)"
                      >
                        <a href="#" class="changeTxt" style="line-height: 0.5rem">
                          {{showFontSizeOption(fsItem)}}
                        </a>
                      </li>
                    </ul>
                  </div>

                  <!-- 新版切换语言Icon -->
                  <img
                    src="../static/img/newImage/indexSwitchLan.jpg"
                    alt=""
                    class="navHeaderIcon"
                    data-dropdown="#dropdown-standard"
                    data-add-anchor-x="5"
                    v-if="openTable.allowSwitchLanguage"
                  />
                  <!-- 语言下拉框 -->
                  <div
                    class="dropdown-menu dropdown-anchor-top-right dropdown-has-anchor menuSelect"
                    id="dropdown-standard"
                  >
                    <ul
                      class="selectUl"
                      style="font-size: 0.35rem; border-radius: 0.1rem; padding: 0.1rem 0"
                    >
                      <li
                        v-for="item in lanSele"
                        :key="item.value"
                        @click="onLanSelect(item.value)"
                      >
                        <a href="#" class="changeTxt" style="line-height: 0.5rem">
                          {{languageLabel(item.label)}}
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <!-- 外卖店铺信息/预约时间 -->
            <take-away-store-info
              v-if="isTakeAway"
              :system-language="systemLanguage"
            ></take-away-store-info>
            <meal-order-timer-tips
              style="padding-top: 0.1rem"
              v-if="showOrderCountdown"
              :order-start-time="orderStartTime"
              :start-time-tips="systemLanguage.orderStartTimeTips"
              :countdown-tips="systemLanguage.countdownTips"
              :deadline-tips="systemLanguage.beforeCountdownTips"
              :timeout-tips="systemLanguage.MDOrderStartTimeout"
              :deadline="orderDeadline"
              :status="orderTimerStatus"
              bg-color="#f7f8fa"
            ></meal-order-timer-tips>
            <!-- 下单等待时间提示 -->
            <order-wait-tips
              v-if="queuedObj.isOrderWait"
              :system-language="systemLanguage"
              :open-Table="openTable"
              :order-wait-obj="queuedObj.orderWaitObj"
              bg-color="#f7f8fa"
            ></order-wait-tips>
            <!-- fty容器 -->
            <div class="app_fty_container">
              <div
                class="app_fty_container_cell"
                v-for="(item, index) in allDataList"
                :key="index"
                @click="onfty(index,item)"
                v-show="!hideMerge(item)&&removeNullMerge(item)&&displayMemberFtype(item)"
                v-if="hasFoodList(item)||item.combine_with_foodType"
                :code="item.code"
              >
                <div class="adaptivity_ftyImg_warp" v-show="item.showFtyimg">
                  <img
                    class="ftyImg"
                    :key="item.name"
                    v-lazy="jointImgUrl(item,'fty')"
                    alt=""
                    @error="errorFtyImgCallback(item)"
                  />
                </div>
                <div class="app_fty_container_cell_top" v-show="!item.showFtyimg">
                  <div class="app_fty_container_cell_top_Box">
                    <p class="fty_top_txt">{{ item.fType_nameA || item.name }}</p>
                    <p>{{ item.fType_nameB || item.name2 }}</p>
                  </div>
                </div>

                <!-- fty名字 -->
                <div class="app_fty_container_cell_bottom">
                  <div class="fty_bottom_txt">{{outListTitle(item)}}</div>
                </div>
              </div>
              <div class="errorDataBox" v-if="showErrorData">
                <img src="../static/img/newImage/empty-error.jpg" class="errorBoxICon" />
                <p>{{systemLanguage.emptyData}}</p>
              </div>
            </div>
            <!-- 弹出的分类foodtype -->
            <div class="merge_content">
              <div class="merge_box">
                <div
                  class="fty_item"
                  v-for="item in getMerge_data(fty_data)"
                  :key="item.code"
                  v-if="hasFoodList(item)"
                  @click="onfty_merge(item)"
                >
                  <div class="disableBox" v-if="item.isExpired" @click.stop="showOutTimeTips"></div>
                  <div class="adaptivity_ftyImg_warp" v-show="item.showFtyimg">
                    <img
                      class="ftyImg"
                      :key="item.name"
                      @error="errorFtyImgCallback(item)"
                      v-lazy="jointImgUrl(item,'fty') "
                    />
                  </div>
                  <!-- fty上半内容 -->
                  <div class="app_fty_container_cell_top_1" v-show="!item.showFtyimg">
                    <div class="app_fty_container_cell_top_Box_1">
                      <p class="fty_top_txt">{{ item.fType_nameA || item.name }}</p>
                      <p>{{ item.fType_nameB || item.name2 }}</p>
                    </div>
                  </div>
                  <!-- fty名字 -->
                  <div class="app_fty_container_cell_bottom">
                    <div class="fty_bottom_txt_1">{{outListTitle(item)}}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- fty页底部 -->
            <footer-Nav
              :open-Table="openTable"
              :system-Language="systemLanguage"
              :allshopl-Number="allshoplNumber"
            ></footer-Nav>
          </div>
        </transition>
        <!-- 主点餐页 -->
        <transition :name="hasProperty('animation','page_transition')?'slide-right':''">
          <div class="app_container" v-show="!ftSwitch">
            <div
              class="header_warp"
              @touchstart="headerTouchStart"
              @touchmove="headerTouchMove"
              @touchend="headerTouchEnd"
            >
              <div class="table_info foodHeader">
                <img
                  src="../static/img/page/black.jpg"
                  class="backFtyIcon"
                  @click="backFtyBtn"
                  v-if="openTable.ftyPage"
                />
                <img
                  src="../static/img/page/store-icon.jpg"
                  class="back-store-list-icon"
                  @click="toggleCourtListDisplay(true)"
                  v-else-if="isFoodCourtMode"
                />
                <img
                  src="../static/img/newImage/home.jpg"
                  class="homeIcon"
                  @click="goIndex"
                  v-if="isShowGoHomeBtn&&!openTable.ftyPage"
                />

                <!-- 个人中心 -->
                <div class="user-icon-content" @click="showUserPopup" v-if="showCRM">
                  <img src="../static/img/newImage/userIcon.jpg" class="navHeaderIcon user-icon" />
                  <span>{{systemLanguage.memberText}}</span>
                </div>
                <nav-header-table
                  :open-Table="openTable"
                  :system-Language="systemLanguage"
                ></nav-header-table>

                <!-- 头部右边icon -->
                <div class="table_info_leftIcon">
                  <!-- 搜索ICon -->
                  <img
                    src="../static/img/newImage/searchIcon.jpg"
                    class="navHeaderIcon"
                    @click="showSearchPopup"
                  />
                  <!-- 字体大小控制 -->
                  <img
                    src="../static/img/newImage/fontSizeIcon.jpg"
                    class="navHeaderIcon"
                    data-dropdown="#dropdownFontSize"
                    v-if="fontSizeOption.length>0"
                  />
                  <!-- 尺寸下拉框 -->
                  <div
                    class="dropdown-menu dropdown-anchor-top-right dropdown-has-anchor menuSelect"
                    id="dropdownFontSize"
                  >
                    <ul
                      class="selectUl"
                      style="font-size: 0.35rem; border-radius: 0.1rem; padding: 0.1rem 0"
                    >
                      <li
                        v-for="(fsItem,fsIndex) in fontSizeOption"
                        :key="fsIndex"
                        @click="setTextZoom(fsItem)"
                      >
                        <a href="#" class="changeTxt" style="line-height: 0.5rem">
                          {{showFontSizeOption(fsItem)}}
                        </a>
                      </li>
                    </ul>
                  </div>
                  <!-- 新版切换语言Icon -->
                  <img
                    src="../static/img/newImage/indexSwitchLan.jpg"
                    alt=""
                    class="navHeaderIcon"
                    data-dropdown="#dropdownStandard"
                    data-add-anchor-x="5"
                    v-if="openTable.allowSwitchLanguage"
                  />
                  <!-- 语言下拉框 -->
                  <div
                    class="dropdown-menu dropdown-anchor-top-right dropdown-has-anchor menuSelect"
                    id="dropdownStandard"
                  >
                    <ul
                      class="selectUl"
                      style="font-size: 0.35rem; border-radius: 0.1rem; padding: 0.1rem 0"
                    >
                      <li
                        v-for="item in lanSele"
                        :key="item.value"
                        @click="onLanSelect(item.value)"
                      >
                        <a href="#" class="changeTxt" style="line-height: 0.5rem">
                          {{languageLabel(item.label)}}
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div class="main_warp">
              <template v-if="!openTable.verticalOrderLayout">
                <custom-tabs
                  :combined-tabs="!!openTable.combinedNavigation"
                  :tabs-config="openTable.combinedNavigation"
                  :tabs-active="tabIsActive"
                  :tabs-list="tabDataList"
                  :merge-map="merge_fty"
                  :remove-item-fn="hasFoodList"
                  :hide-item-fn="displayMemberFtype"
                  :format-name="outListTitle"
                  :language="{other:systemLanguage.other}"
                  @on-tab="onTab"
                  v-show="!!allDataList.length"
                  ref="custom-tabs"
                ></custom-tabs>
                <!-- 箭头 -->
                <img
                  src="../static/img/newImage/rightPrompt.jpg"
                  alt=""
                  class="toRight_img"
                  v-if="!loading && openTable.showArrow"
                />
                <img
                  src="../static/img/newImage/leftPrompt.jpg"
                  alt=""
                  class="toLeft_img"
                  v-if="!loading && openTable.showArrow"
                />
                <meal-order-timer-tips
                  v-if="showOrderCountdown"
                  :order-start-time="orderStartTime"
                  :start-time-tips="systemLanguage.orderStartTimeTips"
                  :countdown-tips="systemLanguage.countdownTips"
                  :deadline-tips="systemLanguage.beforeCountdownTips"
                  :timeout-tips="systemLanguage.MDOrderStartTimeout"
                  :deadline="orderDeadline"
                  :status="orderTimerStatus"
                  bg-color="#f7f8fa"
                ></meal-order-timer-tips>
                <!-- 下单等待时间提示 -->
                <order-wait-tips
                  v-if="queuedObj.isOrderWait"
                  :system-language="systemLanguage"
                  :open-Table="openTable"
                  :order-wait-obj="queuedObj.orderWaitObj"
                  bg-color="#f7f8fa"
                ></order-wait-tips>
                <!-- 中间内容区 -->
                <div
                  :class="[openTable.infiniteLoop?'infLoop':'noInfLoop','food-container']"
                  ref="contentScrollContainer"
                  @touchstart="contentTouchStart"
                  @touchmove="contentTouchMove"
                  @touchend="contentTouchEnd"
                >
                  <template v-if="openTable.infiniteLoop">
                    <template v-for="(ftyItem, i) in tabDataList" :key="ftyItem.code">
                      <div
                        :id="'tab'+i"
                        :data-code="ftyItem.code"
                        :class="['foodCard']"
                        v-if="hasFoodList(ftyItem)&&displayMemberFtype(ftyItem)"
                      >
                        <div class="foodCard-banner" v-if="ftyItem.bannerImgUrl">
                          <img
                            :key="ftyItem.name"
                            v-lazy="ftyItem.bannerImgUrl"
                            @error="errorFtyImgCallback(ftyItem,'banner')"
                          />
                        </div>
                        <div class="foodCard-title" v-else>
                          <div class="dot"></div>
                          <span>{{outListTitle(ftyItem)}}</span>
                          <div class="dot"></div>
                        </div>
                        <food-component
                          ref="food-component"
                          :food-list="ftyItem.foodList"
                          :content-warp-style="contentWarpStyle"
                          :click-layout="ftyItem.display_column"
                          :is-action-disabled="isActionDisabled"
                        ></food-component>
                      </div>
                    </template>
                  </template>
                  <template v-else>
                    <food-component
                      ref="food-component"
                      :food-list="foodList"
                      :content-warp-style="contentWarpStyle"
                      :click-layout="clickLayout"
                      :is-action-disabled="isActionDisabled"
                    ></food-component>
                  </template>
                </div>
              </template>
              <template v-else>
                <meal-order-timer-tips
                  v-if="showOrderCountdown"
                  :order-start-time="orderStartTime"
                  :start-time-tips="systemLanguage.orderStartTimeTips"
                  :countdown-tips="systemLanguage.countdownTips"
                  :deadline-tips="systemLanguage.beforeCountdownTips"
                  :timeout-tips="systemLanguage.MDOrderStartTimeout"
                  :deadline="orderDeadline"
                  :status="orderTimerStatus"
                  bg-color="#fff"
                ></meal-order-timer-tips>
                <!-- 下单等待时间提示 -->
                <order-wait-tips
                  v-if="queuedObj.isOrderWait"
                  :system-language="systemLanguage"
                  :open-Table="openTable"
                  :order-wait-obj="queuedObj.orderWaitObj"
                  bg-color="#fff"
                ></order-wait-tips>
                <vertical-order-layout
                  ref="verticalOrderLayout"
                  :tab-is-active="tabIsActive"
                  :tab-data-list="tabDataList"
                  :in-list-title="inListTitle"
                  :out-list-title="outListTitle"
                  :has-food-list="hasFoodList"
                  :display-member-ftype="displayMemberFtype"
                  :show-outer-price="showOuterPrice"
                  :show-add-cart-btn="showAddCartBtn"
                  :img-tag-url="imgTagUrl"
                  :price-name="priceName"
                  :additem="additem"
                  :onfood-info="onfoodInfo"
                ></vertical-order-layout>
              </template>
            </div>
            <!-- 底部导航 -->
            <footer-Nav
              :open-Table="openTable"
              :system-Language="systemLanguage"
              :allshopl-Number="allshoplNumber"
            ></footer-Nav>
            <!-- 空数据 -->
            <div class="errorDataBox" v-if="showErrorData">
              <img src="../static/img/newImage/empty-error.jpg" class="errorBoxICon" />
              <p>{{ systemLanguage.emptyData}}</p>
            </div>
          </div>
        </transition>
      </template>
      <!-- food详细信息 -->
      <template>
        <transition :name="hasProperty('animation','page_transition')?'fade':''">
          <div class="food_info_warp" v-if="showFoodWarp">
            <!-- 头部 -->
            <div class="food_info_warp_header">
              <!-- <div class="food_info_warp_header_black"></div> -->
              <img src="../static/img/page/black.jpg" alt="" class="blackIcon" @click="blackbtn" />

              <!-- 滚动标签 -->
              <div v-if="nameRoll" id="foodName_warp" class="food_info_warp_header_title merquee">
                <div class="merquee-txt">{{inListTitle(foodInfoItem)}}</div>
              </div>
              <!-- 不滚动 -->
              <div v-else class="food_info_warp_header_title">{{inListTitle(foodInfoItem)}}</div>
            </div>
            <!-- 内容区 -->
            <div class="food_info_warp_content">
              <div
                class="rolling-food-fixed-wrap"
                :style="{display:rollingFood?'block':'contents'}"
              >
                <div
                  class="food_info_warp_content_top"
                  :class="{'food-big-pic-content':rollFoodBigPic}"
                >
                  <!-- 图片标识(热门/新品)  98%:2%为阴影占据 -->
                  <div class="food_info_img_Tag">
                    <template v-if="foodInfoItem.imageTagName">
                      <div :class="'img_tag_'+foodInfoItem.imageTagAzimuth">
                        <img
                          v-for="(extraPaths, index) in foodInfoItem.imageTagName.split(',')"
                          :src="imgTagUrl(foodInfoItem,extraPaths)"
                          alt=""
                          class="img_tag"
                          :key="index"
                        />
                      </div>
                    </template>
                    <img
                      :data-original="removeImageResize(foodInfoItem.imglink)"
                      v-lazy="removeImageResize(foodInfoItem.imglink)"
                      class="food_info_warp_img"
                      :style="{width:rollFoodBigPic?'100%':'3rem'}"
                    />
                  </div>
                  <div class="food_info_warp_content_products">
                    <template v-if="openTable.language == 'en'">
                      <pre class="pre_style" v-html="foodInfoItem.prod_textareaA"></pre>
                    </template>
                    <template v-else-if="openTable.language == 'zh'">
                      <pre class="pre_style" v-html="foodInfoItem.prod_textareaB"></pre>
                    </template>
                    <template v-else>
                      <pre class="pre_style" v-html="foodInfoItem.prod_textareaC"></pre>
                    </template>
                  </div>
                </div>

                <!-- 过敏源 -->
                <div
                  class="food_info_warp_allergen"
                  v-if="
                    foodInfoItem.allergen_icons &&
                    foodInfoItem.allergen_icons.length !== 0
                  "
                >
                  <div class="food_info_warp_allergen_title">{{ systemLanguage.allergenText }}</div>
                  <img
                    v-for="(e, index) in foodInfoItem.allergen_icons"
                    :key="index"
                    :src="configureImgUrl[e]"
                    alt=""
                    class="food_info_warp_allergen_Icon"
                  />
                </div>
              </div>

              <div
                ref="rolling-food"
                class="rolling-food-wrap"
                :style="{display:rollingFood?'block':'contents'}"
              >
                <!-- 细项 -->
                <food-detail-fixed-item
                  :system-language="systemLanguage"
                  :food-info-item="foodInfoItem"
                  cell-class="twoColumnList"
                  :level="1"
                  v-if="hasFoodList(foodInfoItem)||hasMListList(foodInfoItem)"
                ></food-detail-fixed-item>
                <!-- 分割线 -->
                <div
                  v-if="(foodInfoItem.foodTypeList&&foodInfoItem.foodTypeList.length != 0)||(foodInfoItem.mTypeList&&foodInfoItem.mTypeList.length != 0)"
                  class="foodDivider"
                ></div>
                <!-- 可选细项 (合并) -->
                <div
                  class="food_info_warp_content_foodtypeInList"
                  v-if="foodInfoItem.allTypeArry&&foodInfoItem.allTypeArry.length != 0"
                >
                  <div
                    :class="['foodInfoItem_cell', {'fold-food-type-item':itemFoldable}]"
                    v-for="(vItem, i) in foodInfoItem.allTypeArry"
                    :key="vItem.code"
                    v-show="displayMemberFtype(vItem)"
                  >
                    <template>
                      <!--checked则默认展开-->
                      <input
                        hidden
                        type="checkbox"
                        checked
                        style="display: none"
                        :id="'one_'+vItem.code"
                      />
                      <label :for="'one_'+vItem.code">
                        <div class="optionalTitle">
                          {{outListTitle(vItem)}}
                          <span
                            v-if="isShowQtyBound(vItem,2)"
                            :class="[selectPro(vItem,vItem.typeName)?'selectCol1':'selectCol2','selectMinNumBox']"
                          >
                            <span class="selectMinNum">
                              {{selectPrompt(vItem.minQty,vItem.maxQty)}}
                            </span>
                          </span>
                          <span v-if="vItem.minQty == 0" class="optionalText">
                            {{systemLanguage.qtyItemOptional}}
                          </span>
                          <div class="expand-icon" v-if="itemFoldable">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                              <path
                                fill="var(--styleColor)"
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2m3.79 9.71l-3.08 3.08c-.39.39-1.02.39-1.42 0l-3.08-3.08c-.39-.39-.39-1.03 0-1.42a.996.996 0 0 1 1.41 0L12 12.67l2.38-2.38a.996.996 0 0 1 1.41 0c.39.39.39 1.03 0 1.42"
                              />
                            </svg>
                          </div>
                        </div>
                        <div class="selected-list" v-if="itemFoldable">
                          <p v-for="title in selectedItemInType(vItem)" :key="title">{{title}}</p>
                        </div>
                      </label>
                      <!-- 进入详情页--foodtypeInList -->
                      <div
                        v-if="vItem.typeName =='ftyItem'"
                        class="ftyItem_warp fold-item-list-warp"
                      >
                        <div class="infoPoints_content" v-if="hasFoodList(vItem)">
                          <div
                            v-for="(item, index) in vItem.foodList"
                            :key="index"
                            :class="[vItem.food_display_column == 2 ? 'twoColumnList': 'oneColumnList', openTable.lModPhoto ? 'lModPhotoStyle' : '']"
                            class="infoPoints_content_cell"
                            v-show="displayMemberFood(item)"
                          >
                            <div class="food_disable_box" v-if="showTimeoutStyle(item,vItem)"></div>
                            <div
                              :class="[item.selected ? 'infoPoints_select': 'infoPoints_noselect',item.itemCtrl?'hideBack':'']"
                              class="default_infoPoints"
                              @click.stop="multiselect(item, 'foodtypeInList',vItem)"
                            >
                              <!-- 售罄itemCtrl -->
                              <img
                                v-if="item.itemCtrl"
                                src="../static/img/newImage/soldOutList.jpg"
                                alt=""
                                class="soldoutList"
                              />
                              <!-- 细项缩略图 -->
                              <img
                                v-if="vItem.food_show_src"
                                :src="jointImgUrl(item,'food')"
                                class="typeListImg"
                                onerror="this.src='../static/img/newImage/timg.jpg';this.onerror=null"
                              />
                              <!-- 我的细项图标 -->
                              <img
                                src="../static/img/newImage/custom.jpg"
                                alt=""
                                class="infoPoints_addicon"
                                @click.stop="onPicker(vItem,item,'foodtypeInList')"
                                v-if="item.listSelect&&item.selected"
                              />
                              <div class="xi_Title">
                                {{inListTitle(item)}}
                                <!-- 细项价钱 -->
                                <template v-if="item[priceName('foodList')]">
                                  {{showXiPrice(item[priceName('foodList')])}}
                                </template>
                              </div>
                              <!-- 细项删除 -->
                              <div
                                v-if="item.selected"
                                class="delXiItem_warp"
                                @click.stop="delXiItem(item,'foodtypeInList',vItem.code)"
                              >
                                <img
                                  src="../static/img/newImage/delXiItem.jpg"
                                  class="delXiItem_icon"
                                />
                              </div>
                              <!-- 细项数量及详情点击 -->
                              <div class="xiNum_warp" v-if="item.selected">{{item.qty1}}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- 进入详情页--mtypeInList -->
                      <div
                        v-if="vItem.typeName =='mtyItem'"
                        class="mtyItem_warp fold-item-list-warp"
                      >
                        <div class="infoPoints_content" v-if="hasMListList(vItem)">
                          <div
                            v-for="(item, index) in vItem.mListList"
                            :key="index"
                            :class="[vItem.mList_display_column == 2 ? 'twoColumnList': 'oneColumnList', openTable.lModPhoto ? 'lModPhotoStyle' : '']"
                            class="infoPoints_content_cell"
                          >
                            <div class="food_disable_box" v-if="showTimeoutStyle(item,vItem)"></div>
                            <div
                              :class="[item.selected ? 'infoPoints_select': 'infoPoints_noselect',item.itemCtrl?'hideBack':'']"
                              class="default_infoPoints"
                              @click.stop="multiselect(item, 'mtypeInList',vItem)"
                            >
                              <!-- 售罄itemCtrl -->
                              <img
                                v-if="item.itemCtrl"
                                src="../static/img/newImage/soldOutList.jpg"
                                alt=""
                                class="soldoutList"
                              />
                              <!-- 细项缩略图 -->
                              <img
                                v-if="vItem.mList_show_src"
                                :src="jointImgUrl(item,'mlist')"
                                class="typeListImg"
                                onerror="this.src='../static/img/newImage/timg.jpg';this.onerror=null"
                              />
                              <!-- 我的细项图标 -->
                              <img
                                src="../static/img/newImage/custom.jpg"
                                alt=""
                                class="infoPoints_addicon"
                                @click.stop="onPicker(vItem,item,'mtypeInList')"
                                v-if="item.listSelect&&item.selected"
                              />
                              <div class="xi_Title">
                                {{inListTitle(item)}}
                                <!-- 细项价钱 -->
                                <template v-if="item[priceName('mList')]">
                                  {{showXiPrice(item[priceName('mList')])}}
                                </template>
                              </div>
                              <!-- 细项删除 -->
                              <div
                                v-if="item.selected"
                                class="delXiItem_warp"
                                @click.stop="delXiItem(item,'mtypeInList',vItem.code)"
                              >
                                <img
                                  src="../static/img/newImage/delXiItem.jpg"
                                  class="delXiItem_icon"
                                />
                              </div>
                              <!-- 细项数量及详情点击 -->
                              <div class="xiNum_warp" v-if="item.selected">{{item.qty1}}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>

                <!-- 购买数量 -->
                <div
                  class="food_info_warp_content_shopNum"
                  v-if="!isLimitAddCartNumToOne(foodInfoItem)"
                >
                  <div class="shopNumTitle">{{ systemLanguage.buyNum }}</div>
                  <div class="cart_food_numBox">
                    <div id="food_info_delBtn" class="cart_del_btn" @click="delfoodInfo($event)">
                      -
                    </div>
                    <div class="cart_food_numVal">{{ foodInfoItem.qty1 }}</div>
                    <div
                      id="food_info_addBtn"
                      class="cart_add_btn"
                      :class="{'btnDisabledStyle':isActionDisabled(foodInfoItem,1)}"
                      @click="addfoodInfo($event)"
                    >
                      +
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 底部 -->
            <div class="food_info_warp_footer">
              <div
                class="joinCartBtn"
                :class="isActionDisabled(foodInfoItem,0,'normal',false,true,'joinCartBtn') ? 'btnDisabledStyle' : {'btnPulse':hasProperty('animation','page_breathe')}"
                @click="joinCartF"
              >
                {{ systemLanguage.addCartText }}
              </div>
            </div>
          </div>
        </transition>
      </template>
      <!-- 购物车页-->
      <template>
        <transition :name="hasProperty('animation','page_transition')?'slide':''">
          <div class="cart_warp" v-if="showCartTab">
            <div class="cart_info_warp_header">
              <!-- 返回按钮 -->
              <img src="../static/img/page/black.jpg" alt="" class="backFtyIcon" @click="onBack" />
              <nav-header-table
                :open-Table="openTable"
                :system-Language="systemLanguage"
              ></nav-header-table>
              <!--           <img src="../static/img/page/discountIcon.jpg" v-if="openTable.promotionDiscount&&openTable.performType===2" -->
              <!--             @click="onDiscountIcon" width="32" height="32" alt="discount_icon" class="discountIcon" /> -->
            </div>
            <!-- 外卖店铺信息/预约时间 -->
            <take-away-store-info
              v-if="isTakeAway"
              :system-language="systemLanguage"
            ></take-away-store-info>
            <!-- 购物车内容 -->
            <div class="cart_food_box">
              <div
                v-if="shopCartList.length==0"
                :class="['cart_food_null',{'compatible-cart':openTable.displayCartOrderRecord}]"
              >
                <img src="../static/img/newImage/empty-order.jpg" class="emptyOrderICon" />
                <p class="cart_food_null_txt">{{systemLanguage.nullFoodText}}</p>
              </div>
              <template v-else>
                <!-- 购物车标题及更改选项按钮 -->
                <div
                  class="your_cart"
                  :style="isTakeAway?'margin: 0.2rem 0rem 0.8rem':'margin: 0.8rem 0'"
                >
                  <span>{{systemLanguage.carPageTitle}}</span>
                  <!--购物车更改选项按钮  -->
                  <button
                    class="edit_xi_btn"
                    :class="{'anima-shakeX':cartEditXi}"
                    @click="cartEditBtn"
                  >
                    {{cartEditXi?systemLanguage.confirmBtn:systemLanguage.cartHeaderEditBtn}}
                  </button>
                  <!-- 清空购物车按钮-->
                  <button
                    class="clear_cart_btn edit_xi_btn"
                    @click="onClearShopCartBtn"
                    v-if="openTable.clearCartButton"
                  >
                    {{systemLanguage.clear}}
                  </button>
                </div>
                <!-- 有自定義區域  -->
                <div class="expand_content">
                  <!--   折扣碼區域顯示  -->
                  <div
                    :class="`discount_content ${additionalDisConfStatus ? 'disabledDiscount' : ''}`"
                    v-if="openTable.promotionDiscount&&openTable.performType===2&&confirmDiscountCode"
                  >
                    <div class="discount_info">
                      <p>{{systemLanguage.discountInfoText}} : {{confirmDiscountCode}}</p>
                      <p v-if="discountCodeDesc[openTable.language]">
                        {{discountCodeDesc[openTable.language]||''}}
                      </p>
                    </div>

                    <div class="discount_use" @click="onRemoveDiscount" v-if="!discountCompulsory">
                      <svg
                        t="1672379998907"
                        class="icon"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="2149"
                        width="26"
                        height="26"
                      >
                        <path
                          d="M159.14585414 860.41572876c-195.29034532-195.29034532-195.29034532-510.41794762 0-705.70829173s510.41794762-195.29034532
                    705.70829172 0 195.29034532 510.41794762 0 705.70829173-510.41794762 193.07113616-705.70829172 0z m588.09024269-530.39082277l-59.91862787-59.91862909-175.31746896 177.53667812-175.31746896-175.31746896-57.69941993 57.69941993 175.31746895 175.31746895-177.53667689 177.53667691 59.91862787 59.9186291 177.53667691-177.53667812 177.53667811 177.53667812 57.69941993-57.69942115-177.5366769-177.5366769 175.31746774-177.53667691z"
                          :fill="additionalDisConfStatus?'#bcbec2':'var(--styleColor)'"
                          p-id="2150"
                        ></path>
                      </svg>
                    </div>
                  </div>
                </div>
                <!-- 购物车主内容 -->
                <div class="cart_food_warp_content">
                  <div
                    class="store_item_wrap"
                    :style="{display:[isFoodCourtMode?'block':'contents']}"
                    v-for="sn in Object.keys(shopCartGroup)"
                    :key="sn"
                  >
                    <div class="store_info" v-if="isFoodCourtMode">
                      <div class="store_logo">
                        <img src="../static/img/page/shop-store-black.jpg" alt="store_logo" />
                      </div>
                      <strong class="store_name" @click="onSelectStoreInCart(sn)">
                        {{getStoreBasedOnSn(sn).storeName[openTable.language]}}
                        <span style="color: #7c7c7c">></span>
                      </strong>
                    </div>
                    <div
                      class="cart_food_cell"
                      :class="{giveAway:item.giveAway}"
                      v-for="(item, index) in [...shopCartGroup[sn],...(isFoodCourtMode?[]:giveAwayFoodList)]"
                      :key="index"
                    >
                      <div
                        class="cart_food_img"
                        :class="{'cart-soldout':item.itemCtrl}"
                        v-if="openTable.thumbnail"
                      >
                        <img
                          :src="item.itemCtrl?'../static/img/newImage/soldOut.jpg':jointImgUrl(item,'food')"
                          alt=""
                          class="cart_food_img_cell"
                          :key="item.fCode"
                          onerror="this.src='../static/img/newImage/timg.jpg';this.onerror=null"
                        />
                      </div>
                      <div class="cart-soldout" v-else-if="item.itemCtrl">
                        {{systemLanguage.soldOut}}
                      </div>
                      <div class="cart_info">
                        <div class="info_left">
                          <!-- food名称 -->
                          <div class="cart_food_title">{{inListTitle(item)}}</div>
                          <!-- 细项 -->
                          <div v-if="openTable.sideDish" class="cart_food_sideDish">
                            <div class="littleitem" v-if="hasNewOrderItemData(item)">
                              <div v-html="showlogic(item,'xi')" style="margin: 0.1rem 0.2rem">
                                {{showlogic(item,'xi')}}
                              </div>
                            </div>
                          </div>

                          <!-- 价格和 -->
                          <div class="cart_food_priceNum" v-if="openTable.price&&!item.giveAway">
                            <div class="cart_food_price">
                              <div v-if="calculatedTotal(item)!=0">
                                <span class="cart_food_price_amount">{{ showCardPrice(item)}}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="info_right" v-if="item.giveAway">
                        <svg
                          t="1661498573398"
                          class="give_away_icon"
                          viewBox="0 0 1024 1024"
                          version="1.1"
                          xmlns="http://www.w3.org/2000/svg"
                          p-id="1772"
                          width="64"
                          height="64"
                        >
                          <path
                            d="M996.981138 381.561447a118.031554 118.031554 0 0 0-117.877787-117.939293h-83.034183c0.968732-0.768835 2.029724-1.691437 2.967703-2.460272 78.62107-73.346862 92.875272-168.682406 31.69138-225.729966A141.588659 141.588659 0 0 0 732.763291 0.065505a233.372186 233.372186 0 0 0-155.304676 64.735909 311.239798 311.239798 0 0 0-65.443238 90.261233 311.378188 311.378188 0 0 0-65.489368-90.261233A233.341432 233.341432 0 0 0 291.221332 0.065505a141.465646 141.465646 0 0 0-97.949583 35.366411c-61.122385 57.047559-46.898937 152.383103 31.722134 225.729966 0.937979 0.768835 1.998971 1.691437 2.967703 2.460272h-83.034184a118.031554 118.031554 0 0 0-117.877787 117.939293v228.651539H95.798844v295.84772a117.970047 117.970047 0 0 0 117.831657 117.785527h595.93941a117.985424
                     117.985424 0 0 0 117.86241-117.785527V610.212986h69.518064V381.561447zM638.62713 139.378412a126.288842 126.288842 0 0 1 83.034183-35.520178 35.212644 35.212644 0 0 1 25.279296 11.993827 27.770321 27.770321 0 0 1 8.426432 22.296215c0 16.45307-8.841603 32.291071-33.736481 55.663657a194.991941 194.991941 0 0 1-116.862925 49.666743 66.627244 66.627244 0 0 1-16.207043-1.845204c-6.611981-13.685264 7.980508-62.890706 50.066538-102.25506z m-367.349378-26.601692a46.591403 46.591403 0 0 1 31.091688-9.841088 118.185321 118.185321 0 0 1 76.883503 33.213673c42.239797 39.364354 56.724649 89.184864 50.743113 100.871157a36.765691 36.765691 0 0 1-17.221905 2.767806 193.746428 193.746428 0 0 1-116.340117-49.512976c-24.787241-23.065051-33.628844-39.05682-33.628844-55.202356a27.247514 27.247514 0 0 1
                     8.472562-22.296216zM903.383161 379.562476v137.16017H558.822053V355.267289h320.281298A24.32594 24.32594 0 0 1 903.383161 379.562476z m-784.719163 0a24.32594 24.32594 0 0 1 24.27981-24.295187h322.295645v161.455357H118.694751v-137.16017z m70.732823 526.49823V610.212986h275.827256V930.355893h-251.562823a24.310564 24.310564 0 0 1-24.23368-24.295187z m644.468277 0A24.32594 24.32594 0 0 1 809.585287 930.355893H558.822053V610.212986H833.880474v295.84772z"
                            fill="var(--styleColor)"
                            p-id="1773"
                          ></path>
                        </svg>
                      </div>
                      <!--加数量  -->
                      <div class="info_right" v-else>
                        <!-- 购物车单个food数量 -->
                        <div class="cart_food_numBox">
                          <button
                            class="cart_remove_btn"
                            @click="removeCarFood(item)"
                            v-if="item.itemCtrl"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 24 24"
                            >
                              <path
                                fill="#fff"
                                d="M9 3v1H4v2h1v13a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V6h1V4h-5V3zM7 6h10v13H7zm2 2v9h2V8zm4 0v9h2V8z"
                              />
                            </svg>
                          </button>
                          <button
                            class="cart_del_btn"
                            @click="delCarFood($event,item, index)"
                            v-else
                          >
                            -
                          </button>
                          <div class="cart_food_numVal">{{ item.qty1 }}</div>
                          <button
                            class="cart_add_btn"
                            :class="{'btnDisabledStyle':isActionDisabled(item,1,'cart',false,false)}"
                            @click="addCarFood($event,item,index)"
                            :disabled="item.itemCtrl"
                          >
                            +
                          </button>
                        </div>
                        <!-- 修改单个food细项 -->
                        <div
                          class="edit_xiFood_item_btn"
                          v-show="cartEditXi"
                          v-if="!item.itemCtrl"
                          @click="editCarFood(item,index)"
                        >
                          {{systemLanguage.cartItemEditBtn}}
                        </div>
                      </div>
                    </div>
                  </div>
                  <!--  商家自动给外卖餐具  -->
                  <hr
                    role="separator"
                    aria-orientation="horizontal"
                    class="pl-3 pr-3 d-block v-divider theme--light"
                    v-if="additionalItemsForTakeawayAutoList.length||additionalItemsForTakeawayFixedList.length"
                  />
                  <div
                    class="takeaway-cutlery-delivery-content"
                    v-if="isTakeAway&&(additionalItemsForTakeawayAutoList.length||additionalItemsForTakeawayFixedList.length)"
                  >
                    <div>
                      <div
                        class="auto-cutlery-item"
                        v-for="item in  allAdditionalItemsForTakeawayList"
                        :key="item.fCode"
                      >
                        <div class="cart_info">
                          <div class="info_left">
                            <label>
                              <input
                                type="checkbox"
                                @click.stop="onSelectAdditionalItems(item)"
                                v-model="selectedAdditionalItemsForTakeawayMap[item.fixed?'FIXED':'AUTO'][item.fCode]"
                              />
                              <span class="auto-cutlery-title">{{inListTitle(item)}}</span>
                              <span v-if="item.upa1&&!item.fixed">
                                ({{currencyWay+calculatedTotal(item)}})
                              </span>
                            </label>
                          </div>
                          <template v-if="item.fixed&&item.qty1">
                            <!-- 价格和 -->
                            <div class="cart_food_priceNum" v-if="openTable.price">
                              <div class="cart_food_price">
                                <div v-if="calculatedTotal(item)!=0">
                                  <span class="cart_food_price_amount">
                                    {{ showCardPrice(item)}}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <!--加数量  -->
                            <div class="info_right">
                              <div class="cart_food_numBox">
                                <button
                                  class="cart_del_btn"
                                  :disabled="item.qty1===0"
                                  @click="changeTablewareQty(item, false)"
                                >
                                  -
                                </button>
                                <div class="cart_food_numVal">{{ item.qty1 }}</div>
                                <button class="cart_add_btn" @click="changeTablewareQty(item)">
                                  +
                                </button>
                              </div>
                            </div>
                          </template>
                        </div>
                        <div
                          v-if="additionalDisConfStatus&&isAdditionalConflict(item.fCode)"
                          class="additionalDisConfTip additionalDisConfScrollDom1"
                        >
                          {{matchAdditionalConfMsg(item.fCode)}}
                        </div>
                      </div>
                    </div>
                  </div>

                  <total-amount
                    v-if="openTable.performType===2|| openTable.showTotalPrice"
                    :loading="discountRelated.requestDiscountLoading"
                    :all-price="(discountGroup.length||taxesGroup.length)?discountedPrice:allPriceCache"
                    :label="systemLanguage.totalLabel"
                    :currency-way="openTable.currencyWay"
                    :original-price="allPriceCache"
                    :has-discount="discountGroup.length"
                    :subtotal-label="systemLanguage.subtotalText"
                    :discount-group="discountGroup"
                    :taxes-group="taxesGroup"
                    :lan="openTable.language"
                    :tax-config="openTable.billTax"
                    :service-charges="openTable.serviceCharges"
                    :system-language="systemLanguage"
                    :cart-service-charges="cartServiceCharges"
                  ></total-amount>
                </div>
                <!-- 猜你感兴趣数据 -->
                <div class="hotSaleData" v-show="showHotSaleWarp">
                  <p class="hotSale_top_title">{{systemLanguage.hotSaleTitle}}</p>
                  <div class="hotSale_warp_content">
                    <div
                      class="hotSale_warp_cell"
                      :class="{ 'maskLayer': item.isExpired }"
                      v-for="(item,index) in hotSaleData"
                      :key="index"
                      @click="onShowhotSaleItem(item)"
                    >
                      <div class="hotSale_warp_cell_top">
                        <div class="soldOut_warp" v-show="item.itemCtrl">
                          <img
                            src="../static/img/newImage/soldOut.jpg"
                            alt=""
                            class="soldOutLogo"
                          />
                        </div>
                        <img
                          v-lazy="item.imglink"
                          alt=""
                          class="hotSale_item_img"
                          :key="item.imglink"
                        />
                        <div class="hotSale_item_price" v-show="item[priceName('foodList')]">
                          {{showOuterPrice(item[priceName('foodList')])}}
                        </div>
                      </div>
                      <div class="hotSale_warp_cell_name">{{inListTitle(item)}}</div>
                    </div>
                  </div>
                </div>
              </template>
              <review-order-page
                v-if="openTable.performType===1&&openTable.displayCartOrderRecord"
                :orders="historyOrders"
                :open-table="openTable"
                :system-language="systemLanguage"
              ></review-order-page>
            </div>

            <div class="cartPromptText" v-if="openTable.shopTipText">
              <span
                class="textPrompt"
                :class="[openTable.shopTipText.tipFontSize=='normal'?'normal_font':'enlarge_font']"
              >
                {{openTable.shopTipText[openTable.language]}}
              </span>
            </div>
            <!-- 送单按钮 -->
            <div
              @click="onSubOrderBtn"
              :class="[(allshoplNumber == 0||cartEditXi)? 'disabledSend':hasProperty('animation','page_breathe')&&'btnPulse', 'send_single']"
            >
              {{ systemLanguage.confirmOrder }}
            </div>
          </div>
        </transition>
      </template>
      <!-- 提交订单页 -->
      <template>
        <transition :name="hasProperty('animation','page_transition')?'slide':''">
          <section id="send-order-view" v-show="showSendOrderView">
            <v-app>
              <div :flat="true" :height="rootFontSize*1.2" color="var(--styleColor)" class="pa-0">
                <!-- 顶部栏 header -->
                <div class="send-order-view-header">
                  <!-- 返回按钮 -->
                  <img
                    src="../static/img/page/black.jpg"
                    alt=""
                    class="backFtyIcon"
                    @click="onBack"
                  />
                  <nav-header-table
                    :open-Table="openTable"
                    :system-Language="systemLanguage"
                  ></nav-header-table>
                </div>
              </div>
              <v-main class="flex-fill" style="overflow-y: scroll">
                <!-- 据中内容区域 -->
                <div class="send-order-view-main">
                  <!-- 配送栏信息 -->
                  <div class="cart_info_warp_delivery" v-if="isTakeAway">
                    <div class="cart_info_warp_delivery_address" style="width: fit-content">
                      <div class="d-flex align-center" style="min-width: 2rem">
                        <img
                          src="../static/img/page/storeAddress.jpg"
                          alt=""
                          class="cart_info_warp_delivery_icon"
                        />
                        {{systemLanguage.pickUpAddressLabel}}
                      </div>
                      <div @click.stop="backMap" v-if="openTable.storeData">
                        <span>{{handleOpenTableLangEchoByStore('storeName',true)}}</span>
                        <img
                          class="backMapIcon"
                          src="../static/img/page/backMap.jpg"
                          alt="backMap"
                        />
                      </div>
                      <div v-else>{{handleOpenTableLangEchoByStore('storeName',true)}}</div>
                    </div>
                    <div class="d-flex" style="padding-left: 2rem">
                      {{handleOpenTableLangEchoByStore('storeAddress',true)}}
                    </div>
                    <div class="cart_info_warp_delivery_time">
                      <div class="cart_info_warp_delivery_time_left">
                        <div class="d-flex align-center" style="min-width: 2rem">
                          <img
                            src="../static/img/page/storeTime.jpg"
                            alt=""
                            class="cart_info_warp_delivery_icon"
                          />
                          <span>{{systemLanguage.pickupTimeLabel}}</span>
                        </div>
                        <span :style="{color:(stressDate()?'var(--styleColor)':'unset')}">
                          <template v-if="openTable.storeData">
                            {{handleOpenTableLangEchoByStore('pickupTime')||systemLanguage.instantPickup}}
                          </template>
                          <template v-else>{{systemLanguage.instantPickup}}</template>
                        </span>
                      </div>
                      <button
                        light
                        plain
                        depressed
                        text
                        color="#828282"
                        @click="handleChangeDeliveryTime"
                        v-if="openTable.storeData&&openTable.pickupTime"
                      >
                        {{systemLanguage.changeText}}
                      </button>
                    </div>
                  </div>
                  <!--支付方式     -->
                  <div class="payment-methods-wrap" v-if="getAllPayMethod.length">
                    <p class="payment-methods-title">
                      <strong>{{systemLanguage.paymentText}}</strong>
                    </p>
                    <div class="payMent-form-payTypeBox">
                      <div
                        class="payMent-form-item"
                        v-for="item in orderPayMethod"
                        :key="item.value"
                      >
                        <label
                          class="payMent-label"
                          :class="{'disabled':item.value=='wallet'&&isDisabledWallet}"
                        >
                          <input
                            type="radio"
                            name="payment"
                            :value="item.value"
                            v-model="choosePayMethod"
                            autocomplete="off"
                            :disabled="item.value=='wallet'&&isDisabledWallet"
                          />
                          <img :src="item.imgSrc" :class="item.class" />
                          <template v-if="Array.isArray(item.value)">
                            {{systemLanguage[item.belong+'Txt']}}
                          </template>
                          <template v-else>{{systemLanguage[item.value+'Txt']}}</template>
                          <span>{{getPaymentGatewaySuffix(item.value)}}</span>
                        </label>
                      </div>
                    </div>
                  </div>
                  <!-- 无支付方式且存在必填个人信息显示此标题 -->
                  <div
                    class="payment-methods-wrap"
                    v-if="!getAllPayMethod.length&&hasRequiredPersonalInfo"
                  >
                    <p class="payment-methods-title">
                      <strong>{{systemLanguage.contactInfoTxt}}</strong>
                    </p>
                  </div>
                </div>
                <v-container fluid class="pl-5 pr-5">
                  <v-form ref="sendOrderForm" v-model="valid" lazy-validation>
                    <v-row align="center" class="mt-0 mb-0">
                      <v-col class="d-flex pt-0 pb-0">
                        <v-text-field
                          v-model="sendOrderForm.name"
                          :rules="choosePayMethod=='payAtCashier'||!openTable.nameMandatory?false:rules.name"
                          dense
                          :label="systemLanguage.sendOrderFormNameLabel"
                          outlined
                          :disabled="choosePayMethod=='payAtCashier'"
                          :class="{'text-field-required': openTable.nameMandatory}"
                        >
                          <template
                            slot="prepend-inner"
                            v-if="choosePayMethod!='payAtCashier'&&openTable.nameMandatory"
                          >
                            <span class="text-field-prepend-inner">⁕</span>
                          </template>
                        </v-text-field>
                      </v-col>
                    </v-row>
                    <!-- 电话 -->
                    <v-row
                      v-if="openTable.logCustomerPhone||choosePayMethod=='wallet'"
                      class="mt-0 mb-0"
                    >
                      <v-col class="d-flex pt-0 pb-0 pr-0" cols="4">
                        <v-select
                          v-model="sendOrderForm.areaCode"
                          :items="Object.values(regionList)"
                          :rules="customerPhoneRules?rules.phone : []"
                          dense
                          item-text="phone"
                          item-value="phone[0]"
                          :label="systemLanguage.sendOrderFormAreaLabel"
                          outlined
                          :disabled="choosePayMethod=='payAtCashier'"
                        >
                          <template slot="prepend-inner">
                            <span style="padding-top: 0.25em; z-index: 1">+</span>
                          </template>
                          <template slot="item" slot-scope="{item,index,select}">
                            <v-row class="d-flex" justify="center">
                              <v-col cols="12">
                                <span :class="'fi-'+item.code" class="fi fis"></span>
                                <span class="pl-1">
                                  <strong>{{ item.name }}</strong>
                                  ({{ item.native }})
                                </span>
                                <span style="color: #cccccc">+{{ item.phone[0] }}</span>
                              </v-col>
                            </v-row>
                          </template>
                        </v-select>
                      </v-col>
                      <v-col class="d-flex pt-0 pb-0" cols="8">
                        <v-text-field
                          class="phoneField"
                          type="tel"
                          v-model="sendOrderForm.phone"
                          :rules="choosePayMethod=='payAtCashier'?false:customerPhoneRules?rules.phone:rules.number"
                          dense
                          :label="systemLanguage.sendOrderFormPhoneLabel"
                          outlined
                          :class="{'text-field-required': customerPhoneRules}"
                          :disabled="isDisabledSendFormPhone"
                          :persistent-hint="isShowPhoneHint().show"
                          :hint="isShowPhoneHint().show?isShowPhoneHint().message:false"
                        >
                          <template
                            slot="prepend-inner"
                            v-if="choosePayMethod!='payAtCashier'&&customerPhoneRules"
                          >
                            <span class="text-field-prepend-inner">⁕</span>
                          </template>
                        </v-text-field>
                      </v-col>
                    </v-row>
                    <!-- 验证码 -->
                    <v-row v-if="choosePayMethod=='wallet'" align="flex-start" class="mt-0 mb-0">
                      <v-col cols="8" class="pt-0 pb-0">
                        <v-text-field
                          v-model.trim="sendOrderForm.verificationCode"
                          :rules="rules.captcha"
                          dense
                          :label="systemLanguage.walletFormCaptchaLabel"
                          outlined
                          class="discount_code_input"
                        >
                          <template slot="prepend-inner">
                            <span class="text-field-prepend-inner">⁕</span>
                          </template>
                        </v-text-field>
                      </v-col>
                      <v-col cols="4" class="pt-0 pb-0">
                        <v-btn
                          :disabled="captchaDisabled()"
                          class="pa-0"
                          depressed
                          color="var(--styleColor)"
                          outlined
                          style="width: inherit; height: 40px"
                          :loading="captchaBtnLoading"
                        >
                          <span
                            @click="getCaptcha"
                            class="flex-fill d-flex align-center justify-center"
                          >
                            {{captchaBtnText}}
                          </span>
                        </v-btn>
                      </v-col>
                    </v-row>
                    <v-fade-transition hide-on-leave>
                      <v-row v-if="choosePayMethod !== 'iPay88'" align="center" class="mt-0 mb-0">
                        <v-col class="pt-0 pb-0" cols="12">
                          <v-checkbox
                            transition="fade-transition"
                            v-model="sendOrderForm.incomingMail"
                            @change="e=>sendOrderForm.email=''"
                            class="mt-0"
                            :label="systemLanguage.sendOrderFormIncomingMailLabel"
                            style="width: fit-content; font-weight: 600"
                            :disabled="choosePayMethod=='payAtCashier'||openTable.emailMandatory"
                          ></v-checkbox>
                          <v-text-field
                            v-model="sendOrderForm.email"
                            :rules="sendOrderForm.incomingMail?rules.email:false"
                            type="email"
                            dense
                            :label="systemLanguage.sendOrderFormEmailLabel"
                            :disabled="!sendOrderForm.incomingMail||choosePayMethod=='payAtCashier'"
                            outlined
                            clearable
                            :class="{'text-field-required': sendOrderForm.incomingMail}"
                          >
                            <template slot="prepend-inner" v-if="sendOrderForm.incomingMail">
                              <span class="text-field-prepend-inner">⁕</span>
                            </template>
                          </v-text-field>
                        </v-col>
                      </v-row>
                    </v-fade-transition>
                    <v-row v-if="openTable.promotionDiscount" align="flex-start" class="mt-0 mb-0">
                      <v-col cols="8" class="pt-0 pb-0">
                        <v-text-field
                          v-model.trim="sendOrderForm.discountCode"
                          :disabled="disabledDiscountField"
                          :messages="discountRelated.isShowRemoveIcon?discountCodeDesc[openTable.language]||false:false"
                          dense
                          :success-messages="[]"
                          :label="systemLanguage.discountText"
                          outlined
                          :class="['discount_code_input',{'discountConflict-text-field':additionalDisConfStatus}]"
                          :loading="discountRelated.requestDiscountLoading?'var(--styleColor)':false"
                          @input="()=>discountRelated.isShowDiscountErrorMsg=false"
                        >
                          <template
                            v-if="discountRelated.isShowRemoveIcon&&!additionalDisConfStatus"
                            slot="append"
                          >
                            <v-icon transition="fade-transition" color="green" dense>
                              mdi-check-bold
                            </v-icon>
                          </template>
                        </v-text-field>
                      </v-col>
                      <v-col cols="4" class="pt-0 pb-0">
                        <v-btn
                          :loading="discountRelated.requestDiscountLoading&&!isMemberDiscount()"
                          :disabled="discountCompulsory||!sendOrderForm.discountCode"
                          class="pa-0"
                          depressed
                          color="var(--styleColor)"
                          outlined
                          style="width: inherit; height: 40px"
                        >
                          <span
                            v-if="discountRelated.requestDiscountStatus&&!isMemberDiscount()"
                            class="flex-fill d-flex align-center justify-center"
                            disabled
                          >
                            <v-icon color="success">mdi-check-bold</v-icon>
                          </span>
                          <span
                            v-else-if="discountRelated.isShowRemoveIcon"
                            class="flex-fill d-flex align-center justify-center"
                            @click="onRemoveDiscount"
                          >
                            <v-icon color="red">mdi-close-circle</v-icon>
                          </span>
                          <span
                            v-else
                            @click="onConfirmDiscount"
                            class="flex-fill d-flex align-center justify-center"
                          >
                            {{systemLanguage.confirmBtn}}
                          </span>
                        </v-btn>
                      </v-col>
                    </v-row>
                    <!-- 附加项与折扣冲突提示 -->
                    <v-row
                      v-if="additionalDisConfStatus"
                      align="flex-start"
                      class="d-flex flex-column mt-0 mb-0 additionalDisConfScrollDom2"
                    >
                      <v-col
                        v-for="(item,i) in discountConflictTips('additional')"
                        :key="i"
                        class="d-flex align-center pt-0 pb-0"
                      >
                        <v-icon color="red" class="" style="font-size: 0.4rem">
                          mdi-alert-circle
                        </v-icon>
                        <span class="pl-1 red--text">{{item}}</span>
                      </v-col>
                    </v-row>
                    <v-fade-transition hide-on-leave>
                      <v-row
                        v-if="choosePayMethod == 'iPay88'||choosePayMethod=='razer'"
                        align="center"
                        class="mt-0 mb-0"
                      >
                        <v-col class="pt-0 pb-0">
                          <v-checkbox
                            :indeterminate="showAgreement"
                            readonly
                            :rules="rules.agreement"
                            transition="fade-transition"
                            v-model="sendOrderForm.agreement"
                            class="mt-0"
                            style="width: fit-content; font-weight: 600"
                            @click="toggleAgreementStatus"
                          >
                            <template v-slot:label>
                              <span
                                @click.stop="onAgreementCheck"
                                class="text-decoration-underline"
                                style="color: #3679c8"
                              >
                                {{systemLanguage.AgreementText}}
                              </span>
                            </template>
                          </v-checkbox>
                        </v-col>
                      </v-row>
                    </v-fade-transition>
                  </v-form>
                </v-container>
              </v-main>

              <div app class="pa-0" absolute padless color="#ffffff">
                <total-amount
                  v-if="openTable.performType===2 || openTable.showTotalPrice"
                  :loading="discountRelated.requestDiscountLoading"
                  :all-price="(discountGroup.length||taxesGroup.length)?discountedPrice:allPriceCache"
                  :label="systemLanguage.totalLabel"
                  :currency-way="openTable.currencyWay"
                  :original-price="allPriceCache"
                  :has-discount="!!discountGroup.length"
                  :subtotal-label="systemLanguage.subtotalText"
                  :discount-group="discountGroup"
                  :taxes-group="taxesGroup"
                  :lan="openTable.language"
                  :tax-config="openTable.billTax"
                  :service-charges="openTable.serviceCharges"
                  :system-language="systemLanguage"
                  :cart-service-charges="cartServiceCharges"
                ></total-amount>
                <!--底部确认按钮-->
                <div class="send-order-view-footer">
                  <!-- 送单按钮  next  -->
                  <div
                    @click="onSubOrderBtn"
                    class="send_single"
                    :class="{'btnPulse':hasProperty('animation','page_breathe')}"
                  >
                    {{systemLanguage.nextLabel}}
                  </div>
                </div>
              </div>
            </v-app>
          </section>
        </transition>
      </template>
      <!--     agreement dialog  免责声明弹窗文字 -->
      <template>
        <v-dialog v-model="showAgreement" hide-overlay persistent width="80%" scrollable>
          <v-card>
            <v-card-text
              class="v-size--small agreement-scroll-text pa-4"
              ref="agreement-scroll-text"
              v-scroll.self="onAgreementContentScroll"
              style="font-size: 0.4rem; line-height: 0.8rem; height: 500px"
            >
              <pre v-html="openTable.statement&&openTable.statement[this.openTable.language]"></pre>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                :disabled="useAgreementBtn"
                color="var(--styleColor)"
                class="v-picker--full-width text-subtitle"
                text
                @click="onCloseAreementDialog"
              >
                {{systemLanguage.AgreeText}}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </template>
      <!--     snackBar  -->
      <template>
        <v-snackbar
          v-model="notification"
          :timeout="timeOut"
          text
          centered
          light
          app
          :color="snackTheme"
        >
          {{ notificationText }}
          <template v-slot:action="{ attrs }">
            <v-btn :color="snackTheme" text v-bind="attrs" @click="notification = false">
              {{systemLanguage.closeBtn}}
            </v-btn>
          </template>
        </v-snackbar>
      </template>

      <!-- 抛物线DOM -->
      <template>
        <div class="ball-container">
          <!--小球-->
          <div v-for="ball in balls">
            <transition
              name="drop"
              @before-enter="beforeDrop($event)"
              @enter="dropping($event)"
              @after-enter="afterDrop($event)"
            >
              <div class="ball" v-show="ball.show">
                <div class="inner inner-hook">
                  <img
                    :src="ball.imgLink"
                    alt=""
                    style="
                      width: 40px;
                      height: 40px;
                      border-radius: 50%;
                      position: relative;
                      z-index: 200;
                    "
                    onerror="this.src='../static/img/newImage/timg.jpg';this.onerror=null"
                  />
                </div>
              </div>
            </transition>
          </div>
        </div>
      </template>

      <!-- 可选细项弹窗选择第一层(追加套餐进一层or我的细项) -->
      <template>
        <div class="foodDialog" style="display: none">
          <div class="title" v-if="clickXiItem.length!=0">
            {{inListTitle(clickXiItem)}}
            <span
              v-if="isShowQtyBound(clickXiItem,1)"
              :class="[selectProDialog(clickXiItem,localAddSMXiFtyArry,localAddSMXiMtyArry)?'selectCol1':'selectCol2','selectMinNumBox']"
            >
              <span class="selectMinNum">
                {{selectPrompt(clickXiItem.minQty2,clickXiItem.maxQty2)}}
              </span>
            </span>
            <span v-if="clickXiItem.minQty2 == 0" class="optionalText">
              {{systemLanguage.qtyItemOptional}}
            </span>
          </div>
          <div class="optionalFood" id="myxiDialog">
            <!-- 追加套餐细项 -->
            <food-detail-fixed-item
              :system-language="systemLanguage"
              :food-info-item="clickXiItem"
              cell-class="addSealXi_Title"
              class="mar_LR"
              :level="2"
              v-if="hasFoodList(clickXiItem)||hasMListList(clickXiItem)"
            ></food-detail-fixed-item>
            <!-- 可选细项第一层(合并)  -->
            <div
              class="food_info_warp_content_foodtypeInList mar_LR"
              v-if="clickXiItem.allTypeArry&&clickXiItem.allTypeArry.length != 0"
            >
              <div
                :class="['clickXiItem_cell', {'fold-food-type-item':itemFoldable}]"
                v-for="(vItem, i) in clickXiItem.allTypeArry"
                :key="vItem.code"
                v-show="displayMemberFtype(vItem)"
              >
                <template>
                  <!--checked则默认展开-->
                  <input
                    hidden
                    type="checkbox"
                    checked
                    style="display: none"
                    :id="'two_'+vItem.code"
                  />
                  <label :for="'two_'+vItem.code">
                    <div class="optionalTitle">
                      {{outListTitle(vItem)}}
                      <span
                        v-if="isShowQtyBound(vItem,2)"
                        :class="[selectPro(vItem,vItem.typeName)?'selectCol1':'selectCol2','selectMinNumBox']"
                      >
                        <span class="selectMinNum">
                          {{selectPrompt(vItem.minQty,vItem.maxQty)}}
                        </span>
                      </span>
                      <span v-if="vItem.minQty == 0" class="optionalText">
                        {{systemLanguage.qtyItemOptional}}
                      </span>
                      <div class="expand-icon" v-if="itemFoldable">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                          <path
                            fill="var(--styleColor)"
                            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2m3.79 9.71l-3.08 3.08c-.39.39-1.02.39-1.42 0l-3.08-3.08c-.39-.39-.39-1.03 0-1.42a.996.996 0 0 1 1.41 0L12 12.67l2.38-2.38a.996.996 0 0 1 1.41 0c.39.39.39 1.03 0 1.42"
                          />
                        </svg>
                      </div>
                    </div>
                    <div class="selected-list" v-if="itemFoldable">
                      <p v-for="title in selectedItemInType(vItem)" :key="title">{{title}}</p>
                    </div>
                  </label>
                  <!-- 可选细项第一层--foodtypeInList -->
                  <div v-if="vItem.typeName =='ftyItem'" class="ftyItem_warp fold-item-list-warp">
                    <div class="infoPoints_content" v-if="hasFoodList(vItem)">
                      <div
                        v-for="(item, index) in vItem.foodList"
                        :key="index"
                        :class="[vItem.food_display_column == 2 ? 'twoColumnList': 'oneColumnList', openTable.lModPhoto ? 'lModPhotoStyle' : '']"
                        class="infoPoints_content_cell addSealXi_Title"
                        v-show="displayMemberFood(item)"
                      >
                        <div class="food_disable_box" v-if="showTimeoutStyle(item,vItem)"></div>
                        <div
                          :class="[item.selected ? 'infoPoints_select': 'infoPoints_noselect',item.itemCtrl?'hideBack':'']"
                          class="default_infoPoints"
                          @click.stop="addSMXiSelect(item, 'foodtypeInList',vItem)"
                        >
                          <!-- 售罄itemCtrl -->
                          <img
                            v-if="item.itemCtrl"
                            src="../static/img/newImage/soldOutList.jpg"
                            alt=""
                            class="soldoutList"
                          />
                          <!-- 细项缩略图 -->
                          <img
                            v-if="vItem.food_show_src"
                            :src="jointImgUrl(item,'food')"
                            class="typeListImg"
                            onerror="this.src='../static/img/newImage/timg.jpg';this.onerror=null"
                          />
                          <!-- 我的细项图标 -->
                          <img
                            src="../static/img/newImage/custom.jpg"
                            alt=""
                            class="infoPoints_addicon"
                            @click.stop="secOnPicker(vItem,item,'foodtypeInList')"
                            v-if="item.listSelect&&item.selected"
                          />
                          <div class="xi_Title">
                            {{inListTitle(item)}}
                            <!-- 细项价钱 -->
                            <template v-if="item[priceName('foodList')]">
                              {{showXiPrice(item[priceName('foodList')])}}
                            </template>
                          </div>
                          <!-- 细项删除 -->
                          <div
                            v-if="item.selected"
                            class="delXiItem_warp"
                            @click.stop="addSMXiDelXiItem(item,'foodtypeInList',vItem.code)"
                          >
                            <img
                              src="../static/img/newImage/delXiItem.jpg"
                              class="delXiItem_icon"
                            />
                          </div>
                          <!-- 细项数量及详情点击 -->
                          <div class="xiNum_warp" v-if="item.selected">{{item.qty1||1}}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 可选细项第一层--mtypeInList -->
                  <div v-if="vItem.typeName =='mtyItem'" class="mtyItem_warp fold-item-list-warp">
                    <div class="infoPoints_content" v-if="vItem.mListList.length!=0">
                      <div
                        v-for="(item, index) in vItem.mListList"
                        :key="index"
                        :class="[vItem.mList_display_column == 2 ? 'twoColumnList': 'oneColumnList', openTable.lModPhoto ? 'lModPhotoStyle' : '']"
                        class="infoPoints_content_cell addSealXi_Title"
                      >
                        <div class="food_disable_box" v-if="showTimeoutStyle(item,vItem)"></div>
                        <div
                          :class="[item.selected ? 'infoPoints_select': 'infoPoints_noselect',item.itemCtrl?'hideBack':'']"
                          class="default_infoPoints"
                          @click.stop="addSMXiSelect(item, 'mtypeInList',vItem)"
                        >
                          <!-- 售罄itemCtrl -->
                          <img
                            v-if="item.itemCtrl"
                            src="../static/img/newImage/soldOutList.jpg"
                            alt=""
                            class="soldoutList"
                          />
                          <!-- 细项缩略图 -->
                          <img
                            v-if="vItem.mList_show_src"
                            :src="jointImgUrl(item,'mlist')"
                            class="typeListImg"
                            onerror="this.src='../static/img/newImage/timg.jpg';this.onerror=null"
                          />
                          <!-- 我的细项图标 -->
                          <img
                            src="../static/img/newImage/custom.jpg"
                            alt=""
                            class="infoPoints_addicon"
                            @click.stop="secOnPicker(vItem,item,'mtypeInList')"
                            v-if="item.listSelect&&item.selected"
                          />
                          <div class="xi_Title">
                            {{inListTitle(item)}}
                            <!-- 细项价钱 -->
                            <template v-if="item[priceName('mList')]">
                              {{showXiPrice(item[priceName('mList')])}}
                            </template>
                          </div>
                          <!-- 细项删除 -->
                          <div
                            v-if="item.selected"
                            class="delXiItem_warp"
                            @click.stop="addSMXiDelXiItem(item,'mtypeInList',vItem.code)"
                          >
                            <img
                              src="../static/img/newImage/delXiItem.jpg"
                              class="delXiItem_icon"
                            />
                          </div>
                          <!-- 细项数量及详情点击 -->
                          <div class="xiNum_warp" v-if="item.selected">{{item.qty1||1}}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div class="btns">
            <div class="cencelBtn small_btn" @click="onCencelMyxi">
              {{systemLanguage.cancelBtn}}
            </div>
            <div class="defaultBtn small_btn" @click="onfineItem">
              {{systemLanguage.confirmBtn}}
            </div>
          </div>
        </div>
      </template>

      <!-- 可选细项弹窗选择第二层 -->
      <template>
        <div class="secondDialog" style="display: none">
          <div class="title" v-if="myXiClickXiItem.length!=0">
            {{inListTitle( myXiClickXiItem)}}
            <span
              v-if="isShowQtyBound(myXiClickXiItem,1)"
              :class="[selectProDialog(myXiClickXiItem,localSecAddSMXiFtyArry,localSecAddSMXiMtyArry)?'selectCol1':'selectCol2','selectMinNumBox']"
            >
              <span class="selectMinNum">
                {{selectPrompt( myXiClickXiItem.minQty2, myXiClickXiItem.maxQty2)}}
              </span>
            </span>
            <span v-if="myXiClickXiItem.minQty2 == 0" class="optionalText">
              {{systemLanguage.qtyItemOptional}}
            </span>
          </div>
          <div class="secOptionalFood" id="secXiDialog">
            <!-- 可选细项第二层 固定细项  -->
            <food-detail-fixed-item
              :system-language="systemLanguage"
              :food-info-item="myXiClickXiItem"
              cell-class="addSealXi_Title"
              :level="3"
              v-if="hasFoodList(myXiClickXiItem)||hasMListList(myXiClickXiItem)"
            ></food-detail-fixed-item>
            <!-- 可选细项第二层(合并)  -->
            <div
              class="food_info_warp_content_foodtypeInList"
              v-if=" myXiClickXiItem.allTypeArry&& myXiClickXiItem.allTypeArry.length != 0"
            >
              <div
                :class="['myXiClickXiItem_cell', {'fold-food-type-item':itemFoldable}]"
                v-for="(vItem, i) in  myXiClickXiItem.allTypeArry"
                :key="vItem.code"
                v-show="displayMemberFtype(vItem)"
              >
                <template>
                  <!--checked则默认展开-->
                  <input
                    hidden
                    type="checkbox"
                    checked
                    style="display: none"
                    :id="'three_'+vItem.code"
                  />
                  <label :for="'three_'+vItem.code">
                    <div class="optionalTitle">
                      {{outListTitle(vItem)}}
                      <span
                        v-if="isShowQtyBound(vItem,2)"
                        :class="[selectPro(vItem,vItem.typeName)?'selectCol1':'selectCol2','selectMinNumBox']"
                      >
                        <span class="selectMinNum">
                          {{selectPrompt(vItem.minQty,vItem.maxQty)}}
                        </span>
                      </span>
                      <span v-if="vItem.minQty == 0" class="optionalText">
                        {{systemLanguage.qtyItemOptional}}
                      </span>
                      <div class="expand-icon" v-if="itemFoldable">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                          <path
                            fill="var(--styleColor)"
                            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2m3.79 9.71l-3.08 3.08c-.39.39-1.02.39-1.42 0l-3.08-3.08c-.39-.39-.39-1.03 0-1.42a.996.996 0 0 1 1.41 0L12 12.67l2.38-2.38a.996.996 0 0 1 1.41 0c.39.39.39 1.03 0 1.42"
                          />
                        </svg>
                      </div>
                    </div>
                    <div class="selected-list" v-if="itemFoldable">
                      <p v-for="title in selectedItemInType(vItem)" :key="title">{{title}}</p>
                    </div>
                  </label>
                  <!-- 可选细项第二层--foodtypeInList -->
                  <div v-if="vItem.typeName =='ftyItem'" class="ftyItem_warp fold-item-list-warp">
                    <div class="infoPoints_content" v-if="hasFoodList(vItem)">
                      <div
                        v-for="(item, index) in vItem.foodList"
                        :key="index"
                        :class="[vItem.food_display_column == 2 ? 'twoColumnList': 'oneColumnList', openTable.lModPhoto ? 'lModPhotoStyle' : '']"
                        class="infoPoints_content_cell addSealXi_Title"
                        v-show="displayMemberFood(item)"
                      >
                        <div class="food_disable_box" v-if="showTimeoutStyle(item,vItem)"></div>
                        <div
                          :class="[item.selected ? 'infoPoints_select': 'infoPoints_noselect',item.itemCtrl?'hideBack':'']"
                          class="default_infoPoints"
                          @click.stop="secAddSMXiSelect(item, 'foodtypeInList',vItem)"
                        >
                          <!-- 售罄itemCtrl -->
                          <img
                            v-if="item.itemCtrl"
                            src="../static/img/newImage/soldOutList.jpg"
                            alt=""
                            class="soldoutList"
                          />
                          <!-- 细项缩略图 -->
                          <img
                            v-if="vItem.food_show_src"
                            :src="jointImgUrl(item,'food')"
                            class="typeListImg"
                            onerror="this.src='../static/img/newImage/timg.jpg';this.onerror=null"
                          />
                          <div class="xi_Title">
                            {{inListTitle(item)}}
                            <!-- 细项价钱 -->
                            <template v-if="item[priceName('foodList')]">
                              {{showXiPrice(item[priceName('foodList')])}}
                            </template>
                          </div>
                          <!-- 细项删除 -->
                          <div
                            v-if="item.selected"
                            class="delXiItem_warp"
                            @click.stop="secDelXiItem(item,'foodtypeInList',vItem.code)"
                          >
                            <img
                              src="../static/img/newImage/delXiItem.jpg"
                              class="delXiItem_icon"
                            />
                          </div>
                          <!-- 细项数量及详情点击 -->
                          <div class="xiNum_warp" v-if="item.selected">{{item.qty1||1}}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 可选细项第二层--mtypeInList -->
                  <div v-if="vItem.typeName =='mtyItem'" class="mtyItem_warp fold-item-list-warp">
                    <div class="infoPoints_content" v-if="vItem.mListList.length!=0">
                      <div
                        v-for="(item, index) in vItem.mListList"
                        :key="index"
                        :class="[vItem.mList_display_column == 2 ? 'twoColumnList': 'oneColumnList', openTable.lModPhoto ? 'lModPhotoStyle' : '']"
                        class="infoPoints_content_cell addSealXi_Title"
                      >
                        <div class="food_disable_box" v-if="showTimeoutStyle(item,vItem)"></div>
                        <div
                          :class="[item.selected ? 'infoPoints_select': 'infoPoints_noselect',item.itemCtrl?'hideBack':'']"
                          class="default_infoPoints"
                          @click.stop="secAddSMXiSelect(item, 'mtypeInList',vItem)"
                        >
                          <!-- 售罄itemCtrl -->
                          <img
                            v-if="item.itemCtrl"
                            src="../static/img/newImage/soldOutList.jpg"
                            alt=""
                            class="soldoutList"
                          />
                          <!-- 细项缩略图 -->
                          <img
                            v-if="vItem.mList_show_src"
                            :src="jointImgUrl(item,'mlist')"
                            class="typeListImg"
                            onerror="this.src='../static/img/newImage/timg.jpg';this.onerror=null"
                          />
                          <div class="xi_Title">
                            {{inListTitle(item)}}
                            <!-- 细项价钱 -->
                            <template v-if="item[priceName('mList')]">
                              {{showXiPrice(item[priceName('mList')])}}
                            </template>
                          </div>
                          <!-- 细项删除 -->
                          <div
                            v-if="item.selected"
                            class="delXiItem_warp"
                            @click.stop="secDelXiItem(item,'mtypeInList',vItem.code)"
                          >
                            <img
                              src="../static/img/newImage/delXiItem.jpg"
                              class="delXiItem_icon"
                            />
                          </div>
                          <!-- 细项数量及详情点击 -->
                          <div class="xiNum_warp" v-if="item.selected">{{item.qty1||1}}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div class="btns">
            <div class="cencelBtn small_btn" @click="onSecCencel">{{systemLanguage.cancelBtn}}</div>
            <div class="defaultBtn small_btn" @click="onSecfineItem">
              {{systemLanguage.confirmBtn}}
            </div>
          </div>
        </div>
      </template>

      <!-- 第一层细项数量详情框 -->
      <template>
        <div class="xiNumDia" style="display: none">
          <div class="selXiCell" v-for="(item,index) in localXiItem" :key="index">
            <label class="xiNumDia_checkLabel">
              <div class="xiNumDia_txt">
                <p class="xiNumDia_xi">
                  <span class="checkSpan">{{inListTitle(item)}}</span>
                </p>
                <p class="xiNumDia_myXi" v-html="showlogic(item,'optonXi')"></p>
              </div>
            </label>
            <div class="xiNumDia_chooseBtn" @click="showMyxiDia(item)">
              {{systemLanguage.optionBtn}}
            </div>
          </div>
        </div>
      </template>
      <!-- 第二层细项数量详情框 -->
      <template>
        <div class="secXiNumDia" style="display: none">
          <div class="selXiCell" v-for="(item,index) in secLocalXiItem" :key="index">
            <label class="xiNumDia_checkLabel">
              <div class="xiNumDia_txt">
                <p class="xiNumDia_xi">
                  <span class="checkSpan">{{inListTitle(item)}}</span>
                </p>
                <p class="xiNumDia_myXi" v-html="showlogic(item,'optonXi')"></p>
              </div>
            </label>
            <div class="xiNumDia_chooseBtn" @click="secShowMyxiDia(item)">
              {{systemLanguage.optionBtn}}
            </div>
          </div>
        </div>
      </template>
      <!-- 预点餐模块 -->
      <template>
        <div class="preOrderDia" style="display: none">
          <div class="preOrder-form-item">
            <label class="preOrder-label">
              <img src="../static/img/newImage/tableIcon.jpg" class="tableIcon" />
            </label>
            <div class="preOrder-input-block">
              <input
                type="text"
                name="tableNum"
                v-model="preOrderObj.tableNum"
                :placeholder="systemLanguage.preOrderTableNumTip"
                class="layui-input"
                style="text-transform: uppercase"
              />
            </div>
          </div>
          <div class="preOrder-form-item">
            <label class="preOrder-label">
              <img src="../static/img/newImage/QRcode.jpg" class="QRcodeIcon" />
            </label>
            <div class="preOrder-input-block">
              <input
                type="text"
                name="tableKey"
                v-model="preOrderObj.tableKey"
                :placeholder="systemLanguage.preOrderTableKeyTip"
                class="layui-input"
                style="text-transform: uppercase"
              />
            </div>
          </div>
          <div class="preOrder-form-item">
            <div class="preOrder-form-btn">
              <button class="layui-btn layui-btn-sm layui-btn-danger" @click="onPreOrderCancel">
                {{systemLanguage.cancelBtn}}
              </button>
              <button class="layui-btn layui-btn-sm layui-btn-normal" @click="onPreOrderSub">
                {{systemLanguage.confirmBtn}}
              </button>
            </div>
          </div>
        </div>
      </template>
      <!-- 定制化下单成功提示语 -->
      <template>
        <div class="postOrderPopup" style="display: none">
          <div
            v-if="openTable.sponsoredLink"
            style="text-align: center; padding: 0.1rem 0 0.3rem 0"
          >
            <p class="postOrderPopup_tip">{{systemLanguage.sponsoredTipTitle}}</p>
            <p class="postOrderPopup_tip">{{systemLanguage.sponsoredTip}}</p>
          </div>
          <img v-else src="../static/img/newImage/waitingTime.jpg" class="successOrderImg" />
          <p v-show="!openTable.sponsoredLink" class="postOrderPopup_tip">
            {{systemLanguage.postOrderPopupTip}}
          </p>
        </div>
      </template>

      <!-- 显示柜台支付号码 -->
      <template>
        <div class="paidAtCashierSucc">
          <div class="paidAtCashier-top">
            <img src="../static/img/newImage/succeed.jpg" alt="" class="paidAtCashier-top-icon" />
            <p class="paidAtCashier-top-title">{{systemLanguage.paidAtCashierTopTitle}}</p>
          </div>
          <p class="paidAtCashier-center">{{systemLanguage.paidAtCashierCenterTitle}}</p>
          <div class="paidAtCashier-bottom">
            <p class="paidAtCashier-bottom-title">{{systemLanguage.paidAtCashierBottomTitle}}</p>
            <p class="paidAtCashier-bottom-num">{{payAtCashierNum}}</p>
          </div>
          <div id="paidAtCashierQRcode"></div>
        </div>
      </template>
      <!-- 搜索食品弹窗 -->
      <template>
        <div id="searchFoodDia">
          <form onsubmit="return false">
            <div class="searchFoodDia-form">
              <label class="searchFoodDia-label">
                <i
                  class="layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop searchFoodDia-icon"
                  v-show="searchListLoading"
                ></i>
                <i
                  class="layui-icon layui-icon-search searchFoodDia-icon"
                  v-show="!searchListLoading"
                ></i>
              </label>

              <input
                type="text"
                :placeholder="systemLanguage.searchInputPlaceholder"
                class="layui-input searchFoodDia-input"
                @input="debounceSearchList"
                spellcheck="false"
              />
            </div>
          </form>
          <div class="searchList-content" v-if="machFoodNameList.length">
            <ul class="searchList-Commands">
              <li
                v-for="(item,index) in machFoodNameList"
                :key="index"
                class="searchList-Commands-li"
              >
                <h2>{{item.ftyName}}</h2>
                <div
                  class="searchList-Commands-li-foodWarp"
                  v-for="(foodObj,i) in item.foodList"
                  :key="i"
                >
                  <!-- 食品内容卡片 -->
                  <div
                    class="searchList-Commands-li-foodContent"
                    @click="jumpToProduct(foodObj.foodListData)"
                  >
                    <div class="searchList-Commands-li-foodContentRight">
                      <p v-html="foodObj.foodListName"></p>
                      <p v-show="foodObj.foodListData[priceName('foodList')]">
                        {{showOuterPrice(foodObj.foodListData[priceName('foodList')])}}
                      </p>
                    </div>
                    <img
                      v-lazy="foodObj.foodListData.imglink"
                      :key="item.imglink"
                      class="searchList-Commands-li-foodImg"
                    />
                  </div>
                  <!-- 单点添加 -->
                  <div
                    class="searchList-Commands-li-addBtn baseaddFoodBtn"
                    :class="{'btnDisabledStyle':isActionDisabled(foodObj.foodListData)}"
                    @click="searchAddProduct(event, foodObj.foodListData)"
                    v-if="showAddCartBtn(foodObj.foodListData)"
                  >
                    +
                  </div>
                  <!-- 蒙层/售罄超时 -->
                  <div class="searchList-itemCtrl" v-if="foodObj.foodListData.itemCtrl">
                    <img src="../static/img/newImage/soldOut.jpg" alt="" />
                  </div>
                  <!-- 售罄 -->
                  <div
                    class="disableBox"
                    v-if="foodObj.foodListData.hasOwnProperty('isExpired')"
                    @click.stop="showOutTimeTips"
                  ></div>
                </div>
              </li>
            </ul>
          </div>
          <p class="listSearch-Help" v-else>{{systemLanguage.searchEmptyTxt}}</p>
        </div>
      </template>
      <!-- 外卖模式提交订单前显示店铺信息弹窗 -->
      <take-away-sub-dia
        v-if="isTakeAway"
        ref="takeAwaySubDiaDom"
        @editStore="backMap"
        :system-language="systemLanguage"
        :store-photo-url="storePhotoUrl"
      ></take-away-sub-dia>
      <!-- 外卖模式切换预约时间的警告弹窗 -->
      <alter-pickup-time-dia :show-pickup-Dia.sync="showPickupDia"></alter-pickup-time-dia>
      <!-- 外卖模式预约时间失效的警告提示 -->
      <pickup-iceptor-dia
        :show-invalid-pickup-dia.sync="showInvalidPickupDia"
        v-if="initTimePickerState"
      ></pickup-iceptor-dia>
      <vuetify-base-dia ref="base-dialog"></vuetify-base-dia>
      <touch-listener ref="touch-listener"></touch-listener>
      <personal-center
        ref="personalCenter"
        :system-language="systemLanguage"
        :open-Table="openTable"
        :member-center-logo="memberCenterLogo"
        v-if="showCRM"
      ></personal-center>
      <register-page
        ref="registerPage"
        :system-language="systemLanguage"
        :open-table="openTable"
        v-if="showRegister"
      ></register-page>
      <reset-password
        ref="resetPassword"
        :system-language="systemLanguage"
        :open-table="openTable"
        v-if="showResetPassword"
      ></reset-password>
      <birthday-card
        style="display: none"
        :system-language="systemLanguage"
        :open-Table="openTable"
        v-if="showCRM"
      ></birthday-card>
      <order-stop-dia
        ref="orderStopDia"
        :system-language="systemLanguage"
        :open-Table="openTable"
        :queue-Time="queuedObj.queueTime"
        v-if="queuedObj.isOrderStop"
      ></order-stop-dia>
      <meal-order-timer
        ref="meal-order-timer"
        v-if="showOrderCountdown"
        :order-start-time="orderStartTime"
        :order-time-limit="openTable.orderTimeLimit"
        :order-deadline.sync="orderDeadline"
        :order-timer-status.sync="orderTimerStatus"
        :dynamic-list="orderTimeDynamicConfig"
      ></meal-order-timer>
      <!-- test台模拟当前时间 -->
      <test-time-simulation
        ref="testTimeSimulationWarp"
        v-if="!loading&&openTable.isEnableTimeSimulation&&isTestTable"
        :system-language="systemLanguage"
        :store-data="openTable.storeData"
      ></test-time-simulation>
      <div class="danmu"></div>
    </div>
    <script src="../static/plugins/hammer/hammer.min.js"></script>
    <script src="../static/components/BottomSheet.js"></script>
    <script src="../static/components/Coupons.js"></script>
    <script src="../static/components/RechargeView.js"></script>
    <script src="../static/components/CustomTabs.js"></script>
    <script src="../static/components/TakeAwaySubDia.js"></script>
    <script src="../static/components/AlterPickupTimeDia.js"></script>
    <script src="../static/components/PickupIceptorDia.js"></script>
    <script src="../static/components/VuetifyBaseDia.js"></script>
    <script src="../static/components/TouchListener.js"></script>
    <script src="../static/components/PersonalCenter.js"></script>
    <script src="../static/components/FoodComponent.js"></script>
    <script src="../static/byod_webUtils/lifecycle.js"></script>
    <script src="../static/byod_webUtils/Timer.js"></script>
    <script src="../static/components/LastOrderCountdown.js"></script>
    <script src="../static/components/birthdayCard.js"></script>
    <script src="../static/components/OrderWaitTips.js"></script>
    <script src="../static/components/OrderStopDia.js"></script>
    <script src="../static/components/VerticalOrderLayout.js"></script>
    <script src="../static/components/CustomSelect.js"></script>
    <script src="../static/components/ResetPassword.js"></script>
    <script src="../static/components/RegisterPage.js"></script>
    <script src="../static/components/FoodDetailFixedItem.js"></script>
    <script src="../static/byod_webUtils/AccurateTimer.js"></script>
    <script src="../static/byod_webUtils/RetryTimer.js"></script>
    <script src="../static/components/TestTimeSimulation.js"></script>
    <script src="../static/components/ReviewOrderPage.js"></script>
    <script src="../static/danmu/danmu.umd.js"></script>

    <script>
      // 第三方支付后退刷新
      window.addEventListener("pageshow", function (event) {
        //event.persisted属性为true时，表示当前文档是从往返缓存中获取
        if (event.persisted) location.reload()
      })
      //dom加载完成后自定义title
      window.addEventListener("DOMContentLoaded", () => {
        $.getJSON("../static/utils/config.json", function (data) {
          document.title = data.BYOD.DocumentTitle || ""
        })
      })
      // var vConsole = new VConsole()
      //拦截物理返回键,必须用户激活限制，防止网站滥用返回键劫持用户
      ;(function () {
        // 提取公共方法：建立历史栈
        function createHistoryStack() {
          for (let i = 0; i < 20; i++) {
            history.pushState({ step: i }, "", location.href)
          }
        }
        document.addEventListener("click", createHistoryStack, { once: true })
      })()

      Vue.use(VueLazyload, {
        attempt: 1,
        error: "../static/img/newImage/timg.jpg",
        loading: "../static/img/newImage/imgLazyLoading.svg",
        adapter: {
          error(listender, options) {
            if (typeof listender.el.dispatchEvent === "function") {
              listender.el.dispatchEvent(new Event("error"))
            }
          }
        }
      })

      var app = new Vue({
        el: "#app",
        name: "App",
        vuetify: new Vuetify(),
        components: {
          footerNav: footerNav,
          navHeaderTable: navHeaderTable,
          useInput,
          TotalAmount,
          VersionTag,
          // 'take-away-sub-dia': TakeAwaySubDia
          "take-away-store-info": TakeAwayStoreInfo,
          "alter-pickup-time-dia": AlterPickupTimeDia,
          "pickup-iceptor-dia": PickupIceptorDia,
          "vuetify-base-dia": VuetifyBaseDia,
          "food-component": FoodComponent,
          "bottom-sheet": BottomSheet,
          ReviewOrderPage
        },

        mixins: [
          SendOrderFormMixins,
          SnackBar,
          accountMixin,
          registerAndResetMixin,
          globalStoreMixin,
          SSEControlMixin
        ],
        data: {
          rootFontSize: document.documentElement.style.fontSize.split("px")[0] || 40,
          that: null,
          currencyWay: "",
          defaultWay: "$",
          showFoodWarp: false,
          openTable: null,
          tabIsActive: null,
          systemLanguage: {},
          lanSele: [
            {
              label: "i18nLocaleEn",
              value: "en"
            },
            {
              label: "i18nLocaleZh",
              value: "zh"
            }
          ],
          allDataList: [],
          merge_fty: {}, //需要合并的foodType 的fcode
          merge_codes: [],
          fty_data: {},
          disableSendOrder: false, //是否限制送单按钮
          foodList: [], // 食品内容页
          shopCartList: [], // 购物车数组
          // 抛物线
          count: 0,
          balls: [
            //小球 设为3个
            {
              show: false,
              imgLink: ""
            },
            {
              show: false,
              imgLink: ""
            },
            {
              show: false,
              imgLink: ""
            }
          ],
          dropBalls: [],
          nameRoll: false,
          foodInfoItem: {},
          allshoplNumber: 0, // 监听购物车总数，
          foodInfodetails: [], // 细项
          tipTxt: "",
          showLittleDIV: false, // 细项是否显示
          // 本地细项
          localfoodListArry: [],
          localmlistListArry: [],
          localmtypeListArry: [],
          localfoodTypeListArry: [],
          foodBaseUrl: null,
          loading: false,
          numberDishes: 0, // 菜品数量限制,
          isShowNumberDishes: true,
          configureImgUrl: {}, // 过敏源数据
          fixed: true,
          contentStartY: 0,
          contentStartX: 0,

          checkVal: [],
          // checkradio: '',
          listSelect: [],
          clickLayout: 1, // 容器布局切换
          contentWarpStyle: {
            0: "content_warp_zero",
            1: "content_warp_one",
            2: "content_warp_two",
            3: "content_warp_third",
            6: "content_warp_six"
          },
          ftSwitch: true, //fty页面打开/关闭标识
          ftyBaseUrl: "", // fty首页图片baseurl
          showCartTab: false, // 是否显示购物车页面
          localXiItem: [], // 细项中转展示
          clickXiItem: [], // 追加套餐第一层点击得细项
          clickXiOutItem: "", // 点击的外层type(用于max,min)
          clickXiType: "", // 点击得类型
          editXiItem: [], // 点击的细项
          localAddSMXiFtyArry: [], // 追加套餐fty下各个food
          localAddSMXiMtyArry: [], // 追加套餐mty下各个mlist
          foodDialogIndex: null,
          cartEditXi: false,
          clickCartEditIndex: null,
          joinType: "",
          yetCode: [], // 购物车修改细项前的code
          discountCode: "",
          effectiveNum: "",
          baseUrl: "",
          imgTimeStamp: "",
          // 追加套餐第二层
          localSecAddSMXiFtyArry: [],
          localSecAddSMXiMtyArry: [],
          secondDialogIndex: "",
          myXiClickXiItem: "",
          myXiClickXiType: "",
          myXiEditXiItem: "",
          myXiClickXiOutItem: "",
          addDataState: "", // fromOption
          secLocalXiItem: [],
          InvalidFtyFty: [], //不符合的fty
          InvalidFood: [], //不符合时间的food
          tabData: {}, //点击tab传递的数据
          addShowInFcodeState: false, // showInFcode判断选项卡数量是否增加上级数量
          secAddShowInFcodeState: false, // 第三层showInFcode判断选项卡数量是否增加上级数量
          showInFCodesQtyList: [],
          showInFTCodesQtyList: [],
          copyLocalFtyArry: [],
          localAllCode: [],
          oldLocalAllCode: {},
          twoDiaAllCode: {},
          resetAddNumArry: [],
          showErrorData: false,
          preOrderObj: {
            tableNum: "",
            tableKey: ""
          },
          preOrderDiaIndex: "",
          scrollerHeight: "",
          hotSaleData: [],
          serviceCharge: 0, // 服务费
          choosePayMethod: "",

          payAtCashierNum: "",
          initTimePickerState: false, // 初始化时间选择器状态
          giveAwayFcodeList: [], //赠送Fcodes列表
          giveAwayFoodList: [], //对应giveAwayFcodeList的food列表
          excludePrice: 0, //购物车需要排除的价格
          useGiveAwayList: [], ///生效的赠送list
          giveAwayExcludeCodeList: [], //赠送的食品排除列表
          confirmDiscountCode: "", //确认的折扣码
          discountCompulsory: null, //默认折扣码下强制折扣,不允许取消
          discountedPrice: 0, //打折后价格
          discountTimer: null, //用于连续请求折扣的定时器 防抖
          debounceTime: "", //节流阀
          searchListLoading: false,
          machFoodNameList: [], //搜索food名字数据
          searchFoodDiaIndex: "", //搜索food弹窗索引
          showSendOrderView: false, //是否显示订单提交页面
          fontSizeOption: [], //字体大小选项
          storePhotoUrl: "", //店铺图片(外卖模式最后订单弹窗使用)
          memberCenterLogo: "", //个人中心图片
          defaultOss: "https://appwise.oss-cn-hongkong.aliyuncs.com", //默认的图片服务器oss地址
          showPickupDia: false, //是否显示外卖模式切换预约时间的警告弹窗
          backupStoreData: {}, //备份的店铺店铺数据
          invalidShopCartList: [], // 无效的购物车数据
          showInvalidPickupDia: false, //是否显示预约时间无效弹窗
          invalidPickupMsg: "", // 预约时间无效的提示
          showFoodCourtView: false, //是否显示美食广场店铺列表
          reOpenTLoading: false, //请求openTable的loading
          shopCartSourceList: [], // 购物车food对应的源数据: 应新增FoodCourt模式,要用购物车源数据快速回显修改food
          storeSearchVal: "", //美食广场模式搜素值
          foodCourtStoreList: [], //美食广场模式所有的店铺<interface>
          reOpenTStoreN: "", // 请求openTable店铺的storeNumber
          openTableMode: {}, // 开台模式
          invalidPickupMsg: "", // 预约时间无效的提示
          additionalItemsForTakeawayAutoList: [], // 返回的餐具auto数据
          additionalItemsForTakeawayFixedList: [], //返回的固定餐具的数据
          selectedAdditionalItemsForTakeawayMap: {
            FIXED: {},
            AUTO: {}
          }, //已选的餐具
          selectedAdditionalItemsForTakeawayFixed: [],
          infiniteLoopScroll: () => {},
          orderTimerStatus: null,
          orderDeadline: "",
          orderTimeDynamicConfig: [],
          orderStartTime: null,
          queuedObj: {
            isOrderStop: false,
            isOrderWait: false,
            orderWaitObj: {
              waitTxt: "",
              waitTime: []
            },
            queueTime: 0,
            lastUpdatedTime: 0
          }, //订单排队对象
          isUserLayerOpen: false, //是否已经打开登录用户窗口
          isDisabledWallet: false, //是否禁用钱包支付
          ignoreAutoDiscount: false, //是否忽略自动折扣
          itemCtrlUpdateTime: null,
          oisNowDateTime: "", //用于判断当前日期对应PRICE1-8生效价格字段
          menuSourceList: [], //menu list 未合并前数据
          menuMustItemMap: {}, //所有itemCtrlModel=1的code map
          menuDescendantMap: {}, // 所有菜单数据的后代结构,用于更新ItemCtrl
          historyOrders: {}, // 历史点单记录
          backupEditItem: null //备份的点击编辑的购物车item
        },

        created() {
          // staff mode 第二页面进入会重置session中的url,但local中的是
          let localIndexPageUrl = localStorage.getItem("indexPageUrl")
          if (localIndexPageUrl) {
            sessionStorage.setItem("indexPageUrl", localIndexPageUrl)
          }
          this.imgTimeStamp = moment().valueOf() // 图片共用一个时间戳读取缓存
          this.initializeThe() //初始化数据
          this.initFoodCourtMode() // 美食广场模式
          this.fixLan() //固定语言
          // this.timer() //计时器启动
          moment.suppressDeprecationWarnings = true // 关闭 moment警告
          if (this.openTable.infiniteLoop) {
            this.infiniteLoopScroll = throttle(this.onScroll, 20, {
              leading: false,
              trailing: false
            })
          }
        },
        mounted() {
          $("html").css({ "--styleColor": this.openTable.color })
          this.saveFontSize() // 保存字体大小
          this.keepPageHeight() // 处理软键盘弹起顶起页面
          this.mountedAboutTimePicker() //初始化时间选择器
          this.payFailBackOrderView()
          this.enableHorizontalScroll()
          window.addEventListener("beforeunload", this.closeSSE)
        },
        computed: {
          //主food是否限制加入购物车数量为1
          isLimitAddCartNumToOne() {
            return item => {
              return item.maxSingleQuantityToCart == 1 && item.qty1 == 1
            }
          },
          // 是否禁用操作
          isActionDisabled() {
            return (
              item,
              preAddNum = 0, //预增加数量
              source = "normal", //来源
              hasShowTip = false, //是否显示弹窗提示
              allowFilter = true, //是否允许过滤自身数据(用于编辑购物车跳转详情页面过渡时,不因为数据变化导致按钮样式闪变样式)
              targetBtn = null //事件触发目标按钮
            ) => {
              return this.checkMaxFoodNum(
                item,
                preAddNum,
                source,
                hasShowTip,
                allowFilter,
                targetBtn
              )
            }
          },
          itemFoldable() {
            return !!this.openTable.categoryFoldable
          },
          getPriceName() {
            //根据H先行判断
            let { priceWay: { defaultPrice = "PRICE1", use_dow = [] } = {} } = this.openTable
            let dowPriceItem = null
            let currentTimeArry = this.oisNowDateTime.trim().split(" ")
            let nowCurrentDate = currentTimeArry[0]

            if (use_dow[0] && use_dow[0].price != "") {
              //存在H排序优先判断
              use_dow.sort((a, b) => {
                const aHasH = a.dow.includes("H")
                const bHasH = b.dow.includes("H")

                if (aHasH && !bHasH) return -1 // a含H，b不含H，a排前面
                if (!aHasH && bHasH) return 1 // a不含H，b含H，b排前面
                return 0 // 两者都含H或都不含H，保持原顺序
              })
              dowPriceItem = use_dow.find(item => {
                let dow = item.dow.join("") //将dow转换为字符串
                return this.checkUseDowRes(dow, null, nowCurrentDate)
              })
            }
            return dowPriceItem ? dowPriceItem.price : defaultPrice
          },
          // 第一层级的固定细项
          firstLevelFixedItem() {
            this.foodInfoItem
          },
          showCRM() {
            const { displayCRM, memberConfig } = this.openTable
            if (!displayCRM) return false
            // let { takeawayDiscountSwitch, discountSwitch } = memberConfig
            // takeawayDiscountSwitch = discountSwitch ?? true
            const { dineInMemberLoginSwitch = true, takeawayMemberLoginSwitch = true } = displayCRM
            if (this.isTakeAway) return takeawayMemberLoginSwitch
            return dineInMemberLoginSwitch
          },
          // 购物车 主food存在tax字段
          existTaxes() {
            const rate = this.hasProperty("billTax", "billTaxRate")
            if (rate) return true
            return this.includedTax
          },
          // 购物车是否有商品含税
          includedTax() {
            return this.shopCartList.some(it => {
              return it.tax1 || it.tax2 || it.tax3
            })
          },
          //是否启用外卖盒数据
          useTakeAwayPackag() {
            let { takeAwayPackaging = false } = this.openTable
            return takeAwayPackaging && this.isTakeAway
          },
          allPriceCache() {
            this.shopCartList
            return this.getAllFoodPrice()
          },
          // 开启下单倒计时
          showOrderCountdown() {
            //  在获取到food数据后在初始化倒计时,使用allDataList来判断是否初始化成功
            let { performType, orderTimeLimit = [] } = this.openTable
            return (
              performType === 1 &&
              this.orderStartTime &&
              orderTimeLimit.length &&
              this.allDataList.length
            )
          },
          // 是否开启滚动food样式
          rollingFood() {
            return this.openTable.rollingFoodPicture
          },
          rollFoodBigPic() {
            return this.openTable.rollingFoodBigPicture
          },
          tabDataList() {
            if (this.openTable.combinedNavigation) {
              let { tab1, tab2 } = this.openTable.combinedNavigation
              // 获取所有被合并的codes和盒子(包括本身)
              const { allCodes = [], allItems = [] } = Object.entries(this.merge_fty).reduce(
                (pre, cur) => {
                  const [box, items] = cur
                  const boxItem = this.allDataList.find(el => el.code === box)
                  const mergeItems = this.allDataList.filter(el => items.includes(el.code))
                  const codes = [box, ...items]
                  pre.allCodes = Array.isArray(pre.allCodes) ? pre.allCodes : []
                  pre.allItems = Array.isArray(pre.allItems) ? pre.allItems : []
                  pre.allCodes = [...new Set(pre.allCodes.concat(codes))]
                  pre.allItems = pre.allItems.concat([boxItem, ...mergeItems])
                  return pre
                },
                {}
              )
              // 当tab1为other,都是将盒子放在最前面
              if (tab1 === OTHER) {
                const afterNoMerge = this.allDataList.filter(type => !allCodes.includes(type.code))
                // 显示全部; 被合并的放在前面,剩余放后面
                return [...allItems, ...afterNoMerge]
              } else {
                // 排序所有的fty,让被合并的fty移动至盒子的后面
                let list = this.allDataList.filter(e => e.code)
                let boxCodes = Object.keys(this.merge_fty)
                // 先排盒子的顺序
                let mergeMap = this.allDataList
                  .filter(e => boxCodes.includes(e.code))
                  .reduce((pre, cur) => {
                    pre[cur.code] = this.merge_fty[cur.code]
                    return pre
                  }, {})
                let mergeCodes = allCodes.filter(e => !boxCodes.includes(e))
                let noMergeItemList = this.allDataList.filter(e => !mergeCodes.includes(e.code))
                // 在盒子后添加合并type
                for (const bc in mergeMap) {
                  let boxIndex = noMergeItemList.findIndex(e => e.code === bc)
                  let codes = mergeMap[bc]
                  let items = this.allDataList.filter(e => codes.includes(e.code))
                  noMergeItemList.splice(boxIndex + 1, 0, ...items)
                }
                return noMergeItemList
              }
            } else {
              return this.allDataList
            }
          },
          // 所有的附加餐具列表
          allAdditionalItemsForTakeawayList() {
            return this.additionalItemsForTakeawayAutoList.concat(
              this.additionalItemsForTakeawayFixedList
            )
            // .filter(el => !this.selectedAdditionalItemsForTakeawayMap.FIXED[el.fCode])
          },

          //判断何时需要重新请求折扣
          whetherToCalculateDiscount() {
            // 若已登录会员|| 存在生效折扣码 , 则每次更变数据都要重新计算折扣
            // warn:仅在购物车页才计算折扣码
            return (
              (this.LoginValid() ||
                this.loginStatus ||
                !!this.confirmDiscountCode ||
                this.existTaxes) &&
              !this.closeDiscount()
            )
          },
          // 是否送单时显示店铺照片信息/Takeaway提交弹窗
          showTakeAwaySubDia() {
            const { closeTakeawayShopPhoto, performType } = this.openTable
            return performType === 2 && this.isTakeAway && !closeTakeawayShopPhoto
          },
          isTakeAway() {
            return this.openTable.tableNumber === "TAKEAWAY"
          },
          isTestTable() {
            return this.openTable.tableNumber.toUpperCase() === "TEST"
          },
          showHotSaleWarp() {
            let hotSaleDataLen = this.hotSaleData.length
            let { hotSaleNum } = this.openTable
            // console.log(hotSaleDataLen && Number(hotSaleNum))
            return hotSaleDataLen && Number(hotSaleNum)
          },

          // 显示版本标识: PROD UAT History
          versionTag() {
            let { versionNumber } = this.openTable
            switch (versionNumber) {
              case "0":
                return "UAT"
              case "PROD":
                return "PROD"
              default:
                return "HISTORY"
            }
          },
          // 排序付款方式
          orderPayMethod() {
            // 根据orderPayMethod顺序,找到payTypeObj对应的对象
            let newArr = []
            this.getAllPayMethod.forEach(item => {
              let payTypeObj = this.payTypeObj.find(e => e.value == item)
              if (payTypeObj) newArr.push(payTypeObj)
            })
            return newArr
          },
          getAllPayMethod() {
            let { payType } = this.openTable
            if (!payType) return []
            let { pos = [], eft = [], boc = [], solely = [], windcave = [] } = payType
            // 判断 boc 是否有数据，如果有则不加入 boc 数组，直接写死加入 'boc'
            const orderPayMethod = Object.keys(payType).reduce((acc, cur) => {
              if (cur === "boc" && boc.length > 0) {
                return [...acc, "boc"]
              } else {
                return [...acc, ...payType[cur]]
              }
            }, [])
            return orderPayMethod
          },
          isShowGoHomeBtn() {
            let { isDiningStyleMode, initialTableNum } = this.openTable //自定义堂食/外卖模式
            let specialMode = ["AssistMode", "StaffMode", "EnhAssistMode"].includes(initialTableNum)
            return specialMode || isDiningStyleMode
          },
          contentWarpLayoutclass() {
            let map = ["zeroLayout", "oneLayout", "twoLayout", "threeLayout", "sixLayout"]
            return map[+this.tabData.display_column] || ""
          },
          // 给shopCartList根据店铺分组,用于模板渲染
          shopCartGroup() {
            //绑定sseCartVersion有更改则重新排序
            this.sseCartVersion
            return this.shopCartList.reduce((pre, cur) => {
              if (cur.storeNumber in pre) {
                pre[cur.storeNumber].push(cur)
                pre[cur.storeNumber].sort((a, b) => {
                  const isNumA = typeof a.index === "number" && !isNaN(a.index)
                  const isNumB = typeof b.index === "number" && !isNaN(b.index)
                  if (isNumA && isNumB) {
                    return a.index - b.index
                  } else if (isNumA) {
                    return -1
                  } else if (isNumB) {
                    return 1
                  } else {
                    return 0
                  }
                })
              } else {
                pre[cur.storeNumber] = [cur]
              }
              return pre
            }, {})
          },
          // 用于渲染到页面的店铺数据<已验证时间,已排序大->小,已搜索筛选>
          courtStoreList() {
            let list = this.foodCourtStoreList.filter(store => {
              let { openingDates, openingHours, storeName, foodCourtUsable, timeZone } = store
              let formatTimeZone =
                (timeZone && timeZone.replaceAll("-", "/").split("[")[0]) || new Date()
              let isOperation =
                foodCourtUsable &&
                verificationTimePeriod(openingDates, openingHours, true, formatTimeZone)
              return (
                isOperation &&
                storeName[this.openTable.language]
                  .toUpperCase()
                  .includes(this.storeSearchVal.trim().toUpperCase())
              )
            })
            return this.sortFoodCourtStore(list)
          },
          // 根据foodCourtColumn判断应该使用的类名
          foodCourtColumnClassName() {
            switch (this.setFoodCourtConfig("column")) {
              case 1:
                return "one_column"
              case 2:
                return "two_column"
              case 3:
                return "three_column"
              default:
                return "one_column"
            }
          },
          isFoodCourtMode() {
            return this.openTableMode.mode === "FoodCourt"
          },
          isPosPayMethod() {
            let posMethodArr = ["payAtCashier", "wallet"]
            return posMethodArr.includes(this.choosePayMethod)
          },
          customerPhoneRules() {
            let {
              logCustomerPhone: { phoneRequired } = {} // 默认值为空对象
            } = this.openTable
            return phoneRequired || this.choosePayMethod == "wallet"
          },
          // 存在必填个人信息
          hasRequiredPersonalInfo() {
            let { nameMandatory, emailMandatory } = this.openTable
            return this.customerPhoneRules || nameMandatory || emailMandatory
          },
          // 是否禁用提交订单页的电话信息输入框
          isDisabledSendFormPhone() {
            return ["payAtCashier", "wallet"].includes(this.choosePayMethod)
          }
        },
        //
        watch: {
          shopCartList(newVal, oldVal) {
            let allshoplNumber = 0
            newVal.forEach(item => {
              allshoplNumber += item.qty1
              // 此标识用于操作购物车时查找索引
              item.unique = Symbol(item.storeNumber)
            })
            this.allshoplNumber = allshoplNumber
            // console.log(newVal,'新改变的shoplist')

            // 赠送food逻辑
            this.initGiveAway()

            // 若当前存在折扣码 返回新的价格
            if (newVal.length) {
              this.checkDiscountAgain()
            } else {
              //清空折扣/税率信息
              this.discountGroup = []
              this.taxesGroup = []
              this.cartEditXi = false
            }
            // 重置忽略自动折扣(用于checkCode接口异常下是否启动校验自动折扣)
            this.ignoreAutoDiscount = false
            // 购物车更新时,更新itemCtrl状态
            this.updateCartItemCtrl()
            //收集购物车更改记录
            this.collectCartSyncParam()
            // 只锁一次购物车更改
            this.cartSyncLocked = false
          },
          showSendOrderView(v) {
            // sendorder頁面 初始化數據
            // 判斷在購物車是否已經填入折扣碼,存在這回顯
            // this.sendOrderForm.discountCode = this.confirmDiscountCode || ""
            this.discountRelated.isShowRemoveIcon = !!this.confirmDiscountCode
            if (this.$refs.sendOrderForm) {
              this.$refs.sendOrderForm.resetValidation()
            }
            if (v) {
              this.$nextTick(() => {
                document.querySelector(".v-main").scrollTop = 0
              })
            }
          },
          // 选择的支付方式
          choosePayMethod(val, oldVal) {
            this.sendOrderForm.choosePayMethod = val
            // 若选择ipay88 则将agreement 恢复默认
            if (val == "iPay88") {
              this.sendOrderForm.agreement = false
              this.useAgreementBtn = true
            } else if (val == "payAtCashier") {
              this.clearDiscountTimer()
              // 保存临时填写的sendOrderForm数据
              sessionStorage.setItem("tempSendOrderForm", JSON.stringify(this.sendOrderForm))
              // 若选择payAtCashier将sendOrderForm中的数据恢复默认
              this.discountRelated.isShowDiscountErrorMsg = false
              this.removeDiscount()
              this.sendOrderForm = {
                ...this.sendOrderForm,
                email: "",
                name: "",
                phone: "",
                areaCode: this.defaultAreaCode,
                discountCode: "",
                incomingMail: false,
                agreement: false
              }
              this.$refs.sendOrderForm.resetValidation()
              this.checkPayMethodConflictsWithPickup()
              // 若已登录会员,或有税率或开启了显示服务费也要重新请求
              let { serviceCharges: { displayInShoppingCartPage } = {} } = this.openTable
              if (this.existTaxes || this.LoginValid() || displayInShoppingCartPage) {
                this.onRequestDiscount()
              }
            } else {
              if (this.openTable.emailMandatory) {
                //重新覆盖sendOrderForm中的数据
                this.sendOrderForm.incomingMail = true
              }
            }
            if (oldVal == "payAtCashier") {
              // 回显填写的sendOrderForm数据
              const tempSendOrderForm = sessionStorage.getItem("tempSendOrderForm")
              if (tempSendOrderForm) {
                const currentSendOrderForm = JSON.parse(tempSendOrderForm)
                this.sendOrderForm = {
                  ...currentSendOrderForm, // 将currentSendOrderForm中的字段覆盖到sendOrderForm中
                  areaCode: currentSendOrderForm.areaCode || this.defaultAreaCode, //避免areaCode为空
                  incomingMail: this.openTable.emailMandatory
                    ? true
                    : currentSendOrderForm.incomingMail // 保持incomingMail字段不变
                }
              }
              // 从柜台支付离开,默认请求一次折扣,可能有减满折扣
              this.checkDiscountAgain()
            }
            if (val == "wallet") {
              let userLogged = Cookies.get("userLogged") !== undefined
              let memberInfo = Cookies.get("memberInfo")
              if (!userLogged) {
                this.sendOrderForm.phone = "" //抹掉电话号码
              }
              if (!this.sendOrderForm.areaCode) {
                this.sendOrderForm.areaCode = this.defaultAreaCode
              }
              if (memberInfo && userLogged) {
                let { telephone } = JSON.parse(memberInfo)
                this.sendOrderForm.phone = telephone
              }
            }
          },
          showFoodCourtView(val) {
            // 每次打开页面时,将购物车按钮的背景颜色设为初始值
            if (val) {
              document.documentElement.style.setProperty(
                "--styleColor",
                this.setFoodCourtConfig("color")
              )
            }
            sessionStorage.setItem("showFoodCourtView", val)
          },
          // 固定餐具数量改变也要请求折扣码
          selectedAdditionalItemsForTakeawayFixed: {
            deep: true,
            handler(val, old) {
              if (old.length) {
                // 赠送food逻辑
                this.initGiveAway()
                const fillDiscount = this.checkDiscountExclude()
                if (this.whetherToCalculateDiscount || fillDiscount) {
                  this.checkDiscountAgain()
                }
              }
            }
          },
          selectedAdditionalItemsForTakeawayMap: {
            deep: true,
            handler(val, old) {
              // 已选择的可增加数量的附加项目
              this.selectedAdditionalItemsForTakeawayFixed =
                this.additionalItemsForTakeawayFixedList.filter(el => {
                  // qty1===0,则为新增
                  if (val.FIXED[el.fCode] && el.qty1 === 0) {
                    el.qty1 = 1
                  }
                  return val.FIXED[el.fCode]
                })
              // 赠送food逻辑
              this.initGiveAway()
              const fillDiscount = this.checkDiscountExclude()
              if (this.whetherToCalculateDiscount || fillDiscount) {
                this.checkDiscountAgain()
              }
            }
          }
        },
        updated() {
          if (this.enableSSE) {
            debounce(this.dispatchActivity, 200)
          }
        },
        methods: {
          /**
           * 1. 仅更改数量:
           *    - add :     Math.max(编辑后数量-编辑前数量 + 购物车数量,0)
           *    - delete:  Math.max(Math.min(有效数量,编辑后数量) + 购物车数量 - 有效数量,0)
           *    - no change: Math.max(购物车数量,0)
           * 2. 仅更改细项：需要oldFood
           *    - 购物车剩余数量 =   Math.max(购物车数量 - 有效数量,0)，注意减去有效数量的对应idSet
           *      - 大于0: 需要忽略此次购物车同步
           *      - <=0 :  购物车数据被删除,弹窗提醒
           *    - 新增(编辑)的数量 =  有效数量
           *      -  oldFood为编辑前的数据,注意，oldFood的addIdSet长度===编辑前数量
           * 3.  更改细项及数量
           *    - 购物车剩余数量  =  购物车数量 - 有效数量
           *    - add：    新增(编辑)的数量 = Math.max(有效数量,编辑后数量-编辑前数量+有效数量)
           *    - delete:   新增(编辑)的数量  = Math.min(有效数量,编辑后数量)*/
          //确认编辑item时,校验数据
          verificationEditItem(index, foodItem) {
            const addIdSet =
              index === -1
                ? []
                : this.shopCartList[index].addIdSet.concat(this.backupEditItem.addIdSet || [])
            const deleteIdSet =
              index === -1
                ? []
                : this.shopCartList[index].deleteIdSet.concat(this.backupEditItem.deleteIdSet || [])
            // 购物车item的数量
            const cartEfficientIdSet = this.removeEqualId(addIdSet, deleteIdSet)
            // cartItem与editItem的addIdSet 交集
            const efficientIdSet = this.backupEditItem.addIdSet.filter(it =>
              cartEfficientIdSet.includes(it)
            )
            // 当前编辑item的有效数量
            const efficientQty = index === -1 ? 0 : efficientIdSet.length
            // 编辑的更改数量差值
            const newAddQty = foodItem.qty1 - this.backupEditItem.qty1
            // 购物车item的数量
            const cartItemQty = cartEfficientIdSet.length

            const currentHash = this.fnv1a(
              stableStringify(this.getOrderStructure(this.getFoodSelectedTree(foodItem)))
            )
            const oldLastRecord =
              this.backupEditItem.hashRecord[this.backupEditItem.hashRecord.length - 1]
            const oldHash = oldLastRecord.hash
            // 编辑有细项更新
            const hashChanged = currentHash !== oldHash
            // 编辑有数量更新
            const qtyChanged = newAddQty !== 0

            //未更新，以购物车为准
            if (!qtyChanged && !hashChanged) return false

            const cartItemRecord = index === -1 ? [] : this.shopCartList[index].hashRecord

            const cartItemUpdated =
              stableStringify(this.backupEditItem.hashRecord) !== stableStringify(cartItemRecord)

            const update = (cartItemRemainingQty, editItemQty) => {
              if (!cartItemUpdated) return true
              //更新购物车item
              if (cartItemRemainingQty) {
                this.shopCartList[index].addIdSet = moveSubarrayToEnd(
                  this.shopCartList[index].addIdSet,
                  efficientIdSet
                )
                this.shopCartList[index].qty1 = cartItemRemainingQty

                this.updateHashRecord(this.shopCartList[index])
              } else {
                // 移除购物车item
                if (index !== -1) {
                  this.shopCartList.splice(index, 1)
                  sessionStorage.setItem("shopCartList", JSON.stringify(this.shopCartList))
                  this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList"))
                }
              }
              sessionStorage.setItem("shopCartList", JSON.stringify(this.shopCartList))
              this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList"))

              //更新编辑item
              if (!editItemQty) return false

              const oldRecord = {
                hash: this.fnv1a(oldLastRecord.hash),
                qty1: oldLastRecord.qty1
              }
              this.foodInfoItem.hashRecord = [oldRecord]
              this.foodInfoItem.qty1 = editItemQty
              const cartHashRecord = {
                data: {
                  ...this.getFoodSelectedTree(this.backupEditItem),
                  addIdSet:
                    index === -1
                      ? []
                      : this.removeEqualId(this.backupEditItem.addIdSet, deleteIdSet),
                  deleteIdSet: this.backupEditItem.deleteIdSet || [],
                  index: this.backupEditItem.index
                },
                record: [oldRecord]
              }
              this.cartHashRecord.push(cartHashRecord)
              this.joinType = ""
              this.joinCartF()
              return false
            }

            // 编辑后cartItem剩余数量
            const cartItemRemainingQty = Math.max(cartItemQty - efficientQty, 0)
            //仅更新数量
            if (qtyChanged && !hashChanged) {
              this.foodInfoItem.qty1 =
                newAddQty > 0
                  ? newAddQty + cartItemQty
                  : Math.min(efficientQty, foodItem.qty1) + cartItemQty.qty1 - efficientQty

              return index !== -1 ? true : update(cartItemRemainingQty, this.foodInfoItem.qty1)
            }
            // 仅更新细项
            if (!qtyChanged && hashChanged) {
              return update(cartItemRemainingQty, efficientQty)
            }
            const editItemQty =
              newAddQty > 0
                ? Math.max(efficientQty, newAddQty + efficientQty)
                : Math.min(efficientQty, foodItem.qty1)
            return update(cartItemRemainingQty, editItemQty)
          },
          // 编辑加入购物车时,校验是否可以替换
          getEditItemCartIndex() {
            if (
              !this.backupEditItem ||
              !this.backupEditItem.hashRecord ||
              !this.backupEditItem.hashRecord.length
            )
              return -1

            const record = this.backupEditItem.hashRecord[this.backupEditItem.hashRecord.length - 1]
            const index = this.findIndexCartEqualHashItemByAllRecord(this.shopCartList, {
              hash: record.hash,
              data: { ftCode: this.foodInfoItem.ftCode }
            })
            if (index !== -1 && !this.enableSSECartSync) {
              this.clickCartEditIndex = index
              return index
            }
            const intersection = (arr1 = [], arr2 = []) => {
              const res = []
              let a1 = new Set(arr1)
              let a2 = new Set(arr2)
              a1.forEach(item => {
                if (a2.has(item)) {
                  res.push(item)
                }
              })
              return res
            }

            // 若idSet与cartList的idSet有交集,那么则为此editItem的历史数据
            const newIndex = this.shopCartList.findIndex(it => {
              // 若编辑item与购物车item的addIdSet有交集,则视为editIndex存在
              const res = intersection(this.backupEditItem.addIdSet, it.addIdSet)
              return !!res.length
            })
            if (newIndex !== -1) {
              this.clickCartEditIndex = newIndex
              return newIndex
            }
            return -1
          },

          getHistoryOrder() {
            if (!this.openTable.performType === 1 || !this.openTable.displayCartOrderRecord)
              return Promise.resolve()
            return new Promise((resolve, reject) => {
              $.ajax({
                url: "../store/loadTableOrder",
                data: {
                  companyName: this.openTable.companyName,
                  tableNumber: this.openTable.tableNumber,
                  storeNumber: this.openTable.storeNumber,
                  performType: this.openTable.performType,
                  tableKey: this.openTable.tableKey
                },
                xhrFields: {
                  responseType: "json"
                },
                success: res => {
                  if (res.statusCode === 200) resolve(res)
                  else reject(res)
                },
                error: error => reject(error)
              })
            })
          },
          formatHistoryOrderData(data) {
            const { oldOrderItem = [] } = data

            const res = groupBy(
              oldOrderItem.toSorted((a, b) => {
                if (a.createTime > b.createTime) {
                  return -1
                }
                if (a.createTime < b.createTime) {
                  return 1
                }
                return 0
              }),
              it => it.createTime
            )
            this.historyOrders = Object.freeze(res)
          },
          // 判断是否为响应式
          isReactive(obj, prop) {
            const descriptor = Object.getOwnPropertyDescriptor(obj, prop)
            return (
              descriptor &&
              typeof descriptor.get === "function" &&
              typeof descriptor.set === "function"
            )
          },

          foldType(typeItem, level = 0) {
            if (!this.itemFoldable) return
            const selector = [".food_info_warp #one_", "#myxiDialog #two_", "#secXiDialog #three_"]
            const targetCheckout = document.querySelector(selector[level] + typeItem.code)
            if (targetCheckout) {
              targetCheckout.click()
            }
          },
          initFoldType(level = 0) {
            // 复原状态为展开
            const list = [
              "#myxiDialog .fold-food-type-item input",
              "#secXiDialog .fold-food-type-item input"
            ]
            list.slice(level).forEach(s => {
              const items = document.querySelectorAll(s)
              items.forEach(it => (it.checked = true))
            })
          },
          //是否满足type的最大选择数量
          satisfyMaxQty(typeItem) {
            let maxQty = typeItem.maxQty || Infinity
            let listName = typeItem.typeName == "mtyItem" ? "mListList" : "foodList"
            let num = 0
            typeItem[listName].forEach(item => {
              if (item.selected) num += item.qty1 * (item.takeUpQty || 1)
            })
            return num >= maxQty
          },
          selectedItemInType(typeItem) {
            let listName = typeItem.typeName == "mtyItem" ? "mListList" : "foodList"
            return typeItem[listName].filter(it => it.selected).map(it => this.inListTitle(it))
          },

          //判断是否开启横向布局滚动监听
          enableHorizontalScroll() {
            if (this.openTable.infiniteLoop && !this.openTable.verticalOrderLayout)
              this.$refs.contentScrollContainer.addEventListener(
                "scroll",
                this.infiniteLoopScroll,
                {
                  passive: true
                }
              )
          },
          //所有自动满减折扣
          getAllFullDiscountList() {
            if (
              Array.isArray(this.openTable.promotionDiscountList) &&
              this.openTable.promotionDiscount
            ) {
              let currentTime = moment().format("YYYY/MM/DD HH:mm")
              let timeStamp = moment().valueOf()
              let [nowCurrentDate, nowCurrentTime] = currentTime.trim().split(" ") //獲取系统时间 2020/09/16
              return this.openTable.promotionDiscountList.filter(item => {
                let { use_dow = "", code, use_date = "", use_time = "" } = item
                let accordDate =
                  !use_date || this.checkUseDateRes(use_date, timeStamp, nowCurrentDate)
                let accordDow = !use_dow || this.checkUseDowRes(use_dow, timeStamp, nowCurrentDate)
                let accordTime =
                  !use_time ||
                  this.checkUseDowAndTime(
                    use_time,
                    timeStamp,
                    nowCurrentTime,
                    accordDow,
                    nowCurrentDate
                  )

                let resRequest =
                  code.toUpperCase() == "TAKEAWAY" && this.openTable.tableNumber != "TAKEAWAY"
                return accordDate && accordDow && accordTime && !resRequest
              })
            }
            return []
          },
          //点击确认按钮触发的折扣码请求
          onConfirmDiscount() {
            sessionStorage.setItem("couponUseType", "custom")
            this.sendPromotionDiscountTime = false //手动不需要发送优惠券时间戳
            this.onRequestDiscount()
          },
          sortPromotionDiscount() {
            let hasDiscountList =
              Array.isArray(this.openTable.promotionDiscountList) &&
              this.openTable.promotionDiscountList.length
            if (this.openTable.promotionDiscount && hasDiscountList) {
              let list = this.openTable.promotionDiscountList.map(it => {
                return { amount: 0, ...it }
              })
              this.openTable.promotionDiscountList = list.sort((a, b) => b.amount - a.amount)
            }
          },

          //校验减满折扣
          checkDiscountExclude() {
            let list = this.getAllFullDiscountList()
            if (list.length) {
              let allPrice = this.allPriceCache
              let preChecklist = list.filter(e => {
                let { amount, conflict_fCodes = "" } = e //conflict_fCodes为当前折扣码冲突的fcode
                let conflictfCodesArr = conflict_fCodes.split(";")
                let additionalItemsList = [
                  ...this.getSelectedAdditionalItemsData(true),
                  ...this.getSelectedAdditionalItemsData(false)
                ]
                let additionalItemsCodeArr = additionalItemsList.map(e => e.fCode)
                let hasConflict = additionalItemsCodeArr.some(e => conflictfCodesArr.includes(e)) //与additionalItemsForTakeawayFixedList/additionalItemsForTakeawayAutoList里的数据不冲突

                return allPrice >= amount && !hasConflict
              })
              let targetDiscount = null
              for (let i = 0; i < preChecklist.length; i++) {
                let {
                  inoperative_fCodes = "",
                  inoperative_ftCodes = "",
                  inoperative_mlCodes = "",
                  inoperative_mtCodes = "",
                  amount
                } = preChecklist[i]
                // calculatedTotal 计算单个价格
                let excludedPrice = this.getAllFoodPrice(null, [
                  {
                    fcode: inoperative_fCodes.split(";").filter(e => e),
                    ftcode: inoperative_ftCodes.split(";").filter(e => e),
                    mlcode: inoperative_mlCodes.split(";").filter(e => e),
                    mtcode: inoperative_mtCodes.split(";").filter(e => e)
                  }
                ])
                if (amount <= excludedPrice) {
                  targetDiscount = preChecklist[i]
                  // console.log(
                  //   "购物车价格:",
                  //   allPrice,
                  //   "排除后价格:",
                  //   excludedPrice,
                  //   "减满折扣:",
                  //   targetDiscount
                  // )
                  break
                }
              }
              return targetDiscount
            }
          },
          // 移除foodInfo的大图压缩
          removeImageResize(imgLink = "") {
            if (!this.rollFoodBigPic) return imgLink
            //获取视口宽度
            let domWidth = document.getElementById("app").clientWidth
            // 取整
            let customWidth = Math.round(floatSub(domWidth, remToPx(0.72)))
            const newLink = imgLink.replace(/w_\d+,h_\d+/g, "w_" + customWidth)
            return newLink || imgLink
          },
          // 是否显示food/fty过期 阻止点击的遮罩层
          showExpiredmaskLayer(item) {
            // 组合tab开启时: 处于fty页,允许点击fty跳转
            //处于menu页,阻止点击,弹窗提示过期
            // 组合tab关闭:阻止点击,弹窗提示过期
            if (this.openTable.infiniteLoop) {
              return !this.ftSwitch
            } else {
              return item.isExpired && !item.combine_with_foodType
            }
          },
          // 初始化食代广场模式
          initFoodCourtMode() {
            let modeStr = sessionStorage.getItem("mode") || "{}"
            let { mode = "", config = {} } = JSON.parse(modeStr)
            this.openTableMode = { mode, config }
            let isFoodCourt = mode === "FoodCourt"
            let lastStatus = sessionStorage.getItem("showFoodCourtView")
            lastStatus = lastStatus === null || lastStatus === "true"

            if (isFoodCourt) {
              // 初始化大食代广场模式
              this.getFoodCourtList()
              this.showFoodCourtView = lastStatus
              if (lastStatus) return
            }
            // 其他模式<test/takeAway....>
            this.setCurrencyWay()
            this.getConfigureImg() //过敏源图片
            this.getData() //初始化商品
          },
          // 获取美食广场模式的店铺列表
          getFoodCourtList() {
            let index = this.showFoodCourtView ? layer.load(2) : 999
            const domain = sessionStorage.getItem("domain")
            $.ajax({
              url: "../storeNumber/getAllByDomain",
              type: "get",
              xhrFields: {
                responseType: "json"
              },
              data: { domain, foodCourtUsable: true, tableNumber: this.openTable.tableNumber },
              success: res => {
                res = Array.isArray(res) ? res : []
                if (!res.length) {
                  layer.msg("没有店铺")
                }
                let useBackupOss = sessionStorage.getItem("useBackupOss") || false
                let backupOssUrl = sessionStorage.getItem("backupOssUrl") || ""

                let oss = useBackupOss ? backupOssUrl : this.defaultOss
                this.foodCourtStoreList = res.map(item => {
                  let { photoConfig } = item
                  if (!photoConfig) {
                    item.storeLogoUrl = false
                    return item
                  }
                  let frontSplicing = `${oss}/${photoConfig.domain}/${photoConfig.storeNumber}/image/${photoConfig.typeName}/`
                  let finalURL = `${frontSplicing}${photoConfig.fileName}.${this.setImgSuffix(
                    photoConfig
                  )}`

                  item.storeLogoUrl = finalURL
                  return item
                })
                sessionStorage.setItem(
                  "foodCourtStoreList",
                  JSON.stringify(this.foodCourtStoreList)
                )
              },
              complete: () => {
                layer.close(index)
              },
              error: err => {}
            })
          },
          /**
           * @description 设置foodCourt模式 店铺列表的配置信息
           * @param {String} type 'color'|'column'
           * */
          setFoodCourtConfig(type) {
            let { config = {} } = this.openTableMode
            if (type === "color") {
              return config.color || "#ed6211"
            }
            if (type === "column") {
              return config.column || 1
            }
          },
          // 排序foodCourt店铺数据
          sortFoodCourtStore(list) {
            let { config: { sort = "" } = {} } = this.openTableMode
            let sortOrder = sort.split(";").filter(el => el)
            list.sort((a, b) => {
              let aCourt = this.getStoreFoodCount(a),
                bCourt = this.getStoreFoodCount(b)
              if (aCourt > bCourt) {
                return -1
              }
              if (aCourt < bCourt) {
                return 1
              }

              // 如果 count 相同，则按照 sort 数组的顺序排列
              let indexA = sortOrder.indexOf(a.storeNumber)
              let indexB = sortOrder.indexOf(b.storeNumber)

              // 如果 sort 数组中没有某个元素，则它的索引会是 -1，这种情况下应该排在后面
              if (indexA === -1) indexA = Infinity
              if (indexB === -1) indexB = Infinity

              return indexA - indexB
            })
            return list
          },
          // 显示美食广场的店铺列表页面
          toggleCourtListDisplay(status) {
            // 在loading时点击按钮不能弹出,会不显示动画
            if (this.loading) return
            this.showFoodCourtView = status
            // this.ftSwitch=!status
            let classList = this.$refs.food_court.classList
            if (status) {
              // 加200ms的延迟:不加延迟没有展开关闭动画,原因未知,$nextTick无效
              this.$nextTick(this.getFoodCourtList)
            }
          },
          // 请求openTable函数
          checkOpenTable(params) {
            let { companyName, performType, tableNumber: tn } = this.openTable
            let { storeNumber, tableNumber = tn } = params
            let urlData = sessionStorage.getItem("data") || ""
            let requestData = {
              companyName,
              storeNumber,
              tableNumber,
              urlData,
              performType,
              mode: "FoodCourt"
            }
            return sendOpenTableRequest(requestData, "menu")
              .then(result => {
                if (!result) {
                  throw false
                }
                const res = JSON.parse(result)
                const code = res.statusCode
                if (code && code !== 200) {
                  throw code
                }
                if (result && !result.errorCode) {
                  const uiconfig = dynamicConfig(res.uiConfigList)
                  const oldOpenTable = sessionStorage.getItem("openTable")
                  if (oldOpenTable) {
                    const oldData = JSON.parse(oldOpenTable)
                    const { urlPrefix, language, initialTableNum } = oldData
                    const newOpenTable = {
                      ...this.openTable,
                      urlPrefix: urlPrefix || "",
                      language: language || "zh",
                      companyName: sessionStorage.getItem("domain"),
                      initialTableNum: initialTableNum,
                      ...requestData,
                      selectedStoreConfig: res,
                      ...uiconfig
                    }
                    delete newOpenTable.uiConfigList
                    sessionStorage.removeItem("openTable")
                    sessionStorage.setItem("openTable", JSON.stringify(newOpenTable))
                  }
                }
              })
              .catch(err => {
                this.opTableRequestTip(err)
                throw err
              })
          },
          // FoodCourt模式 购物车内点击店铺名字,重新请求openTable,请求food数据
          onSelectStoreInCart(storeNumber) {
            let index = layer.load(2)
            let { tableNumber } = this.openTable
            this.checkOpenTable({ storeNumber, tableNumber })
              .then(r => {
                sessionStorage.setItem("showFoodCourtView", false)
                window.location.reload()
              })

              .finally(() => {
                layer.close(index)
              })
          },
          // FoodCourt模式在美食广场选择店铺,获取食品数据
          onSelectStoreInCourt({ storeNumber }) {
            let index = layer.load(2)
            let { companyName, performType, tableNumber } = this.openTable
            let urlData = sessionStorage.getItem("data")
            this.reOpenTLoading = true
            this.reOpenTStoreN = storeNumber
            this.checkOpenTable({ storeNumber, tableNumber })
              .then(r => {
                // 延时100ms再reload,解决会有不显示loading的情况
                sessionStorage.setItem("showFoodCourtView", false)
                window.location.reload()
              })
              .catch(e => {
                this.reOpenTLoading = false
              })
              .finally(() => {
                layer.close(index)
              })
          },
          // 根据storeNumber获取购物车已选数量
          getStoreFoodCount({ storeNumber }) {
            return this.getQtyNum(this.shopCartGroup[storeNumber])
          },
          /**
           *@description 获取传入数组的总qty
           * @param {Array} list 大Food的列表
           * @return {Number} 返回的qty总数
           * */
          getQtyNum(list = []) {
            return list.reduce((pre, cur) => {
              pre += +cur.qty1 || 0
              return pre
            }, 0)
          },
          // 根据storeNumber获取store店铺数据
          getStoreBasedOnSn(sn) {
            return this.foodCourtStoreList.find(el => el.storeNumber === sn)
          },
          // openTable请求状态码提示
          opTableRequestTip(code) {
            let { errorTxt, errorServiceNotOpenTip, errorServiceTip } = this.systemLanguage
            switch (code) {
              case 404:
                layer.msg(errorServiceNotOpenTip) //提示层
                break
              case 500:
                layer.msg(errorServiceTip) //提示层
                break
              default: //提示层
                layer.msg(errorTxt)
                return
            }
          },
          /**
           * @description 获取元素的padding值
           * */
          getPadding(el) {
            const style = window.getComputedStyle(el, null)
            const paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0
            const paddingRight = Number.parseInt(style.paddingRight, 10) || 0
            const paddingTop = Number.parseInt(style.paddingTop, 10) || 0
            const paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0
            return {
              pLeft: paddingLeft,
              pRight: paddingRight,
              pTop: paddingTop,
              pBottom: paddingBottom
            }
          },
          /**
           * @description 校验<文本>元素是否溢出隐藏
           * @returns {Boolean} 是否溢出
           * */
          checkEllipsis(el) {
            const range = document.createRange()
            range.setStart(el, 0)
            range.setEnd(el, el.childNodes.length)
            window.getSelection().addRange(range)
            const rangeWidth = range.getBoundingClientRect().width // 所有文字的宽度
            const rangeHeight = range.getBoundingClientRect().height // 所有文字的高度
            const { pLeft, pRight, pTop, pBottom } = this.getPadding(el)
            const horizontalPadding = pLeft + pRight
            const verticalPadding = pTop + pBottom
            return (
              rangeWidth + horizontalPadding > el.offsetWidth ||
              rangeHeight + verticalPadding > el.offsetHeight ||
              range.scrollWidth > el.offsetWidth
            )
          },
          // 校验2个大food是否为同源,fcode,ftc,storeNumber都相同
          checkSameFt(a, b, type = "normal") {
            const { groupedFoodsByTypeCode = false } = this.openTable //开启则不区分ftCode,不同ftCode的food视为同一food

            if (type === "del" || type === "editCart") return a.unique === b.unique

            // 基础比较条件
            const baseCondition = a.fCode === b.fCode && a.storeNumber === b.storeNumber

            // 如果启用了groupedFoodsByTypeCode,并且type不为del,则只需比较fCode和storeNumber
            if (groupedFoodsByTypeCode) {
              return baseCondition
            }

            return baseCondition && a.ftCode === b.ftCode
          },
          /**
           * @description 获取新加入购物车数据的源数据,维护shopCartSourceList数组
           * @param { Object } addItem 新加购物车数据
           * */
          addCartSourceData(addItem) {
            if (!this.isFoodCourtMode) return
            let existed = !!this.shopCartSourceList.find(el => this.checkSameFt(el, addItem))
            if (existed) return // 去重
            let source = this.allDataList
              .reduce((pre, cur) => {
                if (this.hasFoodList(cur)) {
                  pre = pre.concat(cur.foodList)
                }
                return pre
              }, [])
              .find(el => this.checkSameFt(el, addItem))
            if (!source) return
            this.shopCartSourceList.push(source)
            sessionStorage.setItem("shopCartSourceList", JSON.stringify(this.shopCartSourceList))
          },

          // 显示一个Vuetify的dialog
          /**
           * @description 四舍五入
           * @example toFixed(1.14)->1.1  toFixed(1.15)=>1.2
           * @param {Number} d 保留的小数位数
           * @param {Number} s 要保留的小数
           * */
          toFixed(d, s) {
            s = s.toString()
            if (!d) d = 0
            if (s.indexOf(".") === -1) s += "."
            s += new Array(d + 1).join("0")
            if (new RegExp("^(-|\\+)?(\\d+(\\.\\d{0," + (d + 1) + "})?)\\d*$").test(s)) {
              let s = "0" + RegExp.$2,
                pm = RegExp.$1,
                a = RegExp.$3.length,
                b = true
              if (a === d + 2) {
                a = s.match(/\d/g)
                if (parseInt(a[a.length - 1]) > 4) {
                  for (let i = a.length - 2; i >= 0; i--) {
                    a[i] = parseInt(a[i]) + 1
                    if (a[i] === 10) {
                      a[i] = 0
                      b = i !== 1
                    } else break
                  }
                }
                s = a.join("").replace(new RegExp("(\\d+)(\\d{" + d + "})\\d$"), "$1.$2")
              }
              if (b) s = s.substr(1)
              return +(pm + s).replace(/\.$/, "")
            }
            return this + ""
          },
          // 点击付款看选中/取消选中附加餐具
          onSelectAdditionalItems(item) {
            // 仅改动fixed的qty1,
            if (item.fixed) {
              item.qty1 = item.qty1 ? 0 : 1
            }
          },
          /**
           * @description 获取有效<已选择>的餐具数据
           * @param {boolean} auto 是否为获取auto的餐具/反之固定
           * */
          getSelectedAdditionalItemsData(fixed = true) {
            if (fixed) {
              return this.selectedAdditionalItemsForTakeawayFixed.filter(item => item.qty1)
            } else {
              let checked = Object.entries(this.selectedAdditionalItemsForTakeawayMap.AUTO)
                .filter(el => el[1])
                .map(e => e[0])
              return this.additionalItemsForTakeawayAutoList.filter(item =>
                checked.includes(item.fCode)
              )
            }
          },
          //处理餐具数据
          setAdditionalItemsForTakeaway(res) {
            let {
              additionalItemsForTakeawayAutoList: auto = [],
              additionalItemsForTakeawayFixedList: fixed = []
            } = res
            let fPriceName = this.priceName("foodList")
            let handleFn = (list, isFixed = true) => {
              let key = isFixed ? "FIXED" : "AUTO"
              return list.map(item => {
                this.$set(this.selectedAdditionalItemsForTakeawayMap[key], item.fCode, false)
                return {
                  ableDiscount: item.ableDiscount || null,
                  desc1: item.desc1 || "",
                  desc2: item.desc2 || "",
                  nameA: item.nameA || "",
                  nameB: item.nameB || "",
                  multi1: item.multi1 || "",
                  fCode: item.fCode,
                  qty1: isFixed ? 0 : item.qty1,
                  fixed: isFixed,
                  discount: item.discount,
                  upa1: item[fPriceName]
                }
              })
            }
            this.additionalItemsForTakeawayAutoList = handleFn(auto, false)
            this.additionalItemsForTakeawayFixedList = handleFn(fixed)
          },
          // 增加减少餐具的数量tablewareQty<暂不以qty1做数量,要以0开始>
          changeTablewareQty(item, add = true) {
            if (add) {
              item.qty1 += 1
            } else if (item.qty1 > 0) {
              item.qty1 -= 1
              if (item.qty1 === 0) {
                this.selectedAdditionalItemsForTakeawayMap.FIXED[item.fCode] = false
              }
            }
            return item
          },
          // 清空购物车按钮
          onClearShopCartBtn() {
            layer.alert(this.systemLanguage.clearShopCartTips, {
              title: false,
              skin: "defaultLayer",
              btn: [this.systemLanguage.confirmBtn],
              yes: i => {
                layer.close(i)
                this.clearShopCartData()
              },
              cancel: () => {}
            })
          },
          //清空购物车数据
          clearShopCartData() {
            this.shopCartList = []
            this.shopCartSourceList = []
            sessionStorage.removeItem("shopCartList")
            sessionStorage.removeItem("shopCartSourceList")
            this.additionalItemsForTakeawayFixedList.forEach(el => {
              el.qty1 = 0
            })
            this.selectedAdditionalItemsForTakeawayMap = {
              FIXED: {},
              AUTO: {}
            }
          },
          // 是否显示外层的添加购物车按钮
          showAddCartBtn(data) {
            let { itemCtrl = null, isExpired = false } = data
            let { showOutsideAddCartBtn = false } = this.openTable

            // 售罄或过期时不显示按钮
            if (itemCtrl || isExpired) {
              return false
            }

            // 检查是否有细项（类型、食物、套餐列表）
            let hasProject =
              this.hasTypeList(data) || this.hasFoodList(data) || this.hasMListList(data)

            // 检查是否有必选的固定细项
            const hasRequiredFixed = data.foodList
              .concat(data.mListList)
              .some(it => it.hasRequiredItem)

            //为true,则校验是否存在细项
            return showOutsideAddCartBtn
              ? !hasProject
              : this.hasRequiredData(data) && !hasRequiredFixed
          },
          // 显示一个Vuetify的dialog(ps:仅限于存在v-app标签的支付页面使用,其他页面无法显示)
          showBaseDialog(config) {
            let { show, title, content, action } = config
            let baseDialog = this.$refs["base-dialog"]
            baseDialog.show = show
            baseDialog.title = title
            baseDialog.content = content
            baseDialog.action = action
          },
          /**
           * @description 校验支付方式是否与预约时间存在冲突<柜台支付>
           * */
          checkPayMethodConflictsWithPickup() {
            if (!this.initTimePickerState) return
            let { PayAtCashierConflictsWithPickup, confirmBtn } = this.systemLanguage
            let { storeData: { pickupTime } = {} } = this.openTable
            let params = {
              show: true,
              title: "",
              content: PayAtCashierConflictsWithPickup,
              action: [
                {
                  text: confirmBtn,
                  color: "primary",
                  handler: () => {
                    this.$refs["base-dialog"].show = false
                  }
                }
              ]
            }
            // 是柜台支付选择的非尽快预约时间,弹出提示
            if (this.choosePayMethod == "payAtCashier" && notInstantPickup(pickupTime)) {
              this.showBaseDialog(params)
            }
          },
          // 支付失败回显orderview逻辑 (url参数:view=card)
          payFailBackOrderView() {
            let showCard = window.location.href.indexOf("view=card") != -1
            if (showCard) {
              let sendOrderForm = JSON.parse(sessionStorage.getItem("sendOrderForm"))
              this.sendOrderForm = { ...sendOrderForm }
              this.confirmDiscountCode = sendOrderForm.discountCode
              this.showSendOrderView = true
              this.choosePayMethod = sendOrderForm.choosePayMethod
              this.$nextTick(() => {
                sessionStorage.removeItem("sendOrderForm")
                //移除url中的view=card参数
                let url = window.location.href
                let newUrl = url.replace(/view=card/, "")
                window.history.replaceState({}, 0, newUrl)
              })
            }
          },
          // 判断openTable是否存在配置
          hasProperty(obj, key) {
            return this.openTable[obj] && this.openTable[obj][key]
          },
          // 设置animation 动画
          setAnimation(event, classNameList) {
            let animation = this.openTable.animation
            if (!this.hasProperty("animation", "page_zoom")) return
            event.target.classList.remove(...classNameList)
            window.requestAnimationFrame(() => {
              window.requestAnimationFrame(() => {
                event.target.classList.add(...classNameList)
              })
            })
            event.target.addEventListener("animationend", () => {
              event.target.classList.remove(...classNameList)
            })
          },
          // init
          initGiveAway() {
            //初始化赠品数据
            let allPrice = this.getAllFoodPrice()
            if ("promotionOfferList" in this.openTable) {
              this.useGiveAwayLevel(allPrice)
              if (this.giveAwayFcodeList.length) {
                this.findGiveAwayFoodList(this.giveAwayFcodeList)
              }
            }
          },
          //判断是否存在newOrderItem数据
          hasNewOrderItemData(item) {
            let orderItemType = [
              "newOrderItemFoodList",
              "newOrderItemMListList",
              "newOrderItemMTypeList",
              "newOrderItemFoodTypeList"
            ]
            return orderItemType.some(el => {
              if (this.isTakeAway) {
                let defaultJudgment = el in item && item[el].length
                let packData = item.packingBoxMList || {}
                let takeoutSuffix = Object.keys(packData).length !== 0
                return defaultJudgment || takeoutSuffix // 外卖盒子和固定细项有一个有就行
              } else {
                return el in item && item[el].length
              }
            })
          },
          //根据fcodes找到该food列表
          findGiveAwayFoodList(fcodeList) {
            // 将满足条件的所有赠品food数据提取出来
            let targetList = this.useGiveAwayList.reduce((pre, cur) => {
              cur.promotionFoodList && pre.push(...cur.promotionFoodList)
              return pre
            }, [])
            // 根据fcodeList找到对应的food,处理数据
            if (targetList.length) {
              fcodeList.forEach(fcode => {
                let foodItem = targetList.find(el => el.fCode === fcode)
                if (foodItem) {
                  foodItem.giveAway = true
                  this.giveAwayFoodList.push(foodItem)
                }
              })
              // console.log(this.giveAwayFoodList,'所有赠品food列表','有效赠品food数据')
            }
          },
          //根据fcode找到外层food
          findMainFoodByCode(list = [], fcode) {
            if (!fcode) return null
            for (const typeItem of list) {
              const items = typeItem.foodList || []
              const target = items.find(it => it.fCode === fcode)
              if (!target) continue
              return target
            }
            return null
          },
          //判断赠送类型/根据价格
          useGiveAwayLevel(price) {
            let validGiveawayList = this.openTable.promotionOfferList.filter(
              el => el.use_able && price >= el.amount
            )

            this.giveAwayFcodeList = []
            this.useGiveAwayList = []
            this.excludePrice = 0
            this.giveAwayFoodList = []
            if (validGiveawayList.length) {
              let sortArr = validGiveawayList.sort((a, b) => a.amount - b.amount)
              this.useGiveAway(sortArr)
            }
          },
          //判断赠送先决条件及日期时间是否符合
          useGiveAway(list) {
            list.forEach(giveAwayData => {
              if (giveAwayData.use_able) {
                //验证日期
                let timeStamp = moment().valueOf()
                let currentTime = moment(timeStamp).format("YYYY/MM/DD HH:mm")
                let currentTimeArry = currentTime.trim().split(" ") //獲取系统时间 2020/09/16
                let nowCurrentDate = currentTimeArry[0]
                let nowCurrentTime = currentTimeArry[1]
                let accordDate = this.checkUseDateRes(
                  giveAwayData.use_date,
                  timeStamp,
                  nowCurrentDate
                )
                //验证星期
                let accordDow = this.checkUseDowRes(giveAwayData.use_dow, timeStamp, nowCurrentDate)
                //验证时间与星期
                let accordTime = this.checkUseDowAndTime(
                  giveAwayData.use_time,
                  timeStamp,
                  nowCurrentTime,
                  accordDow,
                  nowCurrentDate
                )
                if (accordDate && accordDow && accordTime) {
                  //日期时间满足
                  //根据购物车价格 新增赠品逻辑
                  this.addGiveaway(giveAwayData)
                }
              }
            })
            // console.log(this.giveAwayFcodeList,'所有赠品fCode列表')
          },
          //在加入useGiveAwayList前再次验证排出后的总价是否满足赠送条件
          addGiveaway(giveAwayData) {
            let giveAwayFcodeList = giveAwayData.fCodes.split(";").filter(el => el)
            //获取当前赠送级别的排除fcode/ftcode,在计算总价时传入
            let excludeFcodeList = giveAwayData.inoperative_fCodes.split(";").filter(el => el)
            let excludeFtcodeList = giveAwayData.inoperative_ftCodes.split(";").filter(el => el)
            let currentExcludeList = {
              fcode: excludeFcodeList,
              ftcode: excludeFtcodeList
            }
            // 判断是否允许叠加赠送优惠
            if (!this.openTable.promotionOverlay) {
              // 若不允许则值判断当前级别,若允许则要判断之前的级别
              this.giveAwayExcludeCodeList = [currentExcludeList]
            } else {
              this.giveAwayExcludeCodeList.push(currentExcludeList)
            }
            let lastPrice = this.getAllFoodPrice(null, this.giveAwayExcludeCodeList)
            // 若总金额仍然大于优惠,则将此优惠保存数组
            if (lastPrice >= giveAwayData.amount) {
              // this.useGiveAwayList.push(giveAwayData)
              // 也需要判断是否允许叠加赠送优惠
              if (!this.openTable.promotionOverlay) {
                // 不允许,则使用当前优惠
                this.useGiveAwayList = [giveAwayData]
                this.giveAwayFcodeList = giveAwayFcodeList
              } else {
                this.useGiveAwayList.push(giveAwayData)
                this.giveAwayFcodeList.push(...giveAwayFcodeList)
              }
            }
          },
          //强调预约时间
          stressDate() {
            if (!this.openTable.storeData) return false //没有选店铺的外卖模式
            let { pickupTime = "" } = this.openTable.storeData
            let date = pickupTime.split(" ")
            let nowDate = moment().format("YYYY-MM-DD")
            return date.length > 1 && moment(nowDate).isBefore(date[0])
          },
          //返回至地图模式
          backMap() {
            let { confirmBtn, cancelBtn, backMapTip, backMapDiaTitle } = this.systemLanguage
            if (this.shopCartList.length) {
              layer.open({
                title: backMapDiaTitle, //不显示标题
                content: backMapTip,
                closeBtn: 0,
                btn: [cancelBtn, confirmBtn],
                shade: [0.1, "#fff"],
                skin: "backMapTips",
                area: ["80%"],
                yes: (index, layero) => {
                  layer.close(index)
                },
                btn2: index => {
                  layer.close(index)
                  window.location.href = "./map.html"
                  sessionStorage.removeItem("shopCartList")
                }
              })
            } else {
              window.location.href = "./map.html"
            }
          },
          //当点击更改预约时间的按钮时
          handleChangeDeliveryTime() {
            const { openingDates, openingHours, storeNumber } = this.openTable.storeData
            DeliveryTimePicker.option.checkTimeObj = {
              date: this.handleOpenTableLangEchoByStore("checkDate"),
              hourStrAndMinute: this.handleOpenTableLangEchoByStore("hourStrAndMinute")
            }
            this.getStorePickupTime(storeNumber)
              .then(r => {
                let { dateTimeMap = {}, timeZone, startTimeSecond } = r
                // 获取当前时区,与timeZoneMap对比,若时区不同则提示
                if (Object.keys(dateTimeMap).length) {
                  if (this.checkPickupTimeAgainstCurrent()) {
                    this.resetPickup()
                  }
                  let formatTimeZone = timeZone.replaceAll("-", "/").split("[")[0]
                  DeliveryTimePicker.show(dateTimeMap, startTimeSecond, formatTimeZone)
                  DeliveryTimePicker.toggleDisplayWarn(!this.isCurrentTimeZone(timeZone))
                }
              })
              .catch(err => {
                this.invalidPickupMsg = err.data || this.systemLanguage.takeawayTimeoutPrompt
                this.showInvalidPickupDia = true
              })
          },
          /**
           * @description 跟传入的时区对比,是否为当前时区
           * @param {`${number}-${number}-${number} ${number}:${number} [+${number}:${number}`} storeTimeZone 店铺的日期时间字符串[时区
           * @returns {boolean} 是否为当前时区
           * */
          isCurrentTimeZone(storeTimeZone) {
            if (!storeTimeZone) return true
            let [dateTime, timeZone] = storeTimeZone.split("[")
            let [hour, min] = timeZone.split(":")
            return new Date().getTimezoneOffset() === (+hour * 60 + +min) * -1
          },
          /**
           * @description 根据storeNumber获取该店铺的预约时间段列表
           * @async getStorePickupTime(storeNumber)
           * @param {string} storeNumber
           * @returns {Promise<{dataTimeMap:object,timeZoneMap:string}>}
           * */
          getStorePickupTime(storeNumber) {
            let data = {
              storeNumber,
              domain: sessionStorage.getItem("domain"),
              language: this.openTable.language
            }
            return new Promise((resolve, reject) => {
              $.ajax({
                url: "../storeNumber/getDateTimeMapByStoreNumber",
                type: "get",
                xhrFields: {
                  responseType: "json"
                },
                data,

                success: res => {
                  if (res.statusCode === 200) {
                    resolve(res.data || {})
                  } else {
                    reject(res)
                  }
                },
                error: err => {
                  reject(err)
                }
              })
            })
          },
          initTimePicker(language, pickupTime) {
            // if (!pickupTime) return
            let { takeAwayDiffTimeZoneTip, PayAtCashierConflictsWithPickup, confirmBtn } =
              this.systemLanguage
            DeliveryTimePicker.init(
              {
                lan: language || "en",
                showDate: false, // 显示‘今天/明天/后天’还是全部显示日期
                showWeekday: false, // 是否显示周几
                clickOutside: true, // 点击选择器外部关闭，默认true开启
                tableNumber: this.openTable.tableNumber, //默认外卖台号(却分模式显示头部信息)
                checkTimeObj: {
                  date: this.handleOpenTableLangEchoByStore("checkDate"),
                  hourStrAndMinute: this.handleOpenTableLangEchoByStore("hourStrAndMinute")
                },
                warnText: takeAwayDiffTimeZoneTip, // 时区不同的提示语
                // 选择时间后的回调函数
                callback: (date, hourStrAndMinute, time) => {
                  this.backupStoreData = { ...this.openTable.storeData }
                  this.openTable.storeData.pickupTime = time
                  this.openTable.storeData.hourStrAndMinute = hourStrAndMinute
                  this.openTable.storeData.checkDate = date
                  sessionStorage.setItem("openTable", JSON.stringify(this.openTable))
                  let failure = this.getShopDiffByPickupTime()
                  // 若购物车有数据,则要判断购物车数据是否符合预约时间
                  if (this.shopCartList.length && failure.length) {
                    this.invalidShopCartList = failure
                    this.showPickupDia = true
                  } else {
                    this.$nextTick(async () => {
                      //test台模拟时间则自动触发关闭模拟框遮罩层
                      if (this.isTestTable) {
                        await this.$refs.testTimeSimulationWarp.collapsePanel()
                      }
                      window.location.reload()
                    })
                  }
                }
              },
              "#app"
            )
            $("#deliveryTimePicker").css({ "z-index": "521" })
            this.initTimePickerState = true
          },
          //处理openTable数据中多语言数据的展示返回
          handleOpenTableLangEchoByStore(params, lan) {
            let { language, storeName = {}, storeAddress = {}, storeNumber } = this.openTable
            let value = (this.openTable.storeData && this.openTable.storeData[params]) || ""
            switch (params) {
              case "storeName":
                return Object.keys(storeName).length ? storeName[language] : storeNumber
              case "storeAddress":
                return Object.keys(storeAddress).length ? storeAddress[language] : ""
              case typeof value === "object":
                return lan ? value[language] || value.en : value
              case "pickupTime":
                if (!value.match(/\d{2}:\d{2}/)) {
                  return this.systemLanguage.instantPickup
                } else {
                  // let isAfter = this.checkPickupTimeAgainstCurrent()
                  // if (isAfter && this.showSendOrderView) {
                  //   return this.resetPickup()
                  // }
                  return value
                }
              default:
                return value
            }
          },
          /**
           * @description 对比当前时间若预约时间已经过了,则重新设置预约时间,若在营业时间内,则设置为尽快
           * @returns {string} 重置后的预约日期时间
           * */
          async resetPickup() {
            let { openingDates, openingHours, storeNumber, storeData } = this.openTable
            let { pickupTime } = storeData
            let { instantPickup } = this.systemLanguage
            let currentDate = moment().format("YYYY-MM-DD")
            let pickupDate = pickupTime.split(" ")[0]
            //  若当前为预约日期,判断当天是否仍在营业
            if (currentDate === pickupDate) {
              // 店铺是否正在营业
              let isOperation = verificationTimePeriod(openingDates, openingHours, true)
              if (isOperation) {
                // 在营业则设置为尽快,返回<尽快>
                this.openTable.storeData.hourStrAndMinute = instantPickup
                let pickupTime = this.openTable.storeData.pickupTime
                let [date, period] = pickupTime.split(" ")
                this.openTable.storeData.pickupTime = `${date} ${instantPickup}`
                DeliveryTimePicker.option.checkTimeObj.hourStrAndMinute = instantPickup
                sessionStorage.setItem("openTable", JSON.stringify(this.openTable))
                return instantPickup
              } else {
                //   已打烊
              }
            }
          },
          // 判断当前日期时间是否超过了预约日期时间<true:预约失效>
          checkPickupTimeAgainstCurrent() {
            let pickupTime = this.openTable.storeData && this.openTable.storeData["pickupTime"]
            let [date, period] = pickupTime.split(" ")
            let [startTime, endTime] = period.split("-")
            let fullPickup = `${date} ${endTime}`
            let current = moment().format("YYYY-MM-DD HH:mm")
            // fullPickup是否已经过期
            return !moment(current).isBefore(fullPickup)
          },
          // 根据选择的pickup时间,对比
          getShopDiffByPickupTime() {
            let failure = []
            let formatStartTime = "",
              formatEndTime = ""
            let { pickupTime = "" } = this.openTable.storeData
            let [date, time] = pickupTime.split(" ")
            if (time.match(/\d{2}:\d{2}/)) {
              //   是时间段,不是尽快
              let [startTime, endTime] = time.split("-")
              formatStartTime = moment(`${date} ${startTime}`, "YYYY-MM-DD HH:mm").format(
                "YYYY/MM/DD HH:mm"
              )
              formatEndTime = moment(`${date} ${endTime}`, "YYYY-MM-DD HH:mm").format(
                "YYYY/MM/DD HH:mm"
              )
            } else {
              formatStartTime = void 0
              formatEndTime = void 0
            }
            this.shopCartList.forEach(shop => {
              let start = this.consistentTimePeriod(shop, formatStartTime),
                end = this.consistentTimePeriod(shop, formatEndTime)
              // 判断开始时间和结束时间若都没有交集,则为无效数据
              // 比如: food的时间为11:00-12:00,预约时间为11:30-12:30,存在交集时间,则为有效数据
              if (!start && !end) {
                failure.push(shop)
              }
            })
            return failure
          },

          saveFontSize() {
            if (!sessionStorage.getItem("zoomFontSize")) {
              sessionStorage.setItem(
                "zoomFontSize",
                JSON.stringify({
                  originalFontSize: document.documentElement.style.fontSize,
                  currentFontSize: document.documentElement.style.fontSize
                })
              )
            }
            if (this.openTable.pageFontSize) {
              //default是关键字,不能用作变量名
              let { default: defaultSize } = this.openTable.pageFontSize
              //兼容旧版本没有default字段
              if (defaultSize && defaultSize.toLowerCase() !== "normal") {
                this.setTextZoom(defaultSize)
              }
            }
          },
          setTextZoom(type) {
            // 点击时候放大
            let { originalFontSize, currentFontSize } = JSON.parse(
              sessionStorage.getItem("zoomFontSize")
            )
            let zoomFontSize = { originalFontSize, currentFontSize }
            if (type == "normal") {
              document.documentElement.style.fontSize = originalFontSize
              zoomFontSize = { originalFontSize, currentFontSize: originalFontSize }
            } else {
              // 根据type放大倍数 Large / XLarge
              let size = Number(originalFontSize.replace("px", "")),
                newFontSize
              if (type == "large") {
                newFontSize = parseInt(size) + 7
              } else if (type == "xLarger") {
                newFontSize = parseInt(size) + 13
              }

              // 设置根元素的字体大小
              document.documentElement.style.fontSize = `${newFontSize}px`
              // 保存新的字体大小
              zoomFontSize = { originalFontSize, currentFontSize: `${newFontSize}px` }
            }
            sessionStorage.setItem("zoomFontSize", JSON.stringify(zoomFontSize))
            if (this.openTable.combinedNavigation) {
              this.$nextTick(this.$refs["custom-tabs"].moveSlider)
            }
            let { verticalOrderLayout, infiniteLoop } = this.openTable
            if (verticalOrderLayout || infiniteLoop) {
              this.$nextTick(() => {
                this.goAnchor(this.tabIsActive)
              })
            }
          },

          removeNullMerge(data) {
            const showFty = this.hasFoodList(data)
            const merItemArr = this.merge_fty[data.code]
            const isFind = this.allDataList.find(el => merItemArr && merItemArr.includes(el.code))
            if (showFty && isFind) {
              return true
            }
            if (showFty && !isFind) {
              return true
            }
            if (!showFty && isFind) {
              return true
            }
            if (!showFty && isFind) {
              return false
            }
          },
          /**
           * @description 是否显示超时的样式<变灰/不可点击>
           * @param {object} item 当前foodlist/mlist
           * @param {object} vItem 当前fty/mty
           * @returns {boolean} 是否显示超时的样式
           * */
          showTimeoutStyle(item, vItem) {
            let status = false
            // isExpired 字段存在,改item则已经超时
            if (vItem.hasOwnProperty("isExpired") && vItem.isExpired) {
              status = true
            } else if (item.hasOwnProperty("isExpired") && item.isExpired) {
              status = true
            }
            return status
          },

          // 判断SHOW WITH FCODE的food和fty数量限制
          getCartCheckNum(foodItemCode, vItemCode, type = 1, spliceIndex) {
            let cartFtyNum = 0,
              cartFoodNum = 0
            let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
            let useCartList
            let copyCartArry = JSON.parse(JSON.stringify(shopCartList))

            if (type == 2 && this.joinType != "edit") {
              // 深克隆一份购物车数据
              copyCartArry.splice(spliceIndex, 1) // 删除准备加1的对应Item
              useCartList = copyCartArry
            } else if (this.joinType == "edit") {
              copyCartArry.splice(this.clickCartEditIndex, 1) // 删除准备加1的对应Item
              useCartList = copyCartArry
            } else {
              useCartList = shopCartList
            }
            // console.log(useCartList, 'useCartList')
            // 递归遍历查询细项下所有foodlist (food)
            useCartList.forEach(cartFood => {
              let oneFoodAllCode = this.recursiveCartFun(cartFood, "checkFood", foodItemCode)
              //  验证的code与大food的code相同加一
              if (cartFood.fCode == foodItemCode) {
                oneFoodAllCode.push(foodItemCode)
              }
              // console.log(oneFoodAllCode, 'oneFoodAllCode')
              oneFoodAllCode.forEach(e => {
                if (e == foodItemCode) {
                  cartFoodNum += 1
                }
              })
              // 验证的code与大food的fty相同加一 (ftype)
              if (cartFood.ftCode == vItemCode) {
                cartFtyNum += cartFood.qty1
              }
              let ftyChildArry = this.recursiveCartFun(cartFood, "checkCartFty", vItemCode)
              cartFtyNum += ftyChildArry.length * cartFood.qty1
              cartFoodNum = cartFoodNum * cartFood.qty1
              // cartFtyNum = cartFtyNum * cartFood.qty1
            })
            // console.log(cartFoodNum, '已选food数量')
            // console.log(cartFtyNum, '已选fty数量')
            return { cartFoodNum, cartFtyNum }
          },
          // 递归food和fty
          recursiveCartFun(foodItem, type, vItemCode) {
            // allFoodCode/ftyChildArry返回的类型值不一样,无法合并,分type进行返回
            let allFoodCode = [] // 所有code
            let ftyChildArry = [] // 子对象
            let foodSetArry = foodItem.newOrderItemFoodList || []
            let newOrderItemFoodTypeList = foodItem.newOrderItemFoodTypeList || []
            if (foodSetArry.length != 0) {
              foodSetArry.forEach(item => {
                allFoodCode.push(item.fCode)
              })
            }
            newOrderItemFoodTypeList.forEach(item => {
              // type为checkFty情况下执行
              let newOrderItemFoodList = item.newOrderItemFoodList || []
              if (type == "checkCartFty") {
                if (vItemCode == item.code) {
                  ftyChildArry.push(...newOrderItemFoodList)
                }
              } else if (type == "checkLocalFty") {
                ftyChildArry.push({
                  code: item.code,
                  qty: newOrderItemFoodList.length
                })
              }

              newOrderItemFoodList.forEach(i => {
                let thisItemxiCode = this.recursiveCartFun(i, type, vItemCode)
                if (type == "checkCartFty" || type == "checkLocalFty") {
                  ftyChildArry.push(...thisItemxiCode)
                }
                allFoodCode.push(i.fCode, ...thisItemxiCode)
              })
            })
            if (type == "checkFood") {
              // console.log(allFoodCode, 'allFoodCode')
              return allFoodCode
            } else {
              return ftyChildArry
            }
          },

          // checkCartSIFCodes 判断是否符合SHOW WITH FCODE 里food的数量添加
          checkCartSIFCodes(foodItem, nextQty = 0) {
            let verifyArry = [...this.showInFCodesQtyList, ...this.showInFTCodesQtyList]
            let checkFoodVal = false,
              checkFtyVal = false

            let { cartFoodNum, cartFtyNum } = this.getCartCheckNum(foodItem.fCode, foodItem.ftCode)
            for (let i = 0; i < verifyArry.length; i++) {
              const item = verifyArry[i]
              if (item.code == foodItem.fCode) {
                if (cartFoodNum + nextQty > item.qty) {
                  checkFoodVal = true
                  break
                }
              } else if (item.code == foodItem.ftCode) {
                if (cartFtyNum + nextQty > item.qty) {
                  checkFtyVal = true
                  break
                }
              }
            }
            // console.log(checkFoodVal, checkFtyVal, "最终限制判断food和fty")
            return checkFoodVal || checkFtyVal
          },

          //  判断是否符合SHOW WITH FCODE 里food的数量添加
          checkLocalSIFCodes(foodItem, nextQty = 0, tier = 1) {
            let { qty1 } = this.foodInfoItem // 大food基础数量不为1,成倍增加
            let verifyArry = [...this.showInFCodesQtyList, ...this.showInFTCodesQtyList]
            let checkFoodVal = false,
              checkFtyVal = false
            this.addFoodAndFtyToArry(foodItem)
            let localtFtyNum = 0
            let localFoodNum = 0
            // 获取对应点击的food数量
            this.localAllCode.forEach(item => {
              if (item.code == foodItem.fCode) {
                localFoodNum = item.qty
              } else if (item.code == foodItem.ftCode) {
                localtFtyNum = item.qty
              }
            })

            let { cartFoodNum, cartFtyNum } = this.getCartCheckNum(foodItem.fCode, foodItem.ftCode)
            // localFoodNum = localFoodNum * qty1
            // localtFtyNum = localtFtyNum * qty1
            localFoodNum += cartFoodNum
            localtFtyNum += cartFtyNum
            for (let i = 0; i < verifyArry.length; i++) {
              const item = verifyArry[i]
              // 判断foodCode
              if (item.code == foodItem.fCode) {
                if (localFoodNum > item.qty) {
                  checkFoodVal = true
                  // 执行回减
                  break
                }
              } else if (item.code == foodItem.ftCode) {
                if (localtFtyNum > item.qty) {
                  checkFtyVal = true
                  break
                }
              }
            }
            if (checkFoodVal || checkFtyVal) this.localAllCode = this.resetAddNumArry
            return checkFoodVal || checkFtyVal
          },

          // 递归单个food和fty生成对象
          setLocalAllCodeObj(type) {
            let foodItem = {
              ftCode: this.foodInfoItem.ftCode,
              fCode: this.foodInfoItem.fCode,
              newOrderItemFoodList: this.localfoodListArry,
              newOrderItemFoodTypeList: this.localfoodTypeListArry
            }
            let { qty1 } = this.foodInfoItem
            let oneFoodAllCode = this.recursiveCartFun(
              foodItem, //递归item所有细项
              "checkFood"
            )

            var allFoodCodeObj = {}
            oneFoodAllCode.push(foodItem.fCode)
            for (var i = 0; i < oneFoodAllCode.length; i++) {
              var key = oneFoodAllCode[i]
              if (allFoodCodeObj[key]) {
                allFoodCodeObj[key]++
              } else {
                allFoodCodeObj[key] = 1
              }
            }
            let localArry = []
            for (let key in allFoodCodeObj) {
              localArry.push({
                code: key,
                qty: allFoodCodeObj[key]
              })
            }

            // 获取fty长度逻辑
            let ftyChildArry = this.recursiveCartFun(foodItem, "checkLocalFty")
            // 先预先加入自身大food的fty
            ftyChildArry.push({
              code: foodItem.ftCode,
              qty: 1
            })
            // 合并相同fty
            ftyChildArry = ftyChildArry.reduce((obj, item) => {
              let find = obj.find(i => i.code === item.code)
              let _d = {
                ...item
              }
              find ? (find.qty += item.qty) : obj.push(_d)
              return obj
            }, [])
            // 初始化数量
            ftyChildArry.forEach(item => {
              item.qty = item.qty
            })
            localArry = [...localArry, ...ftyChildArry]
            // 第一层判断重置数量
            if (type == "openFoodInfo") {
              localArry.forEach(item => {
                item.qty = item.qty * qty1
              })
            }
            this.localAllCode = localArry
            this.oldLocalAllCode = JSON.parse(JSON.stringify(localArry))
            // console.log(JSON.parse(JSON.stringify(this.localAllCode)), "进入详情页遍历所有code")
          },
          getDelLocalNum(item, delItemArry) {
            let coypItem = JSON.parse(JSON.stringify(item)) // 点击删除的item
            let copyDelItemArry = JSON.parse(JSON.stringify(delItemArry)) // 共删除的item数组数量
            let { qty1 } = this.foodInfoItem
            let delNum = copyDelItemArry.length * qty1
            let delAllfCode = [] //存放删除的单个item及携带后面所有fCode
            let delAllFtyCode = [] //存放删除的单个item及携带后面所有fCode
            copyDelItemArry.forEach(delItem => {
              // 删除自身的固定foolist再递归
              // delete delItem.newOrderItemFoodList
              delAllfCode.push(coypItem.fCode)
              delAllfCode.push(
                ...this.recursiveCartFun(
                  delItem, //递归item所有细项
                  "checkFood"
                )
              )
              // fty逻辑
              let ftyChildArry = this.recursiveCartFun(delItem, "checkLocalFty")
              delAllFtyCode.push({ code: item.ftCode, qty: 1 })
              delAllFtyCode = [...delAllFtyCode, ...ftyChildArry]
            })
            console.log(delAllfCode, "delAllfCode")
            // 处理food合并及初始化数量
            let copyDelItemObj = {}
            for (var i = 0; i < delAllfCode.length; i++) {
              var key = delAllfCode[i]
              if (copyDelItemObj[key]) {
                copyDelItemObj[key]++
              } else {
                copyDelItemObj[key] = 1
              }
            }
            let copyDelArry = []
            for (let key in copyDelItemObj) {
              copyDelArry.push({
                code: key,
                qty: copyDelItemObj[key] * qty1
              })
            }
            // 处理ffty合并及初始化数量
            delAllFtyCode = delAllFtyCode.reduce((obj, item) => {
              let find = obj.find(i => i.code === item.code)
              let _d = {
                ...item
              }
              find ? (find.qty += item.qty) : obj.push(_d)
              return obj
            }, [])
            // 初始化数量
            delAllFtyCode.forEach(item => {
              item.qty = item.qty * qty1
            })
            // 合并food和fty的数量
            copyDelArry = [...copyDelArry, ...delAllFtyCode]
            console.log(delAllFtyCode, "删除整合后的delAllFtyCode")
            copyDelArry.forEach(item => {
              this.localAllCode.forEach(local => {
                if (item.code == local.code) {
                  local.qty = local.qty - item.qty // 删除对应Fcode数量
                }
              })
            })

            console.log(copyDelItemObj, "删除整合后的copyDelItemObj")
            console.log(JSON.parse(JSON.stringify(this.localAllCode)), "删除操作后所有的code")
            // let { fCode, ftCode } = item
          },
          // 执行添加数量进localAllCode事件
          addFoodAndFtyToArry(foodItem) {
            let { qty1 } = this.foodInfoItem // 大food基础数量不为1,成倍增加
            let { fCode, ftCode, foodList } = foodItem

            // 备份预算加一前的所有数值
            this.resetAddNumArry = JSON.parse(JSON.stringify(this.localAllCode))
            let localAllCode = JSON.parse(JSON.stringify(this.localAllCode)) || [] //深拷贝避免影响到备份oldLocalAllCode数据
            let foolistCode = [{ code: fCode, qty: qty1 }] //预先加入点击的自身fCode
            if (!foodList) return
            // 添加固定细项
            foodList.forEach(foodItem => {
              foolistCode.push({ code: foodItem.fCode, qty: qty1 })
            })
            localAllCode = [...localAllCode, ...foolistCode] //合并原有的code,预加点击后的细项
            // 预加fty数量
            localAllCode.push({ code: foodItem.ftCode, qty: qty1 })
            // 合并food和fty数量
            localAllCode = localAllCode.reduce((obj, item) => {
              let find = obj.find(i => i.code === item.code)
              let _d = {
                ...item
              }
              find ? (find.qty += item.qty) : obj.push(_d)
              return obj
            }, [])

            // 添加时候算上大food的qty
            // localAllCode.forEach((item) => {
            //   item.qty = item.qty * qty1
            // })
            this.localAllCode = localAllCode
            // console.log(JSON.parse(JSON.stringify(this.localAllCode)), "点击预添加后")
          },

          // 超出回减保持最后一个状态
          subtract(foodItem) {
            let { fCode, ftCode, foodList } = foodItem
            let foolistCode = [fCode]
            if (!foodList) return
            foodList.forEach(foodItem => {
              foolistCode.push(foodItem.fCode)
            })

            this.localAllCode.forEach(localItem => {
              if (foolistCode.includes(localItem.code)) {
                localItem.qty--
              }
            })
          },
          // 是否存在fMtypeArry, allTypeList.length>0
          hasTypeList(item) {
            if (!item) return false
            item.allTypeArry = Array.isArray(item.allTypeArry) ? item.allTypeArry : []
            return !!item.allTypeArry.length
          },
          // 是否存在foodList, foodList.length>0
          hasFoodList(item) {
            if (!item) return false
            return !!(Array.isArray(item.foodList) && item.foodList.length)
          },
          // 是否存在MList, mList.length>0
          hasMListList(item) {
            if (!item) return false
            return !!(Array.isArray(item.mListList) && item.mListList.length)
          },
          // 弹窗title 中Qty数量判断是否符合
          selectPro(typeItem, type = "ftyItem") {
            let minQty = typeItem.minQty || -Infinity
            let maxQty = typeItem.maxQty || Infinity
            let listName = type == "mtyItem" ? "mListList" : "foodList"
            let num = 0
            // console.log(typeItem[listName], ' typeItem[listName]');
            typeItem[listName].forEach(item => {
              if (item.selected) num += item.qty1 * (item.takeUpQty || 1)
            })
            return num <= maxQty && num >= minQty
          },
          onDisableItem(data) {
            if (!data) return
            let val = data.filter(el => !el.isExpired && !el.itemCtrl)
            return val
          },
          selectProDialog(clickXiItem, localFtypeArry, localmtypeArry) {
            let minQty2 = clickXiItem.minQty2 || -Infinity
            let maxQty2 = clickXiItem.maxQty2 || Infinity
            let food =
              clickXiItem.newOrderItemFoodList || this.onDisableItem(clickXiItem.foodList) || []
            let mlist =
              clickXiItem.newOrderItemMListList || this.onDisableItem(clickXiItem.mListList) || []
            let baseLength = food.length + mlist.length
            let FtypeLength = 0,
              MtypeLength = 0
            localFtypeArry.forEach(item => {
              item.newOrderItemFoodList.forEach(inItem => {
                FtypeLength += inItem.qty1
              })
            })
            localmtypeArry.forEach(item => {
              item.newOrderItemMListList.forEach(inItem => {
                MtypeLength += inItem.qty1
              })
            })
            let allLength = baseLength + FtypeLength + MtypeLength
            // console.log(baseLength, FtypeLength, MtypeLength, '总数');
            return allLength <= maxQty2 && allLength >= minQty2
          },
          // 我的细项弹窗确定按钮
          onfineItem() {
            let editItem = this.editXiItem
            if (this.addSMXiVerifyQty2("min", "null", "null")) return // 验证是否符合minQty2(满足最小值)
            let allTypeList = [...(this.editXiItem.allTypeArry || [])]
            // console.log(allTypeList, 'allTypeList');
            if (this.belowFoodTypeNum(editItem)) return // 验证是否符合minQty(满足最小值)
            if (editItem.hasRequiredItem) {
              // Why is editXiItem used here?
              this.$set(this.editXiItem, "selected", true)
              this.$set(this.clickXiItem, "selected", true)
            }
            // console.log(this.clickOnSource, 'this.clickOnSource');
            if (this.clickOnSource == "fromMulti") {
              // 正式外层fty添加选中数据
              let item = this.clickXiItem
              let type = this.clickXiType
              let vItem = this.clickXiOutItem
              this.addData(item, type, vItem)
              console.log(
                this.localfoodTypeListArry,
                "multiselect追加套餐确定后localfoodTypeListArry"
              )
              if (this.itemFoldable) {
                const isMax = this.satisfyMaxQty(vItem)
                //满足最大Qty,折叠当前type
                if (isMax) this.foldType(vItem, 1)
              }
            } else {
              this.$set(editItem, "newOrderItemFoodTypeList", this.localAddSMXiFtyArry)
              this.$set(editItem, "newOrderItemMTypeList", this.localAddSMXiMtyArry)
              // if (this.clickXiType == "foodList" || this.clickXiType == "mListList") {
              //   this.clickXiItem.localListSelect = str
              // }
            }
            layer.close(this.foodDialogIndex) // 关闭最外层
          },
          // 第二层弹窗确定添加细项
          onSecfineItem() {
            let editItem = this.myXiClickXiItem
            if (this.secVerifyQty2("min", "null", "null")) return // 验证是否符合minQty2(满足最小值)
            let allTypeList = [...(this.myXiClickXiItem.allTypeArry || [])]
            if (this.belowFoodTypeNum(editItem)) return // 验证是否符合minQty(满足最小值)
            if (editItem.hasRequiredItem) {
              this.$set(editItem, "selected", true)
            }
            if (this.addDataState == "fromMulti") {
              // 正式外层fty添加选中数据
              let [item, type, vItem] = [
                this.myXiClickXiItem,
                this.myXiClickXiType,
                this.myXiClickXiOutItem
              ]
              this.noXiAdd(item, type, vItem)
              if (this.itemFoldable) {
                const isMax = this.satisfyMaxQty(vItem)
                //满足最大Qty,折叠当前type
                if (isMax) this.foldType(vItem, 1)
              }
            } else {
              this.$set(
                this.myXiEditXiItem,
                "newOrderItemFoodTypeList",
                this.localSecAddSMXiFtyArry
              )
              this.$set(this.myXiEditXiItem, "newOrderItemMTypeList", this.localSecAddSMXiMtyArry)
              console.log(
                this.localSecAddSMXiFtyArry,
                this.localSecAddSMXiMtyArry,
                "修改细项第二层选项按钮进"
              )
            }
            layer.close(this.secondDialogIndex) // 关闭最外层
          },
          onCencelMyxi() {
            layer.close(this.foodDialogIndex) // 关闭最外层
            this.localAllCode = this.oldLocalAllCode
          },
          onSecCencel() {
            layer.close(this.secondDialogIndex) // 关闭最外层
            // 恢复原始第二层数据
            this.localAllCode = this.twoDiaAllCode
            console.log(this.localAllCode, "第三层取消恢复数据")
          },
          setCurrencyWay() {
            const way = this.openTable.currencyWay
            if (way) {
              this.currencyWay = way
            } else {
              this.currencyWay = this.defaultWay
            }
          },

          // 处理细项中的细项
          todofineItem(data) {
            let lan = this.openTable.language
            if (data) {
              if (data.length == 0) {
                return ""
              } else {
                let arryText = []
                data.forEach((item, i) => {
                  if (lan == "en") {
                    if (item.name) {
                      arryText.push(item.name)
                    } else if (item.desc) {
                      arryText.push(item.desc)
                    } else if (item.desc1) {
                      arryText.push(item.desc1)
                    }
                  } else if (lan == "zh") {
                    if (item.name2) {
                      arryText.push(item.name2)
                    } else if (item.desc2) {
                      arryText.push(item.desc2)
                    }
                  } else {
                    if (item.multi1) {
                      arryText.push(item.multi1)
                    } else if (item.name3) {
                      arryText.push(item.name3)
                    }
                  }
                })
                let str = ""
                arryText.forEach((item, i) => {
                  str += arryText[i] + "" + "+" + ""
                })
                if (str.length > 0) {
                  str = str.substr(0, str.length - 1)
                }
                // console.log(str, "str");
                return str
              }
            } else {
              return ""
            }
          },

          checkShopCartIntegrity() {
            const foodCourt =
              JSON.parse(sessionStorage.getItem("mode") || "{}").mode === "FoodCourt"
            if (foodCourt) return true
            try {
              const shopCart = JSON.parse(sessionStorage.getItem("shopCartList")) || []
              if (!shopCart.length) return true
              const storeNumber = this.openTable.storeNumber
              const validData = shopCart.filter(it => it.storeNumber === storeNumber)
              sessionStorage.setItem("shopCartList", JSON.stringify(validData))
            } catch {
              sessionStorage.removeItem("shopCartList")
            }
          },
          // 初始化openTable/shopcart信息
          initializeThe() {
            // this.openTable = JSON.parse(this.getQueryString('openTable'))
            sessionStorage.removeItem("couponUseType")
            this.openTable = JSON.parse(sessionStorage.getItem("openTable"))
            this.sortPromotionDiscount()
            this.ftSwitch = this.openTable.ftyPage
            this.checkShopCartIntegrity()
            // 添加状态使watch中shopCartList可不触发
            this.cartSyncLocked = true
            this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
            this.initCartHashRecord()
            this.shopCartSourceList = JSON.parse(sessionStorage.getItem("shopCartSourceList")) || []
            // 初始化显示第三语言
            this.showIi8Lan()
            // 初始字体大小配置
            if (this.openTable.pageFontSize) {
              this.fontSizeOption = this.openTable.pageFontSize.fontSizeType
            }
            console.log(JSON.parse(JSON.stringify(this.openTable)), "初始化OpenTable")
            //openTable获取配置前缀pix字段,动态加载script标签支付依赖
            if (this.openTable.webBaseUrl && !window.SpiralPG) {
              //动态创建script标签,前缀+"spiralpg.min.js",async属性加载
              let script = document.createElement("script")
              script.type = "text/javascript"
              script.src = this.openTable.webBaseUrl + "spiralpg.min.js"
              script.async = true
              document.body.appendChild(script)
            }
            this.initSetPayMethod() //初始设置支付方式
            //获取MD历史点单记录
            this.getHistoryOrder()
              .then(this.formatHistoryOrderData)
              .catch(() => {})
          },
          // 是否显示国际语言
          showIi8Lan() {
            if (this.openTable.thirdLan) {
              if (this.lanSele.find(l => l.value === "thirdLan")) return
              this.lanSele.push({
                label: this.openTable.thirdLan,
                value: "thirdLan"
              })
            }
          },
          hideMerge(data) {
            return this.merge_codes.includes(data.code)
          },
          getMerge_data(item) {
            if (this.merge_fty.hasOwnProperty([item.code])) {
              let result = []
              const codes = this.merge_fty[item.code]
              if (item.foodList.length) {
                result.push(item)
              }
              result = this.allDataList.reduce((pre, acc) => {
                if (codes.includes(acc.code) && acc.foodList.length) pre.push(acc)
                return pre
              }, result)
              return result.sort((a, b) => {
                return codes.indexOf(a.code) - codes.indexOf(b.code)
              })
            }
            return []
          },
          sort2(a, b) {
            return a.sort2 - b.sort2
          },
          getData() {
            this.loading = true
            // 设置请求头(版本号;cms具备不同版本预览BYOD功能)
            let versionNumber = sessionStorage.getItem("versionNumber") || ""
            let urlPath =
              versionNumber == ""
                ? "../food/getFoodAndFoodTypeList"
                : "../manager_food/getFoodAndFoodTypeList" //根据是否是PROD环境判断请求地址
            let {
              companyName,
              storeNumber,
              tableNumber,
              performType,
              ftyPage,
              redirectAfterLogin,
              storeData: { pickupTime, startTimeSecond } = {}
            } = this.openTable
            let tableKey
            if (performType == 1 || performType == 3) {
              tableKey = this.openTable.tableKey
            } else {
              tableKey = ""
            }
            $.get({
              url: urlPath,
              dataType: "json",
              headers: {
                versionNumber
              },
              data: {
                companyName,
                tableKey,
                performType,
                table_number: tableNumber,
                store_number: storeNumber,
                pickupTime: notInstantPickup(pickupTime)
                  ? formatPickupTime(pickupTime, startTimeSecond)
                  : void 0,
                mode: this.isFoodCourtMode ? "FoodCourt" : void 0
              },

              success: async res => {
                if (res.RESULT_CODE == 0) {
                  const {
                    orderTimeLimit_longestOrderTime,
                    orderTimeLimit_countdown,
                    orderStartTime,
                    pax,
                    promotionOfferList,
                    showInFCodesQtyList,
                    showInFTCodesQtyList,
                    additionalItemsForTakeawayAutoList,
                    additionalItemsForTakeawayFixedList,
                    itemCtrlUpdateTime,
                    oisNowDateTime
                  } = res
                  // 初始化當前台的落單時間限制
                  if (orderTimeLimit_longestOrderTime && orderTimeLimit_countdown) {
                    this.orderTimeDynamicConfig = [
                      orderTimeLimit_longestOrderTime,
                      orderTimeLimit_countdown
                    ]
                  }
                  this.orderStartTime = orderStartTime
                  this.itemCtrlUpdateTime = itemCtrlUpdateTime
                  this.oisNowDateTime = oisNowDateTime || moment().format("YYYY/MM/DD HH:mm:ss")

                  let versionNumber = sessionStorage.getItem("versionNumber")
                  // 赋值数据
                  let domain = location.host.split(".")[0]
                  let isFoodCourt = this.openTableMode.mode === "FoodCourt"
                  let baseUrl =
                    this.defaultOss +
                    "/" +
                    domain +
                    (isFoodCourt ? `/${storeNumber}` : "") +
                    "/image/"
                  let useBackupOss = sessionStorage.getItem("useBackupOss") || false
                  let backupOssUrl = sessionStorage.getItem("backupOssUrl") || ""
                  // 使用备用oss
                  if (useBackupOss) {
                    baseUrl = baseUrl.replace(this.defaultOss, backupOssUrl)
                  }
                  if (versionNumber) {
                    baseUrl = baseUrl + versionNumber + "/" //判断是否需要加版本号
                  }
                  this.setMaxOrderLimit(pax) //处理最大下单限制数量
                  this.openTable.promotionOfferList = promotionOfferList || []

                  this.baseUrl = baseUrl
                  // showInFcode的限制数组
                  this.showInFCodesQtyList = showInFCodesQtyList || []
                  this.showInFTCodesQtyList = showInFTCodesQtyList || []
                  this.foodBaseUrl = baseUrl + "food/"
                  this.ftyBaseUrl = baseUrl + "foodType/"
                  checkOrderWait(companyName, storeNumber, "menu", true) //判断是否需要排队
                  // res.list = res.list.filter(i => i.code === "AF")
                  this.sortAllList(res.list, "code", "finalSort")

                  const allMenuList = this.combineMenuData(res) // 组合menu数据

                  const allTypeCodes = allMenuList.map(t => t.code)
                  allMenuList.forEach(e => {
                    this.aboutFtyImg(e) //处理fty相关图片
                    // 处理合并foodType
                    if (e.hasOwnProperty("combine_with_foodType") && e.combine_with_foodType) {
                      let codeArr = e.combine_with_foodType.split(";").filter(Boolean)
                      let efficient = allTypeCodes.filter(code => codeArr.includes(code))
                      const sorted = codeArr
                        .map(it => {
                          if (efficient.includes(it)) return it
                        })
                        .filter(Boolean)
                      if (efficient.length) {
                        this.merge_fty[e.code] = sorted
                      }
                      this.merge_codes = this.merge_codes.concat(sorted)
                    }
                    if (!e.display_column && e.display_column !== 0) e.display_column = 1 // 判断fty的弹性布局类型
                    this.formatMenuFoodItem(e)
                  })

                  this.setAdditionalItemsForTakeaway({
                    additionalItemsForTakeawayAutoList,
                    additionalItemsForTakeawayFixedList
                  })

                  console.log("被合并的type:", this.merge_fty)

                  this.allDataList = handleMenuItemCtrl(allMenuList)
                  this.updateItemCtrlMap()
                  if (allMenuList.length) {
                    this.createSSE()
                    this.handleSSEActivity()
                    this.initDanmuLib()
                    // console.log(JSON.parse(JSON.stringify(this.allDataList)), "总数据")
                    // 选中的tab改为用code,用于组合tabs时能更好定位数据
                    let redirectData = this.redirectScroll() || this.showFirstData()
                    if (!ftyPage && redirectData) {
                      const { index, code } = redirectData
                      this.tabIsActive = code
                      if (this.allDataList[index]) {
                        this.foodList = this.allDataList[index].foodList
                        this.clickLayout = this.allDataList[index].display_column
                      }
                      this.$nextTick(() => {
                        this.onfty_anchor(code)
                      })
                    }
                  } else {
                    this.layerDia(this.systemLanguage.menuNoDataTip)
                  }
                  sessionStorage.setItem("openTable", JSON.stringify(this.openTable))
                } else {
                  this.showErrorData = true
                  this.layerDia(this.systemLanguage.menuNoDataTip)
                }
                this.loading = false
              },
              error: error => {
                this.loading = false
                this.layerDia(this.systemLanguage.menuNoDataTip)
                this.showErrorData = true
                console.log(error)
              }
            })
          },
          // 根据map组合menu菜单数据
          combineMenuData(result) {
            const { list = [], foodMap, mListMap, foodTypeMap, mTypeMap } = result
            this.menuMap = Object.freeze({
              foodMap,
              mListMap,
              foodTypeMap,
              mTypeMap
            })
            this.menuSourceList = Object.freeze(JSON.parse(JSON.stringify(list)))
            this.menuDescendantMap = Object.freeze(
              findAllAndCollectDescendants(this.menuSourceList)
            )
            this.menuMustItemMap = collectNestedDirectCodes(list)
            const res = this.recursiveMergeMenu(list)
            // 已合并菜单数据
            this.combineMenuCompleted = true
            return res
          },
          recursiveMergeMenu(list = []) {
            if (!list.length) return []
            list.forEach(typeItem => {
              this.combineMenuHelper(
                typeItem,
                typeItem.typeName !== "mtyItem" ? "foodTypeMap" : "mTypeMap"
              )

              if (Array.isArray(typeItem.foodList)) {
                typeItem.foodList.forEach(item => {
                  this.combineMenuHelper(item, "foodMap")
                  this.recursiveToDealWith(item)
                  this.updateFoodInventoryAccordingChild(item, typeItem)
                })
              }
              if (Array.isArray(typeItem.mListList)) {
                typeItem.mListList.forEach(item => {
                  this.combineMenuHelper(item, "mListMap")
                  this.recursiveToDealWith(item)
                  this.updateFoodInventoryAccordingChild(item, typeItem)
                })
              }
            })
            return list
          },

          combineMenuHelper(item, mapKey) {
            const key = mapKey === "foodMap" ? "fCode" : "code"
            if (
              this.combineMenuCompleted &&
              !this.updateInventoryCodes
                .concat(this.updateItemCtrlDescendantsCodes)
                .includes(item[key])
            ) {
              // 已处理过menu数据&&不在更新售罄数量list及其后代中,则跳过更新
              return false
            }
            const defaultMap = {
              foodMap: {
                desc1: "",
                desc2: "",
                itemCtrl: false,
                minQty2: null,
                maxQty2: null,
                upa1: 0,
                upa2: 0,
                upa3: 0,
                upa4: 0,
                upa5: 0,
                upa6: 0,
                upa7: 0,
                upa8: 0,
                qty1: 1,
                resetQty: 1,
                takeUpQty: 1
              },
              foodTypeMap: {
                name: "",
                name2: "",
                minQty: null,
                maxQty: null,
                item_ctrl_model: 0,
                itemCtrl: false
              },
              mListMap: {
                name: "",
                name2: "",
                itemCtrl: false,
                price: 0,
                price2: 0,
                price3: 0,
                price4: 0,
                price5: 0,
                price6: 0,
                price7: 0,
                price8: 0,
                qty1: 1,
                resetQty: 1,
                takeUpQty: 1
              },
              mTypeMap: {
                desc: "",
                desc2: "",
                minQty: null,
                maxQty: null,
                item_ctrl_model: 0,
                itemCtrl: false
              }
            }
            // 特殊字段, 需要重置库存/售罄字段
            const overriddenKey = ["inventory", "itemCtrl"]
            const target = this.menuMap[mapKey][item[key]]
            if (!target) return
            const obj = { ...defaultMap[mapKey], ...target }
            for (const targetKey in obj) {
              if (!item.hasOwnProperty(targetKey)) {
                this.$set(item, targetKey, obj[targetKey])
              } else {
                // 待优化
                if (targetKey === "packingBoxMList" && target["packingBoxMList"]) {
                  for (const packingKey in target["packingBoxMList"]) {
                    if (!item["packingBoxMList"].hasOwnProperty(packingKey)) {
                      item["packingBoxMList"][packingKey] = target["packingBoxMList"][packingKey]
                    }
                  }
                }
                // 包含在待更新ItemCtrl列表中,强制更新字段
                if (
                  this.enableSSEItemCtrl &&
                  this.updateInventoryCodes.includes(item.fCode || item.code) &&
                  overriddenKey.includes(targetKey)
                ) {
                  if (mapKey == "foodMap" || mapKey === "mListMap") {
                    // SSE ItemCtrl 开始时才覆盖特殊字段
                    // 值不一致才赋值
                    if (item[targetKey] !== obj[targetKey])
                      this.$set(item, targetKey, obj[targetKey])
                  }
                }
              }
            }
            if (mapKey == "foodMap" || mapKey === "mListMap") {
              const qty1 = this.regularQty(item.qty1)
              if (qty1 !== item.qty1) {
                item.qty1 = qty1
                item.resetQty = item.qty1
              }
            }
          },

          formatMenuFoodItem(typeItem) {
            if (!Array.isArray(typeItem.foodList)) return
            typeItem.foodList.forEach(item => {
              Vue.set(item, "qty1", 1)
              Vue.set(item, "storeNumber", this.openTable.storeNumber)
              if (item.allergen_icons && item.allergen_icons.length != 0) {
                item.allergen_icons = item.allergen_icons.split(",")
              }
              item.newOrderItemFoodList = [] //添加选中细项
              item.newOrderItemMListList = [] //添加选中细项
              item.newOrderItemMTypeList = [] //添加选中细项
              item.newOrderItemFoodTypeList = [] //添加选中细项
              //服务器端限制img的大小参数
              let serverImgParam = typeItem.display_column == 0 ? "w_400,h_400" : "w_200,h_200"
              let suffix = this.setImgSuffix(item)
              let imglink = `${this.baseUrl}food/${item.fCode}.${suffix}?x-oss-process=image/resize,${serverImgParam},${item.photoTime}`
              item.imglink = imglink
            })
          },
          // 登录后重定向滚动
          redirectScroll() {
            let { ftyPage, redirectAfterLogin } = this.openTable
            // 根据ftcode找到索引 滚动到对应位置
            let scrollIndex = this.allDataList.findIndex(fty => fty.code == redirectAfterLogin)
            let scrollData = this.allDataList.find(fty => fty.code == redirectAfterLogin)
            // 寻找到对应的index并且fty时间未过期
            if (scrollIndex != -1) {
              if (ftyPage) {
                this.$nextTick(() => {
                  let scrollDom = $(".app_fty_container").children().eq(scrollIndex)
                  scrollDom[0].scrollIntoView({
                    behavior: "instant",
                    block: "center",
                    inline: "nearest"
                  })
                  this.onfty(scrollIndex, scrollData)
                })
              } else {
                return { index: scrollIndex, code: scrollData.code }
              }
            }
          },
          showFirstData() {
            for (let i = 0; i < this.allDataList.length; i++) {
              // console.log(this.allDataList[i]);
              if (!this.allDataList[i].isExpired)
                return { index: i, code: this.allDataList[i].code }
            }
          },
          // opentable解析
          getQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i")
            var r = decodeURI(window.location.search).substr(1).match(reg)
            if (r != null) {
              return unescape(r[2])
            }
            return null
          },
          toFormData(obj) {
            let data = new FormData()
            for (const key in obj) {
              if (Object.hasOwnProperty.call(obj, key)) {
                const element = obj[key]
                if (Array.isArray(element)) {
                  element.forEach(value => {
                    data.append(key, value)
                  })
                } else {
                  data.append(key, element)
                }
              }
            }
            return data
          },
          getSpecifiedPhotoConfig(params) {
            return fetch("../photoConfig/getSpecifiedPhotoConfig", {
              method: "POST",
              body: this.toFormData(params)
            }).then(r => r.json())
          },
          // 获取过敏源图片
          getConfigureImg(list = []) {
            let typeNameList = [
              "Allergen icons",
              "Image Tag",
              "Member Center Image",
              "Queue Waiting photo"
            ]
            let storeNumber = this.openTable.storeNumber
            if (this.isTakeAway) typeNameList = [...typeNameList, "Store photo"]
            if (list.includes("Store photo") && this.isFoodCourtMode) {
              storeNumber = "*"
            }
            if (!list.length) list = typeNameList
            let data = {
              domain: sessionStorage.getItem("domain"),
              storeNumber,
              typeNameList: list
            }
            this.getSpecifiedPhotoConfig(data)
              .then(async res => {
                let { photoConfigList, statusCode, ossBackupUrl } = res
                let obj = {}
                if (statusCode === 200) {
                  // forEach 无法使用async标识
                  for (const item of photoConfigList) {
                    //前置拼接
                    let frontSplicing = `${this.defaultOss}/${item.domain}/${item.storeNumber}/image/${item.typeName}/`
                    let useBackupOss = sessionStorage.getItem("useBackupOss") || false
                    // 使用备用oss
                    if (useBackupOss) {
                      let backupOssUrl = sessionStorage.getItem("backupOssUrl")
                      frontSplicing = frontSplicing.replace(this.defaultOss, backupOssUrl)
                    }
                    //后置拼接
                    let tailSplice = `.${this.setImgSuffix(
                      item
                    )}?x-oss-process=image/resize,w_100,h_100`
                    if (item.typeName == "Allergen icons") {
                      let finalURL = `${frontSplicing}${item.fileName}${tailSplice}`
                      if (!useBackupOss) {
                        await checkImage(finalURL, [this.defaultOss]).then(r => {
                          obj[item.fileName] = r.url
                        })
                      } else {
                        obj[item.fileName] = finalURL
                      }
                    } else if (item.typeName == "Image Tag") {
                      const mapFileName = item.fileName.split(";")
                      let fileNameObj = {}
                      const checkItem = async mapItem => {
                        let finalURL = `${frontSplicing}${item.extraPaths}${mapItem}${tailSplice}`
                        if (!useBackupOss) {
                          const res = await checkImage(finalURL, [this.defaultOss])
                          fileNameObj[mapItem] = res.url
                        } else {
                          fileNameObj[mapItem] = finalURL
                        }
                        return Promise.resolve()
                      }
                      for (const name of mapFileName) {
                        await checkItem(name)
                      }

                      // 去除最后斜杠字符串extraPaths: "a1/"
                      let seriesName = item.extraPaths.substring(0, item.extraPaths.length - 1)
                      // 去除最后一个字符串
                      obj[seriesName] = fileNameObj
                    } else if (
                      item.typeName == "Store photo" ||
                      item.typeName == "Member Center Image"
                    ) {
                      let finalURL = `${frontSplicing}${item.fileName}.${this.setImgSuffix(item)}`
                      let imgUrl = ""
                      const promiseImg = () => {
                        return new Promise((resolve, reject) => {
                          if (!useBackupOss) {
                            checkImage(finalURL, [this.defaultOss])
                              .then(r => {
                                imgUrl = r.url
                                resolve()
                              })
                              .catch(() => {
                                reject()
                              })
                          } else {
                            imgUrl = finalURL
                            resolve()
                          }
                        })
                      }
                      await promiseImg()
                      if (item.typeName == "Store photo") this.storePhotoUrl = imgUrl
                      if (item.typeName == "Member Center Image") this.memberCenterLogo = imgUrl
                    }
                  }
                  if (list.includes("Image Tag") || list.includes("Allergen icons")) {
                    this.configureImgUrl = obj
                  }
                }
              })
              .catch(() => {
                // 图片接口无需报错
                // this.layerDia("Failed to configure image")
              })
          },

          // 固定语言
          fixLan() {
            // let language = sessionStorage.getItem(language) || 'zh';
            let { language } = this.openTable
            document.documentElement.setAttribute("lang", language)
            // 后期将固定语言抽出单独js文件引入
            this.systemLanguage = window.i18n[language]
          },
          onTab(item) {
            this.tabIsActive = item.code
            let { verticalGoAnchor, infiniteLoop } = this.openTable
            if (verticalGoAnchor || infiniteLoop) {
              this.goAnchor(item.code)
            } else {
              this.tabData = item
              this.clickLayout = item.display_column
              this.foodList = Array.isArray(item.foodList) ? item.foodList : []
            }
          },

          // 验证(fty/mty)是否存在必选数据
          hasRequiredData(item) {
            // foodTypeList,foodList,有数据无加号进入选择界面
            // mListList,mTypeList 有数据，有加号进入选择界面
            // 全无 数据直接添加
            // let foodtype = item.foodTypeList || []
            // let mTypeList = item.mTypeList || []
            // let allList = [...foodtype, ...mTypeList]
            let allTypeArry = item.allTypeArry || []
            let res = true
            if (allTypeArry.length > 0) {
              for (let i = 0; i < allTypeArry.length; i++) {
                let items = allTypeArry[i]
                if (items.minQty && items.minQty != 0 && items.minQty != -Infinity) {
                  // console.log(items, "items")
                  res = false
                  break
                }
              }
            }
            return res
          },

          // 历史订单记录打开关闭
          onHistoryPopUp() {
            window.location.href = "../order/historyIndex" //encodeURIComponent:参数编码、
          },
          // 抛物线
          additem(event, addFoodObj, animateDrop = true) {
            // 判断SHOW WITH FCODE里food的数量限制
            if (this.checkCartSIFCodes(addFoodObj, 1)) {
              let { limitByFcode, language } = this.openTable
              this.layerDia(limitByFcode[language])
              return
            }
            // 判断主food最大数量限制
            if (this.checkMaxFoodNum(addFoodObj)) return
            if (addFoodObj.hasOwnProperty("isExpired") && addFoodObj.isExpired) return
            let fPriceName = this.priceName("foodList")
            let { storeNumber } = this.openTable
            let newfoodObj = {
              storeNumber: addFoodObj.storeNumber || storeNumber,
              upa1: addFoodObj[fPriceName], //传入动态价格字段
              desc1: addFoodObj.desc1,
              desc2: addFoodObj.desc2,
              nameA: addFoodObj.nameA,
              nameB: addFoodObj.nameB,
              multi1: addFoodObj.multi1,
              fCode: addFoodObj.fCode,
              k1: addFoodObj.k1,
              kpName: addFoodObj.kpName,
              single: addFoodObj.single,
              qty1: addFoodObj.qty1 || 1, // 商品数量
              maxSingleOrderCount: addFoodObj.maxSingleOrderCount,
              maxSingleQuantityToCart: addFoodObj.maxSingleQuantityToCart,
              mapCode: addFoodObj.mapCode,
              seq: addFoodObj.seq,
              finalSort: addFoodObj.finalSort,
              fGroup: addFoodObj.fGroup,
              level: addFoodObj.level,
              points: addFoodObj.points,
              t_able: addFoodObj.t_able,
              sc_able: addFoodObj.sc_able,
              discount: addFoodObj.discount,
              ftCode: addFoodObj.ftCode,
              newOrderItemFoodList: [...(this.onDisableItem(addFoodObj.foodList) || [])],
              newOrderItemMListList: [...(this.onDisableItem(addFoodObj.mListList) || [])],
              newOrderItemMTypeList: [],
              newOrderItemFoodTypeList: [],
              use_dow: addFoodObj.use_dow,
              use_date: addFoodObj.use_date,
              use_time: addFoodObj.use_time,
              use_dow2: addFoodObj.use_dow2,
              use_date2: addFoodObj.use_date2,
              use_time2: addFoodObj.use_time2,
              minQty: addFoodObj.minQty,
              maxQty: addFoodObj.maxQty,
              minQty2: addFoodObj.minQty2,
              maxQty2: addFoodObj.maxQty2,
              show_In_FCodes: addFoodObj.show_In_FCodes,
              not_Show_With_FCodes: addFoodObj.not_Show_With_FCodes,
              hotSaleMap: addFoodObj.hotSaleMap,
              photoTime: addFoodObj.photoTime,
              photoSuffix: addFoodObj.photoSuffix,
              packingBoxMList: this.useTakeAwayPackag ? addFoodObj.packingBoxMList : null,
              packingBoxMListCode: this.useTakeAwayPackag ? addFoodObj.packingBoxMListCode : null,
              ableDiscount: addFoodObj.ableDiscount,
              tax1: addFoodObj.tax1,
              tax2: addFoodObj.tax2,
              tax3: addFoodObj.tax3
            }
            //判断其下的flist的qty1
            newfoodObj.newOrderItemFoodList.forEach(i => {
              i.qty1 = i.qty1 || 1
            })
            newfoodObj.newOrderItemMListList.forEach(i => {
              i.qty1 = i.qty1 || 1
            })

            this.updateHashRecord(newfoodObj)
            let allshoplNumber = this.allshoplNumber
            console.log(JSON.parse(JSON.stringify(newfoodObj)), "newfoodObj")
            if (allshoplNumber + newfoodObj.qty1 > this.numberDishes && this.isShowNumberDishes) {
              let { promptText } = this.systemLanguage
              promptText = promptText.replace("#numberDishes", this.numberDishes)
              this.layerDia(promptText)
              // 是否大于等于2
              return
            }
            let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
            let thereAre = false //储存变量遍历结束判断
            let ISselect = false
            if (shopCartList && shopCartList.length !== 0) {
              // 有数据
              const lastRecord = newfoodObj.hashRecord[newfoodObj.hashRecord.length - 1]
              const existSameItem = this.findCartEqualHashItem(shopCartList, {
                hash: lastRecord.hash,
                data: { ftCode: newfoodObj.ftCode }
              })
              //购物车存在相同细项的item
              if (existSameItem) {
                existSameItem.qty1 += 1
                this.updateHashRecord(existSameItem)
              } else {
                shopCartList.push(newfoodObj)
                this.addCartSourceData(newfoodObj)
              }
            } else {
              // 没有数据直接添加
              shopCartList.push(newfoodObj)
              this.addCartSourceData(newfoodObj)
            }
            this.shopCartList = shopCartList
            sessionStorage.setItem("shopCartList", JSON.stringify(shopCartList))
            this.addCartSourceData(newfoodObj)
            this.addImgLink = addFoodObj.imglink
            if (animateDrop) this.drop(event.target, addFoodObj.imglink) //默认开启小球动画
          },

          drop(el, imgLink) {
            //抛物
            for (let i = 0; i < this.balls.length; i++) {
              let ball = this.balls[i]
              if (!ball.show) {
                ball.show = true
                ball.el = el
                ball.imgLink = imgLink
                this.dropBalls.push(ball)
                return
              }
            }
          },
          beforeDrop(el) {
            /* 购物车小球动画实现 */
            let count = this.balls.length
            let clientWidth = document.body.clientWidth
            let xOffset,
              yOffset = 80
            // 判断是否有PC模式
            let isPCmodel = $("body").hasClass("pc")
            // x越小值越靠右,联想竖屏视口宽800/横屏1334
            if (isPCmodel) {
              let bodyOffset = document.body.getBoundingClientRect().left
              xOffset = bodyOffset + 260
            } else if (clientWidth > 1024) {
              xOffset = 880
            } else if (clientWidth >= 1000) {
              xOffset = 720
            } else if (clientWidth >= 375) {
              xOffset = 280
            } else {
              xOffset = 220
            }
            while (count--) {
              let ball = this.balls[count]
              if (ball.show) {
                let rect = ball.el.getBoundingClientRect() //元素相对于视口的位置
                //  获取视口宽度
                let x = rect.left - xOffset
                // console.log(rect, x);
                // let x = rect.left - 120;
                // debugger;
                // let y = -(window.innerHeight - rect.top - 80); //获取y
                let y = -(window.innerHeight - rect.top - yOffset) //获取y
                el.style.display = ""
                el.style.webkitTransform = "translateY(" + y + "px)" //translateY
                el.style.transform = "translateY(" + y + "px)"
                let inner = el.getElementsByClassName("inner-hook")[0]
                inner.style.webkitTransform = "translateX(" + x + "px)"
                inner.style.transform = "translateX(" + x + "px)"
              }
            }
          },
          dropping(el, done) {
            /*重置小球数量  样式重置*/
            let rf = el.offsetHeight
            el.style.webkitTransform = "translate3d(0,0,0)"
            el.style.transform = "translate3d(0,0,0)"
            let inner = el.getElementsByClassName("inner-hook")[0]
            inner.style.webkitTransform = "translate3d(0,0,0)"
            inner.style.transform = "translate3d(0,0,0)"
            el.addEventListener("transitionend", done)
          },
          afterDrop(el) {
            /*初始化小球*/
            let ball = this.dropBalls.shift()
            if (ball) {
              ball.show = false
              el.style.display = "none"
              // 获取 img 元素
              const img = el.querySelector("img")
              let srcStr = ""
              if (img) srcStr = img.src
              if (!srcStr.includes("timg")) {
                ball.imgLink = ""
              }
            }
            // 小球结束动画后给.defaultSubAtTheBottom下的bottomSubmitIcon添加动画swingAnimation,0.5s后移除
            if (this.hasProperty("animation", "page_shaking")) {
              $(".defaultSubAtTheBottom").find(".bottomSubmitIcon").addClass("swingAnimation")
              setTimeout(() => {
                $(".defaultSubAtTheBottom").find(".bottomSubmitIcon").removeClass("swingAnimation")
              }, 500)
            }
          },
          //直接移除cart的某个food
          removeCarFood(item) {
            let index = this.shopCartList.findIndex(el => this.checkSameFt(el, item, "del"))
            this.shopCartList.splice(index, 1)
            this.shopCartSourceList =
              this.shopCartSourceList.filter(el => !this.checkSameFt(el, item)) || []
            sessionStorage.setItem("shopCartList", JSON.stringify(this.shopCartList))
            sessionStorage.setItem("shopCartSourceList", JSON.stringify(this.shopCartSourceList))
          },
          delCarFood(event, item) {
            let shopCartList = this.shopCartList
            if (item.qty1 > 1) {
              item.qty1--
              // 点击按钮动画
              this.setAnimation(event, ["animate__animated", "animate__headShake"])
              this.updateHashRecord(item)
            } else if (item.qty1 == 1) {
              this.removeCarFood(item)
            }
            sessionStorage.setItem("shopCartList", JSON.stringify(shopCartList))
            this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList"))
            // 避免空数据但状态维持购物车编辑状态影响重新加入数据
            if (shopCartList.length == 0 && this.cartEditXi) this.cartEditBtn()
          },
          addCarFood(event, item, index) {
            if (this.checkMaxFoodNum(item, 1, "cart")) return // 判断主food最大数量限制
            // console.log('购物车添加');
            if (this.allowCartlAddSIFode(item, index)) {
              let { limitByFcode, language } = this.openTable
              this.layerDia(limitByFcode[language])
              return
            }
            // 点击按钮动画
            this.setAnimation(event, ["animate__animated", "animate__bounceIn"])
            let shopCartList = this.shopCartList
            let allshoplNumber = this.allshoplNumber
            if (allshoplNumber >= this.numberDishes && this.isShowNumberDishes) {
              let { promptText } = this.systemLanguage
              promptText = promptText.replace("#numberDishes", this.numberDishes)
              this.layerDia(promptText)
              return
            }
            item.qty1++
            this.updateHashRecord(item)
            sessionStorage.setItem("shopCartList", JSON.stringify(shopCartList))
            this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList"))
          },
          // 购物车页判断是否允许加一
          allowCartlAddSIFode(foodItem, clickIndex) {
            // 把购物车所有code合计
            let checkFoodVal = false,
              checkFtyVal = false
            let oneFoodAllCode = this.recursiveCartFun(
              foodItem, //递归item所有细项
              "checkFood"
            )
            let num = foodItem.qty1 + 1
            oneFoodAllCode.push(foodItem.fCode) //添加自身大FOOD的fcode
            // console.log(oneFoodAllCode, '所有fcode')

            let verifyArryFood = this.showInFCodesQtyList
            let verifyArryFty = this.showInFTCodesQtyList

            var allFoodCodeObj = {}
            for (var i = 0; i < oneFoodAllCode.length; i++) {
              var key = oneFoodAllCode[i]
              if (allFoodCodeObj[key]) {
                allFoodCodeObj[key]++
              } else {
                allFoodCodeObj[key] = 1
              }
            }
            // 预加1然后对数量相乘
            for (let key in allFoodCodeObj) {
              allFoodCodeObj[key] = allFoodCodeObj[key] * num
            }
            // 遍历本地每个code再寻找购物车对应的code数量相加
            for (let i = 0; i < verifyArryFood.length; i++) {
              const item = verifyArryFood[i]
              for (let key in allFoodCodeObj) {
                if (item.code == key) {
                  let { cartFoodNum } = this.getCartCheckNum(key, null, 2, clickIndex)
                  let num = allFoodCodeObj[key] + cartFoodNum
                  console.log(cartFoodNum, "购物车数量", num, "总数量")
                  if (num > item.qty) {
                    checkFoodVal = true
                    console.log(key, "购物车food按钮添加超限了")
                    break
                  }
                }
              }
            }

            for (let i = 0; i < verifyArryFty.length; i++) {
              let baseFty = 0
              const item = verifyArryFty[i]
              let { cartFtyNum } = this.getCartCheckNum(null, item.code, 2, clickIndex)
              let ftyChildArry = this.recursiveCartFun(foodItem, "checkCartFty", foodItem.ftCode)
              if (item.code == foodItem.ftCode) {
                baseFty += 1
                baseFty += ftyChildArry.length
              }
              console.log(item.code, baseFty * num, "购物车预算后fty数量", cartFtyNum, "购物车数量")
              if (cartFtyNum + baseFty * num > item.qty) {
                checkFtyVal = true
                console.log(item.code, "购物车fty按钮添加超限了")
                break
              }
            }
            //console.log(allFoodCodeObj, "预加一后的全部code")
            return checkFoodVal || checkFtyVal
          },
          // 详情页showINCode判断是否允许加一
          allowDetailAddSIFode() {
            let foodItem = {
              ftCode: this.foodInfoItem.ftCode,
              fCode: this.foodInfoItem.fCode,
              newOrderItemFoodList: this.localfoodListArry,
              newOrderItemFoodTypeList: this.localfoodTypeListArry
            }
            // 执行一次重置
            this.setLocalAllCodeObj()
            let checkFoodVal = false,
              checkFtyVal = false
            let qty1 = this.foodInfoItem.qty1 + 1 // 预加数量
            // console.log(qty1, 'qty1')
            let localAllCode = JSON.parse(JSON.stringify(this.localAllCode))
            console.log(JSON.parse(JSON.stringify(this.localAllCode)), "详情页添加")
            let verifyArryFood = this.showInFCodesQtyList
            let verifyArryFty = this.showInFTCodesQtyList

            // 遍历本地每个code再寻找购物车对应的code数量相加
            for (let i = 0; i < verifyArryFood.length; i++) {
              const item = verifyArryFood[i]
              for (let index = 0; index < localAllCode.length; index++) {
                const loaclItem = localAllCode[index]
                let { cartFoodNum } = this.getCartCheckNum(item.code, null)
                let num = loaclItem.qty * qty1 + cartFoodNum
                if (item.code == loaclItem.code) {
                  console.log(item.code, num, cartFoodNum, "判断")
                  if (num > item.qty) {
                    checkFoodVal = true
                    console.log(item.code, "详情页food按钮添加超限了")
                    break
                  }
                }
              }
            }

            for (let i = 0; i < verifyArryFty.length; i++) {
              const item = verifyArryFty[i]
              for (let index = 0; index < localAllCode.length; index++) {
                const loaclFtyItem = localAllCode[index]
                let { cartFtyNum } = this.getCartCheckNum(null, item.code)
                let num = loaclFtyItem.qty * qty1 + cartFtyNum
                if (item.code == loaclFtyItem.code) {
                  console.log(item.code, num, cartFtyNum, "详情页添加判断fty")
                  if (num > item.qty) {
                    checkFtyVal = true
                    console.log("详情页fty按钮添加超限了")
                    break
                  }
                }
              }
            }

            if (!checkFoodVal && !checkFtyVal) {
              this.localAllCode.forEach(item => {
                item.qty = item.qty * qty1
              })
              console.log(this.localAllCode, "详情页允许加一后总Code")
            }

            return checkFoodVal || checkFtyVal
          },
          // 商品详情删除
          delfoodInfo(event) {
            let foodInfoItem = this.foodInfoItem
            // console.log("详情减")
            if (foodInfoItem.qty1 > 1) {
              foodInfoItem.qty1--
              // 总数量减1
              this.localAllCode.forEach(localItem => {
                localItem.qty--
              })
              this.setAnimation(event, ["animate__animated", "animate__headShake"])
            } else {
              // console.log('不可以再减')
            }
          },
          // 商品详情添加
          addfoodInfo() {
            // console.log('详情页添加');
            // 点击按钮动画
            // if (this.allowDetailAddSIFode()) {
            //   let { limitByFcode, language } = this.openTable
            //   this.layerDia(limitByFcode[language])
            //   return
            // }

            if (this.checkMaxFoodNum(this.foodInfoItem, 1)) return // 判断主food最大数量下单限制
            let foodInfoItem = this.foodInfoItem
            let allshoplNumber = this.allshoplNumber
            let foodqty1 = this.foodInfoItem.qty1
            // console.log(allshoplNumber, foodqty1);
            if (this.joinType == "edit") {
              let effectiveNum = this.effectiveNum
              if (effectiveNum + foodqty1 >= this.numberDishes && this.isShowNumberDishes) {
                let { promptText } = this.systemLanguage
                promptText = promptText.replace("#numberDishes", this.numberDishes)
                this.layerDia(promptText)
                return
              }
            } else {
              if (allshoplNumber + foodqty1 >= this.numberDishes && this.isShowNumberDishes) {
                let { promptText } = this.systemLanguage
                promptText = promptText.replace("#numberDishes", this.numberDishes)
                this.layerDia(promptText)
                return
              }
            }
            this.setAnimation(event, ["animate__animated", "animate__bounceIn"])

            this.foodInfoItem.qty1++
          },

          // 判断购物车是否大于2数量
          cartlimit() {
            let shopCartList = this.shopCartList
            // this.showLittleDIV = false; //重置细项是否显示
            let totalNumber = 0
            shopCartList.forEach(item => {
              totalNumber += item.qty1
            })

            return totalNumber
          },

          showOutTimeTips() {
            this.layerDia(this.systemLanguage.dataTimeOutTip)
          },
          // 食品详情
          onfoodInfo(item) {
            // showInFcode判断是否符合,避免可以不选细项直接添加
            if (this.checkCartSIFCodes(item, 1)) {
              let { limitByFcode, language } = this.openTable
              this.layerDia(limitByFcode[language])
              return
            }
            //如果存在invalid，那么不能点击
            if (item.hasOwnProperty("isExpired") && item.isExpired) {
              return
            }
            // 禁止售罄食品点击
            if (item.itemCtrl) return
            this.showFoodWarp = true //显示food弹窗
            // console.log(item, '点击的food');
            let copyItem = JSON.parse(JSON.stringify(item)) //深拷贝一层
            this.foodInfoItem = {
              ...copyItem,
              qty1: item.qty1 || 1,
              unique: item.unique
            }
            this.toDealWithSome() // 处理foodInfoItem细项等逻辑
            this.cartSelectShow() // 回显购物车已选中 select
            this.toDealWithfoodName() // foodItem长度超过后滚动显示
            console.log(JSON.parse(JSON.stringify(this.foodInfoItem)), "进来的的FoodItem")
            this.setLocalAllCodeObj("openFoodInfo")
            this.$nextTick(this.previewFun) // 预览图片初始化
          },
          // fty和mty铅笔图标
          onPicker(vItem, item, type) {
            this.clickXiItem = item
            this.clickXiType = type // 点击得细项
            this.clickXiOutItem = vItem // 点击得细项
            // console.log(type, 'type');
            let outCode = vItem.code
            let typeCode = {
              foodList: "fCode",
              mListList: "code",
              foodtypeInList: "fCode",
              mtypeInList: "code"
            }
            this.showXiItem(outCode, item[typeCode[type]], type)
            if (this.localXiItem && this.localXiItem.length == 1) {
              this.showMyxiDia(this.localXiItem[0]) //当已选细项为1直接弹窗
            } else {
              let that = this
              layer.open({
                skin: "xiNumDiaLayer",
                type: 1,
                shade: [0.1, "#fff"],
                title: false, //不显示标题
                content: $(".xiNumDia"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
                cancel: function (index, layero) {
                  // console.log('销毁了');
                  layer.close(index)
                },
                end: function () {}
              })
            }

            console.log(item, "点击我的细项", type)
          },
          // fty和mty铅笔图标
          secOnPicker(vItem, item, type) {
            this.myXiClickXiItem = item
            this.myXiClickXiType = type
            this.myXiClickXiOutItem = vItem
            // console.log(type, 'type');
            //父级类型为foood时候标识为fCode
            let outCode = type == "foodList" ? vItem.fCode : vItem.code
            let typeCode = {
              foodList: "fCode",
              mListList: "code",
              foodtypeInList: "fCode",
              mtypeInList: "code"
            }
            this.showXiItem(outCode, item[typeCode[type]], type, "second")
            if (this.secLocalXiItem && this.secLocalXiItem.length == 1) {
              this.secShowMyxiDia(this.secLocalXiItem[0])
            } else {
              let that = this
              layui.use("layer", function () {
                var layer = layui.layer
                layer.ready(function () {
                  layer.open({
                    skin: "xiNumDiaLayer",
                    type: 1,
                    shade: [0.1, "#fff"],
                    title: false, //不显示标题
                    content: $(".secXiNumDia"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
                    cancel: function (index, layero) {
                      // console.log('销毁了');
                      layer.close(index)
                    },
                    end: function () {}
                  })
                })
              })
            }

            // console.log(item, '打开第二层弹窗secOnPicker', type)
          },
          // foodItem长度超过后滚动显示
          toDealWithfoodName() {
            let foodName = this.inListTitle(this.foodInfoItem)
            let nameObj = this.textSize("0.45rem", "Arial", foodName)
            if (nameObj.width > 200) {
              this.nameRoll = true
            } else {
              this.nameRoll = false
            }
          },
          // 处理foodInfoItem细项等逻辑
          toDealWithSome() {
            this.localfoodListArry = []
            this.localmlistListArry = []
            this.localmtypeListArry = []
            this.localfoodTypeListArry = []
            let { foodList, mListList } = this.foodInfoItem
            let foodListLen = (foodList && foodList.length) || 0
            let mlistListLen = (mListList && mListList.length) || 0
            let fPriceName = this.priceName("foodList")
            let mPriceName = this.priceName("mList")
            if (foodListLen != 0 || mlistListLen != 0) {
              this.showLittleDIV = true
              if (foodListLen !== 0) {
                foodList.forEach(item => {
                  // 我的细项
                  this.localfoodListArry.push({
                    ...item,
                    upa1: item[fPriceName], //传入动态价格字段
                    allTypeArry: item.allTypeArry || [],
                    qty1: item.qty1 || 1,
                    newOrderItemMListList: [...(this.onDisableItem(item.mListList) || [])],
                    newOrderItemFoodList: [...(this.onDisableItem(item.foodList) || [])],
                    listSelect: item.listSelect || false,
                    isExpired: item.isExpired
                  })
                })
              }

              if (mlistListLen !== 0) {
                mListList.forEach(item => {
                  this.localmlistListArry.push({
                    ...item,
                    qty1: item.qty1 || 1,
                    price: item[mPriceName], //传入动态价格字段
                    allTypeArry: item.allTypeArry || [],
                    newOrderItemMListList: [...(item.mListList || [])],
                    newOrderItemMTypeList: [],
                    listSelect: item.listSelect || false
                  })
                })
              }

              // console.log(
              //   "foodList",
              //   foodList,
              //   "mListList",
              //   mListList,
              //   "allTypeArry",
              //   this.foodInfoItem.allTypeArry || []
              // )
              // console.log(mTypeList, '00');
            } else {
              this.showLittleDIV = false
            }
          },

          // 判断数组是否相同(foodList,foodTypeList,)
          sameArry(a, b) {
            if (a.length !== b.length) {
              // 判断数组的长度
              return false
            } else {
              // 循环遍历数组的值进行比较
              for (let i = 0; i < a.length; i++) {
                if (a[i] !== b[i]) {
                  return false
                }
              }
              return true
            }
          },
          joinCartF() {
            if (!this.showFoodWarp) return //防止食品详情关闭前动画未结束触发多次点击
            let allTypeList = [...(this.foodInfoItem.allTypeArry || [])]
            if (this.foodInfoItem.itemCtrl || this.foodInfoItem._effectiveItemCtrl) {
              this.layerDia(this.systemLanguage.soldOutTip)
              this.foodInfoItem.itemCtrl = true
              return
            }
            if (this.checkMaxFoodNum(this.foodInfoItem, 0, "normal", true, true, "joinCartBtn"))
              return // 判断主food最大数量限制
            if (this.belowFoodTypeNum(this.foodInfoItem)) return //遍历所有套餐项是否小于最小数量
            let allshoplNumber = this.allshoplNumber
            let foodqty1 = this.foodInfoItem.qty1 || 1
            let foodInfoItem = this.foodInfoItem
            // let localfoodListArry = this.localfoodListArry;
            let localfoodListArry = this.onDisableItem(this.localfoodListArry)
            let localmlistListArry = this.onDisableItem(this.localmlistListArry)
            let localmtypeListArry = this.localmtypeListArry
            let localfoodTypeListArry = this.localfoodTypeListArry
            let thereAre = false //储存变量遍历结束判断code
            let ISselect = false //储存变量遍历结束判断selectedItem
            let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
            if (this.joinType == "edit") {
              this.cartFoodReplace(
                localmtypeListArry,
                localfoodTypeListArry,
                localfoodListArry,
                localmlistListArry
              )
              return
            }
            if (allshoplNumber + foodInfoItem.qty1 > this.numberDishes && this.isShowNumberDishes) {
              // 是否大于等于2
              let { promptText } = this.systemLanguage
              promptText = promptText.replace("#numberDishes", this.numberDishes)
              this.layerDia(promptText)
              return
            }
            let fPriceName = this.priceName("foodList")
            let { storeNumber } = this.openTable
            let newfoodObj = {
              storeNumber: foodInfoItem.storeNumber || storeNumber,
              upa1: foodInfoItem[fPriceName], //传入动态价格字段
              desc1: foodInfoItem.desc1,
              desc2: foodInfoItem.desc2,
              nameA: foodInfoItem.nameA,
              nameB: foodInfoItem.nameB,
              multi1: foodInfoItem.multi1,
              fCode: foodInfoItem.fCode,
              k1: foodInfoItem.k1,
              kpName: foodInfoItem.kpName,
              single: foodInfoItem.single,
              newOrderItemFoodList: this.onDisableItem(localfoodListArry),
              newOrderItemMListList: this.onDisableItem(localmlistListArry),
              newOrderItemMTypeList: localmtypeListArry,
              newOrderItemFoodTypeList: localfoodTypeListArry,
              qty1: foodInfoItem.qty1 || 1,
              maxSingleOrderCount: foodInfoItem.maxSingleOrderCount,
              maxSingleQuantityToCart: foodInfoItem.maxSingleQuantityToCart,
              mapCode: foodInfoItem.mapCode,
              seq: foodInfoItem.seq,
              finalSort: foodInfoItem.finalSort,
              t_able: foodInfoItem.t_able || "",
              sc_able: foodInfoItem.sc_able || "",
              discount: foodInfoItem.discount || "",
              fGroup: foodInfoItem.fGroup,
              level: foodInfoItem.level,
              points: foodInfoItem.points,
              ftCode: foodInfoItem.ftCode,
              use_dow: foodInfoItem.use_dow,
              use_date: foodInfoItem.use_date,
              use_time: foodInfoItem.use_time,
              use_dow2: foodInfoItem.use_dow2,
              use_date2: foodInfoItem.use_date2,
              use_time2: foodInfoItem.use_time2,
              minQty: foodInfoItem.minQty,
              maxQty: foodInfoItem.maxQty,
              minQty2: foodInfoItem.minQty2,
              maxQty2: foodInfoItem.maxQty2,
              show_In_FCodes: foodInfoItem.show_In_FCodes,
              not_Show_With_FCodes: foodInfoItem.not_Show_With_FCodes,
              hotSaleMap: foodInfoItem.hotSaleMap,
              photoTime: foodInfoItem.photoTime,
              photoSuffix: foodInfoItem.photoSuffix,
              packingBoxMList: this.useTakeAwayPackag ? foodInfoItem.packingBoxMList : null,
              packingBoxMListCode: this.useTakeAwayPackag ? foodInfoItem.packingBoxMListCode : null,
              ableDiscount: foodInfoItem.ableDiscount,
              tax1: foodInfoItem.tax1,
              tax2: foodInfoItem.tax2,
              tax3: foodInfoItem.tax3,
              hashRecord: foodInfoItem.hashRecord //当分离qty时,添加food但是同步购物车表现为edit,需要字段
            }
            this.updateHashRecord(newfoodObj)
            if (shopCartList && shopCartList.length !== 0) {
              // 有数据
              const lastRecord = newfoodObj.hashRecord[newfoodObj.hashRecord.length - 1]
              const existSameItem = this.findCartEqualHashItem(shopCartList, {
                hash: lastRecord.hash,
                data: { ftCode: newfoodObj.ftCode }
              })
              //购物车存在相同细项的item
              if (existSameItem) {
                existSameItem.qty1 += foodqty1
                this.updateHashRecord(existSameItem)
              } else {
                shopCartList.push(newfoodObj)
                this.addCartSourceData(newfoodObj)
              }
            } else {
              // 没有数据直接添加
              shopCartList.push(newfoodObj)
              // 获取源大food数据,维护shopCartSourceList数组
              this.addCartSourceData(newfoodObj)
            }
            sessionStorage.setItem("shopCartList", JSON.stringify(shopCartList))
            this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList"))

            // console.log(this.shopCartList);
            this.showFoodWarp = false
            // this.resetSelecte();  // 重置数据
            // $('.food_info_warp').fadeOut();
          },
          // 我的细项选择
          sortCartCode(oneFood) {
            let allCodeArry = []
            let foodSetArry = [...oneFood.newOrderItemFoodList]
            if (foodSetArry.length != 0) {
              foodSetArry.forEach(item => {
                let thisItemxiCode = this.onCmyXi(item)
                allCodeArry.push(item.fCode, ...thisItemxiCode)
              })
            }
            if (oneFood.newOrderItemFoodTypeList.length != 0) {
              oneFood.newOrderItemFoodTypeList.forEach(item => {
                allCodeArry.push(item.code)
                if (item.newOrderItemFoodList && item.newOrderItemFoodList.length != 0) {
                  item.newOrderItemFoodList.forEach(i => {
                    let thisItemxiCode = this.onCmyXi(i)
                    allCodeArry.push(i.fCode, ...thisItemxiCode)
                  })
                }
              })
            }
            if (oneFood.newOrderItemMListList.length != 0) {
              oneFood.newOrderItemMListList.forEach(item => {
                let thisItemxiCode = this.onCmyXi(item)
                allCodeArry.push(item.code, ...thisItemxiCode)
              })
            }

            if (oneFood.newOrderItemMTypeList.length != 0) {
              oneFood.newOrderItemMTypeList.forEach(item => {
                allCodeArry.push(item.code)
                if (item.newOrderItemMListList && item.newOrderItemMListList.length != 0) {
                  item.newOrderItemMListList.forEach(i => {
                    let thisItemxiCode = this.onCmyXi(i)
                    allCodeArry.push(i.code, ...thisItemxiCode)
                  })
                }
              })
            }
            // console.log(allCodeArry, "传进来单个Food的code");
            allCodeArry.sort((a, b) => {
              if (a.length == b.length) {
                return b.localeCompare(a)
              } else {
                return b.length - a.length
              }
            })
            return allCodeArry
          },
          sortlocalCode() {
            let allCodeArry = []
            let foodSetArry = [...this.localfoodListArry]
            if (foodSetArry.length != 0) {
              foodSetArry.forEach(item => {
                let thisItemxiCode = this.onCmyXi(item)
                allCodeArry.push(item.fCode, ...thisItemxiCode)
              })
            }
            if (this.localfoodTypeListArry.length != 0) {
              this.localfoodTypeListArry.forEach(item => {
                allCodeArry.push(item.code)
                if (item.newOrderItemFoodList && item.newOrderItemFoodList.length != 0) {
                  item.newOrderItemFoodList.forEach(i => {
                    let thisItemxiCode = this.onCmyXi(i)
                    allCodeArry.push(i.fCode, ...thisItemxiCode)
                  })
                }
              })
            }
            if (this.localmlistListArry.length != 0) {
              this.localmlistListArry.forEach(item => {
                let thisItemxiCode = this.onCmyXi(item)
                allCodeArry.push(item.code, ...thisItemxiCode)
              })
            }

            if (this.localmtypeListArry.length != 0) {
              this.localmtypeListArry.forEach(item => {
                allCodeArry.push(item.code)
                if (item.newOrderItemMListList && item.newOrderItemMListList.length != 0) {
                  item.newOrderItemMListList.forEach(i => {
                    let thisItemxiCode = this.onCmyXi(i)
                    allCodeArry.push(i.code, ...thisItemxiCode)
                  })
                }
              })
            }
            // console.log(allCodeArry, "本地的code");
            allCodeArry.sort((a, b) => {
              if (a.length == b.length) {
                return b.localeCompare(a)
              } else {
                return b.length - a.length
              }
            })
            return allCodeArry
          },
          // 我的细项code遍历(包含追加套餐fty等)
          onCmyXi(everyItem) {
            let evmyxiCode = []
            // foodlist
            if (everyItem.newOrderItemFoodList && everyItem.newOrderItemFoodList.length != 0) {
              everyItem.newOrderItemFoodList.forEach(item => {
                let thisItemxiCode = this.onCmyXi(item)
                evmyxiCode.push(item.fCode, ...thisItemxiCode)
              })
            }
            // fty
            if (
              everyItem.newOrderItemFoodTypeList &&
              everyItem.newOrderItemFoodTypeList.length != 0
            ) {
              everyItem.newOrderItemFoodTypeList.forEach(item => {
                evmyxiCode.push(item.code)
                if (item.newOrderItemFoodList && item.newOrderItemFoodList.length != 0) {
                  item.newOrderItemFoodList.forEach(i => {
                    let thisItemxiCode = this.onCmyXi(i)
                    evmyxiCode.push(i.fCode, ...thisItemxiCode)
                  })
                }
              })
            }
            // mlist
            if (everyItem.newOrderItemMListList && everyItem.newOrderItemMListList.length != 0) {
              everyItem.newOrderItemMListList.forEach(item => {
                let thisItemxiCode = this.onCmyXi(item)
                evmyxiCode.push(item.code, ...thisItemxiCode)
              })
            }
            // mty
            if (everyItem.newOrderItemMTypeList && everyItem.newOrderItemMTypeList.length != 0) {
              everyItem.newOrderItemMTypeList.forEach(item => {
                evmyxiCode.push(item.code)
                if (item.newOrderItemMListList && item.newOrderItemMListList.length != 0) {
                  item.newOrderItemMListList.forEach(i => {
                    let thisItemxiCode = this.onCmyXi(i)
                    evmyxiCode.push(i.code, ...thisItemxiCode)
                  })
                }
              })
            }
            return evmyxiCode
          },
          addData(item, type, vItem) {
            if (item.selected) {
              if (vItem.lNoDup) {
                this.layerDia(this.systemLanguage.lNoDupTxt)

                return // lNoDup为pos设置type下的list不可多选,数量为1
              }
              this.addXiItem(item, type, vItem, this.localAddSMXiFtyArry, this.localAddSMXiMtyArry)
              layer.close(this.foodDialogIndex) // 关闭最外层
              // 第一层数据选择完成
              if (this.itemFoldable) {
                const isMax = this.satisfyMaxQty(vItem)
                //满足最大Qty,折叠当前type
                if (isMax) this.foldType(vItem)
              }
              return
            }
            let foodtypeInList = this.localfoodTypeListArry //细项foodTypeList.foodList

            let mtypeInList = this.localmtypeListArry //细项foodTypeList.foodList

            switch (type) {
              case "foodtypeInList":
                this.radioXi(foodtypeInList, vItem, item.takeUpQty, type)
                this.localfoodTypeListArry = this.mergeFtypeList(
                  foodtypeInList,
                  item,
                  vItem,
                  this.localAddSMXiFtyArry,
                  this.localAddSMXiMtyArry
                )
                break
              case "mtypeInList":
                this.radioXi(mtypeInList, vItem, item.takeUpQty || 1, type)
                this.localmtypeListArry = this.mergeMtypeList(
                  mtypeInList,
                  item,
                  vItem,
                  this.localAddSMXiFtyArry,
                  this.localAddSMXiMtyArry
                )
                break
            }
            // console.log(this.localfoodTypeListArry, this.localmtypeListArry, "追加套餐的数据")
            Vue.set(item, "selected", true)
            this.filterEmptyData() // 过滤空数据fty,mty
            // 第一层数据选择完成
            if (this.itemFoldable) {
              const isMax = this.satisfyMaxQty(vItem)
              //满足最大Qty,折叠当前type
              if (isMax) this.foldType(vItem)
            }
          },
          // 过滤空数据fty,mty
          filterEmptyData() {
            this.localmtypeListArry = this.localmtypeListArry.filter(item => {
              return item.newOrderItemMListList.length != 0
            })
            this.localfoodTypeListArry = this.localfoodTypeListArry.filter(item => {
              return item.newOrderItemFoodList.length !== 0
            })
          },
          // 多选
          multiselect(item, type, vItem) {
            if (item.itemCtrl) return // 判断点击细项是否售罄
            if (vItem.isExpired || item.isExpired) {
              this.showOutTimeTips()
              return
            } // 超时判断是否显示
            if (this.checkLocalSIFCodes(item, 1)) {
              let { limitByFcode, language } = this.openTable
              this.layerDia(limitByFcode[language])
              return
            }
            if ((item.takeUpQty || 1) > vItem.maxQty) {
              console.log("当前or自身选中数量大于type限制数量")
              return
            }
            if (item.selected) {
              if (item.qty1 + item.takeUpQty > (item.inventory || Infinity)) {
                this.layerDia(
                  this.systemLanguage.insufficientInventory.replace("#qty", item.inventory)
                )
                // 库存不足
                return
              }
            }
            if (item.takeUpQty > (item.inventory || Infinity)) {
              this.layerDia(
                this.systemLanguage.insufficientInventory.replace("#qty", item.inventory)
              )
              // 库存不足
              return
            }

            this.clickXiOutItem = vItem
            this.clickXiType = type
            this.editXiItem = item
            let allTypeArry = item.allTypeArry || []
            let arry = [...(item.foodList || []), ...(item.mListList || []), ...allTypeArry]
            // console.log(item, "multiselect...item")
            allTypeArry.forEach(item => {
              if (item.typeName == "ftyItem") {
                item.foodList.forEach(o => {
                  Vue.set(o, "selected", false)
                })
              }
              if (item.typeName == "mtyItem") {
                item.mListList.forEach(o => {
                  Vue.set(o, "selected", false)
                })
              }
            })

            if (arry.length != 0) {
              if (item.selected && vItem.lNoDup) {
                this.layerDia(this.systemLanguage.lNoDupTxt)
                return // 禁止item多数量选择
              }
              // 有细项
              this.resetCurrentDiaNum(item) //打开之前先重置数量
              this.clickXiItem = item
              this.clickOnSource = "fromMulti"
              this.isShowFirstXiDia(item) // 判断是否存在必选细项
            } else {
              // 无细项
              console.log("无细项")
              this.addData(item, type, vItem)
            }
            // console.log(
            //   this.localfoodTypeListArry,
            //   this.localmtypeListArry,
            //   "本地储存的点击细项数组"
            // )
          },
          // 存在可选细项,判断是否必选决定是否需要弹窗(第一层)
          isShowFirstXiDia(item) {
            let typeRes = this.hasRequiredData(item) // false为有必选
            let itemRes = typeof item.minQty2 !== "string" && item.minQty2 > 0 // true为有必选
            const hasRequireFixedItem = this.checkFixedReqiredQty(item)
            let res =
              !typeRes || itemRes || hasRequireFixedItem || this.openTable.modifiersAlwaysDispMods //(modifiersAlwaysDispMods固定弹窗显示)
            if (res) {
              let that = this
              layui.use("layer", function () {
                var layer = layui.layer
                layer.open({
                  skin: "foodDialogLayer",
                  type: 1,
                  shade: [0.1, "#fff"],
                  title: false, //不显示标题
                  content: $(".foodDialog"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
                  success: function (layero, index) {
                    that.foodDialogIndex = index
                  },
                  cancel: function (index, layero) {
                    that.localAllCode = that.oldLocalAllCode
                  },
                  end: function () {
                    that.localAddSMXiFtyArry = []
                    that.localAddSMXiMtyArry = []
                    that.setLocalAllCodeObj("openFoodInfo")
                    that.initFoldType()
                  }
                })
              })
            } else {
              this.onfineItem()
            }
          },
          // 追加套餐点击单个细项item
          addSMXiSelect(item, type, vItem) {
            if (item.itemCtrl) return // 判断点击细项是否售罄
            if (vItem.isExpired || item.isExpired) {
              this.showOutTimeTips()
              return
            }
            this.twoDiaAllCode = JSON.parse(JSON.stringify(this.localAllCode))
            if (this.checkLocalSIFCodes(item, 1, 2)) {
              let { limitByFcode, language } = this.openTable
              this.layerDia(limitByFcode[language])
              return
            }
            if (item.selected) {
              if (item.qty1 + item.takeUpQty > (item.inventory || Infinity)) {
                this.layerDia(
                  this.systemLanguage.insufficientInventory.replace("#qty", item.inventory)
                )
                // 库存不足
                return
              }
            }
            if (item.takeUpQty > (item.inventory || Infinity)) {
              this.layerDia(
                this.systemLanguage.insufficientInventory.replace("#qty", item.inventory)
              )
              // 库存不足
              return
            }
            this.myXiClickXiOutItem = vItem
            this.myXiClickXiType = type
            this.myXiEditXiItem = item
            let allTypeArry = item.allTypeArry || []
            let arry = [...(item.foodList || []), ...(item.mListList || []), ...allTypeArry]
            console.log(item, "追加套餐第一层点击细项")
            allTypeArry.forEach(item => {
              if (item.typeName == "ftyItem") {
                item.foodList.forEach(o => {
                  Vue.set(o, "selected", false)
                })
              }
              if (item.typeName == "mtyItem") {
                item.mListList.forEach(o => {
                  Vue.set(o, "selected", false)
                })
              }
            })

            if (arry.length != 0) {
              if (item.selected && vItem.lNoDup) {
                this.layerDia(this.systemLanguage.lNoDupTxt)
                return // 禁止item多数量选择
              }
              // 有细项
              this.resetCurrentDiaNum(item) //打开之前先重置数量
              this.myXiClickXiItem = item
              this.addDataState = "fromMulti"
              this.isShowSecXiDia(item, type, vItem) // 判断是否存在必选细项
            } else {
              // 无细项
              console.log("无细项")
              this.noXiAdd(item, type, vItem)
            }
            if (this.itemFoldable) {
              const isMax = this.satisfyMaxQty(vItem)
              //满足最大Qty,折叠当前type
              if (isMax) this.foldType(vItem, 1)
            }
          },
          // 存在可选细项,判断是否必选决定是否需要弹窗(第二层)
          isShowSecXiDia(item, type, vItem) {
            let typeRes = this.hasRequiredData(item) // false为有必选
            let itemRes = typeof item.minQty2 !== "string" && item.minQty2 > 0 // true为有必选
            const hasRequireFixedItem = this.checkFixedReqiredQty(item)
            let res =
              !typeRes || itemRes || hasRequireFixedItem || this.openTable.modifiersAlwaysDispMods
            if (res) {
              let that = this
              layui.use("layer", function () {
                var layer = layui.layer
                layer.open({
                  skin: "secondDialogLayer",
                  type: 1,
                  shade: [0.1, "#fff"],
                  title: false, //不显示标题
                  scrollbar: false,
                  content: $(".secondDialog"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
                  success: function (layero, index) {
                    that.secondDialogIndex = index
                  },
                  cancel: function (index, layero) {
                    that.localAllCode = that.twoDiaAllCode
                    console.log(that.localAllCode, "关闭")
                  },
                  end: function () {
                    that.localSecAddSMXiFtyArry = []
                    that.localSecAddSMXiMtyArry = []
                    that.initFoldType(1)
                  }
                })
              })
            } else {
              this.noXiAdd(item, type, vItem)
            }
          },
          noXiAdd(item, type, vItem) {
            if (this.addSMXiVerifyQty2("max", type, item.takeUpQty || 1, vItem)) return
            if ((item.takeUpQty || 1) > vItem.maxQty) {
              return
            }
            if (item.selected) {
              if (vItem.lNoDup) {
                this.layerDia(this.systemLanguage.lNoDupTxt)
                return // lNoDup为pos设置type下的list不可多选,数量为1
              }
              this.addSMXiAddXiItem(
                item,
                type,
                vItem,
                this.localSecAddSMXiFtyArry,
                this.localSecAddSMXiMtyArry
              ) // 增加数量
              return
            }
            let foodtypeInList = this.localAddSMXiFtyArry //细项foodTypeList.foodList
            let mtypeInList = this.localAddSMXiMtyArry //细项foodTypeList.foodList
            switch (type) {
              case "foodtypeInList":
                this.radioXi(foodtypeInList, vItem, item.takeUpQty || 1, type)
                this.localAddSMXiFtyArry = this.mergeFtypeList(
                  foodtypeInList,
                  item,
                  vItem,
                  this.localSecAddSMXiFtyArry,
                  this.localSecAddSMXiMtyArry
                )
                break
              case "mtypeInList":
                this.radioXi(mtypeInList, vItem, item.takeUpQty || 1, type)
                this.localAddSMXiMtyArry = this.mergeMtypeList(
                  mtypeInList,
                  item,
                  vItem,
                  this.localSecAddSMXiFtyArry,
                  this.localSecAddSMXiMtyArry
                )
                break
            }
            Vue.set(item, "selected", true)
            Vue.set(item, "qty1", item.resetQty)
            console.log(this.localAddSMXiFtyArry, "无细项添加")
          },

          getNewOrderItemFoodList(list, item, vItem, newOFtyArry, newOMtyArry) {
            let resObj = {
              upa1: item.upa1,
              desc1: item.desc1,
              desc2: item.desc2,
              multi1: item.multi1,
              fCode: item.fCode,
              nameA: item.nameA,
              nameB: item.nameB,
              k1: item.k1,
              kpName: item.kpName,
              qty1: 1,
              single: item.single,
              mapCode: item.mapCode,
              seq: item.seq,
              fGroup: item.fGroup,
              level: item.level,
              points: item.points,
              t_able: item.t_able,
              sc_able: item.sc_able,
              discount: item.discount,
              listSelect: item.listSelect,
              foodList: item.foodList,
              mListList: item.mListList,
              allTypeArry: item.allTypeArry || [],
              maxQty2: item.maxQty2,
              minQty2: item.minQty2,
              newOrderItemFoodList: [...(this.onDisableItem(item.foodList) || [])],
              newOrderItemMListList: [...(this.onDisableItem(item.mListList) || [])],
              newOrderItemFoodTypeList: [...(newOFtyArry || [])],
              newOrderItemMTypeList: [...(newOMtyArry || [])],
              resetQty: item.resetQty, //重置参考数量
              peerSort: item.peerSort,
              finalSort: item.finalSort,
              itemCtrl: item.itemCtrl,
              ftCode: item.ftCode,
              use_dow: item.use_dow,
              use_date: item.use_date,
              use_time: item.use_time,
              use_dow2: item.use_dow2,
              use_date2: item.use_date2,
              use_time2: item.use_time2,
              minQty: item.minQty,
              maxQty: item.maxQty,
              minQty2: item.minQty2,
              maxQty2: item.maxQty2,
              show_In_FCodes: item.show_In_FCodes,
              not_Show_With_FCodes: item.not_Show_With_FCodes,
              takeUpQty: item.takeUpQty,
              photoTime: item.photoTime,
              photoSuffix: item.photoSuffix,
              packingBoxMList: this.useTakeAwayPackag ? item.packingBoxMList : null,
              packingBoxMListCode: this.useTakeAwayPackag ? item.packingBoxMListCode : null,
              ableDiscount: item.ableDiscount,
              tax1: item.tax1,
              tax2: item.tax2,
              tax3: item.tax3
            }
            let newOrderItemFoodList = []
            for (let i = 0; i < item.resetQty; i++) {
              newOrderItemFoodList.push(resObj)
            }
            const obj = {
              code: vItem.code,
              peerSort: vItem.peerSort,
              finalSort: vItem.finalSort,
              newOrderItemFoodList,
              use_dow: vItem.use_dow,
              use_date: vItem.use_date,
              use_time: vItem.use_time,
              use_dow2: vItem.use_dow2,
              use_date2: vItem.use_date2,
              use_time2: vItem.use_time2,
              minQty: vItem.minQty,
              maxQty: vItem.maxQty,
              lNoDup: vItem.lNoDup,
              show_In_FCodes: vItem.show_In_FCodes,
              not_Show_With_FCodes: vItem.not_Show_With_FCodes
            }
            list.push(obj)
            return list
          },
          mergeFtypeList(list, item, vItem, newOFtyArry, newOMtyArry) {
            if (list.length != 0) {
              for (let i = 0; i < list.length; i++) {
                if (list[i].code == vItem.code) {
                  const orderItem = list[i].newOrderItemFoodList
                  let resObj = {
                    upa1: item.upa1,
                    desc1: item.desc1,
                    desc2: item.desc2,
                    nameA: item.nameA,
                    nameB: item.nameB,
                    multi1: item.multi1,
                    fCode: item.fCode,
                    k1: item.k1,
                    kpName: item.kpName,
                    qty1: 1,
                    single: item.single,
                    mapCode: item.mapCode,
                    seq: item.seq,
                    t_able: item.t_able,
                    sc_able: item.sc_able,
                    discount: item.discount,
                    listSelect: item.listSelect,
                    foodList: item.foodList,
                    mListList: item.mListList,
                    allTypeArry: item.allTypeArry || [],
                    maxQty2: item.maxQty2,
                    minQty2: item.minQty2,
                    newOrderItemFoodList: [...(this.onDisableItem(item.foodList) || [])],
                    newOrderItemMListList: [...(this.onDisableItem(item.mListList) || [])],
                    newOrderItemFoodTypeList: [...(newOFtyArry || [])],
                    newOrderItemMTypeList: [...(newOMtyArry || [])],
                    resetQty: item.resetQty,
                    peerSort: item.peerSort,
                    finalSort: item.finalSort,
                    itemCtrl: item.itemCtrl,
                    ftCode: item.ftCode,
                    use_dow: item.use_dow,
                    use_date: item.use_date,
                    use_time: item.use_time,
                    use_dow2: item.use_dow2,
                    use_date2: item.use_date2,
                    use_time2: item.use_time2,
                    minQty: item.minQty,
                    maxQty: item.maxQty,
                    show_In_FCodes: item.show_In_FCodes,
                    not_Show_With_FCodes: item.not_Show_With_FCodes,
                    takeUpQty: item.takeUpQty,
                    photoTime: item.photoTime,
                    photoSuffix: item.photoSuffix,
                    ableDiscount: item.ableDiscount,
                    packingBoxMList: this.useTakeAwayPackag ? item.packingBoxMList : null,
                    packingBoxMListCode: this.useTakeAwayPackag ? item.packingBoxMListCode : null,
                    tax1: item.tax1,
                    tax2: item.tax2,
                    tax3: item.tax3
                  }
                  for (let i = 0; i < item.qty1; i++) {
                    orderItem.push(resObj)
                  }
                  return list
                  break
                }
              }
              return this.getNewOrderItemFoodList(list, item, vItem, newOFtyArry, newOMtyArry)
            }
            return this.getNewOrderItemFoodList(list, item, vItem, newOFtyArry, newOMtyArry)
          },
          mergeMtypeList(list, item, vItem, newOFtyArry = [], newOMtyArry = []) {
            let mPriceName = this.priceName("mList")
            let itemObj = {
              price: item[mPriceName], //传入动态价格字段,
              code: item.code,
              k1: item.k1,
              kpName: item.kpName,
              name: item.name,
              name2: item.name2,
              name3: item.name3,
              nameA: item.nameA,
              nameB: item.nameB,
              multi1: item.multi1,
              qty1: 1,
              single: item.single,
              mapCode: item.mapCode,
              seq: item.seq,
              fGroup: item.fGroup,
              level: item.level,
              points: item.points,
              discType: item.discType,
              listSelect: item.listSelect || false,
              allTypeArry: item.allTypeArry || [],
              foodList: item.foodList,
              mListList: item.mListList,
              newOrderItemMListList: [...(this.onDisableItem(item.mListList) || [])],
              newOrderItemMTypeList: [...newOMtyArry],
              resetQty: item.resetQty,
              peerSort: item.peerSort,
              finalSort: item.finalSort,
              itemCtrl: item.itemCtrl,
              ftCode: item.ftCode,
              use_dow: item.use_dow,
              use_date: item.use_date,
              use_time: item.use_time,
              packingBoxMList: this.useTakeAwayPackag ? item.packingBoxMList : null,
              packingBoxMListCode: this.useTakeAwayPackag ? item.packingBoxMListCode : null,
              takeUpQty: item.takeUpQty,
              ableDiscount: item.ableDiscount
            }
            let newOrderItemMListList = []
            for (let i = 0; i < item.resetQty; i++) {
              newOrderItemMListList.push(itemObj)
            }
            if (list.length != 0) {
              for (let i = 0; i < list.length; i++) {
                if (list[i].code == vItem.code) {
                  list[i].newOrderItemMListList.push(...newOrderItemMListList)
                  return list
                  break
                }
              }
              const obj = {
                code: vItem.code,
                peerSort: vItem.peerSort,
                finalSort: vItem.finalSort,
                newOrderItemMListList,
                minQty: vItem.minQty,
                maxQty: vItem.maxQty,
                lNoDup: vItem.lNoDup
              }
              list.push(obj)
              return list
            }
            const obj = {
              code: vItem.code,
              peerSort: vItem.peerSort,
              finalSort: vItem.finalSort,
              newOrderItemMListList,
              minQty: vItem.minQty,
              maxQty: vItem.maxQty,
              lNoDup: vItem.lNoDup
            }
            list.push(obj)
            return list
          },
          // foodType，mty套餐选择单选(nextQty1是当前点击的数量)
          radioXi(inList, vItem, nextQty1, type) {
            let max = vItem.maxQty || Infinity
            // let max = 2;

            let vItemList
            let Index = (inList || []).findIndex(inList => inList.code === vItem.code)
            let t = type === "foodtypeInList" ? "newOrderItemFoodList" : "newOrderItemMListList"
            let codeTxt = type === "foodtypeInList" ? "fCode" : "code"
            if (type == "foodtypeInList") {
              vItemList = vItem.foodList
            } else {
              vItemList = vItem.mListList
            }
            // console.log(vItemList, vItem, 'vItem');
            let selectednNum =
              (inList[Index] &&
                inList[Index][t] &&
                inList[Index][t].reduce((total, item) => {
                  return total + (item.takeUpQty || 1)
                }, 0)) ||
              0

            // 已有的数量+当前点击的数量是否大于设定值
            if (selectednNum + nextQty1 > +max) {
              if (Index == -1) return
              //判断需要从头移除多少个

              let removeNum = selectednNum + nextQty1 - +max
              let len = inList[Index][t].length
              for (let i = 0; i < len; i++) {
                if (removeNum <= 0) {
                  break
                } else {
                  let reCode = inList[Index][t][0][codeTxt] //需要移除的code
                  let list = inList[Index][t].filter(e => e[codeTxt] === reCode) //剩余code数量
                  if (list.length === 1) {
                    vItemList.forEach(e => {
                      if (e[codeTxt] === reCode) {
                        this.$set(e, "selected", false)
                        e.qty1 = e.resetQty //重置数量
                      }
                    })
                  } else {
                    vItemList.find(e => e[codeTxt] === reCode).qty1 = list.length - 1
                  }
                  removeNum -= inList[Index][t][0].takeUpQty || 1
                  inList[Index][t].splice(0, 1)
                  if (removeNum <= 0) break
                }
              }
              inList = inList.filter(item => {
                return item[t].length !== 0
              })
            }
          },
          //递归校验固定细项的必选项
          checkFixedReqiredQty(foodData, self = false) {
            const {
              foodList = [],
              mListList = [],
              mTypeList = [],
              foodTypeList = [],
              allTypeArry,
              minQty2,
              maxQty2
            } = foodData
            const dissatisfied = []
            let selectedQty = foodList.length + mListList.length
            // 兼容在递归数据还未添加allTypeArry字段
            selectedQty += this.calcSelectedQty(allTypeArry || foodTypeList.concat(mTypeList))
            foodList.forEach(it => {
              // 递归校验type
              const list = this.getDissatisfiedQtyItem(
                (it.allTypeArry || it.foodTypeList || []).concat(it.mTypeList)
              ).concat(this.checkFixedReqiredQty(it, true))
              if (list.length) dissatisfied.push(it)
            })
            mListList.forEach(it => {
              const list = this.getDissatisfiedQtyItem(it.allTypeArry || it.mTypeList).concat(
                this.checkFixedReqiredQty(it, true)
              )
              if (list.length) dissatisfied.push(it)
            })
            const min = minQty2 == "-Infinity" ? -Infinity : minQty2 || -Infinity
            const max = maxQty2 == "Infinity" ? Infinity : maxQty2 || Infinity
            //校验foodData本身的minQty2和maxQty2
            const satisfySelf = selectedQty >= min && selectedQty <= max
            // 校验当前层且失败,返回自身
            return self && !satisfySelf ? [foodData] : dissatisfied
          },
          // 计算typelist的所有已选数量
          calcSelectedQty(typeList = []) {
            let selectednNum = 0
            for (let i = 0; i < typeList.length; i++) {
              let item = typeList[i]
              let children = item.foodList || item.mListList
              children.forEach(e => {
                if (e.selected) {
                  selectednNum += toNumber(e.qty1) * (e.takeUpQty || 1)
                }
              })
            }
            return selectednNum
          },
          // 校验allTypeArry下的qty,返回不满足的type[]
          getDissatisfiedQtyItem(typeList = []) {
            typeList = typeList.filter(Boolean)
            let list = []
            for (let i = 0; i < typeList.length; i++) {
              let item = typeList[i]
              let min = item.minQty
              let max = item.maxQty
              const count = this.calcSelectedQty([item])
              if (count < min || count > max) list.push(item)
            }
            return list
          },
          showNotSatisfiedQtyHint(list = [], isType = true, isFixed = false) {
            if (!list.length) return
            let arrName = ``
            if (isType) {
              list.forEach(e => {
                arrName += `<p class='tips_p'>${this.outListTitle(e)} : ${this.selectPrompt(
                  e.minQty,
                  e.maxQty
                )}</p>`
              })
            } else {
              if (isFixed) {
                const text = this.systemLanguage.pleaseChooseFixedItemTip
                list.forEach(e => {
                  let value = text.replace("#name", this.inListTitle(e))
                  arrName += `<p class='tips_p'>${value} </p>`
                })
              } else
                list.forEach(e => {
                  arrName += `<p class='tips_p'>${this.inListTitle(e)} : ${this.selectPrompt(
                    e.minQty2,
                    e.maxQty2
                  )}</p>`
                })
            }
            this.layerDia(arrName)
          },
          // 套餐选中数量限制（低于数量逻辑）
          belowFoodTypeNum(data) {
            // 校验固定list的必选qty项
            const fixedList = this.checkFixedReqiredQty(data)
            // 此处type和list分开校验,若要合并根据typeName区分type/list
            if (fixedList.length) {
              this.showNotSatisfiedQtyHint(fixedList, false, true)
              return true
            }
            // 校验type的必选qty项
            const typeList = this.getDissatisfiedQtyItem(data.allTypeArry || [])
            this.showNotSatisfiedQtyHint(typeList, true)
            return !!typeList.length
          },
          // foodInfo页点击confirm校验qty
          isAllowConfirm(data) {
            const { allTypeArry, foodTypeList = [], mTypeList = [] } = data
            const fixed = this.checkFixedReqiredQty(data, true)
            const types = this.getDissatisfiedQtyItem(allTypeArry || foodTypeList.concat(mTypeList))
            return !fixed.concat(types).length
          },
          // 给固定list设置子级是否有必选标识
          setRequiredItemFlag(data) {
            const hasRequiredItem = this.isAllowConfirm(data)
            // 固定foodlist/mlist是否被默认选中--->是否有必选细项
            this.$set(data, "hasRequiredItem", !hasRequiredItem)
            // 控制固定list的选中状态
            this.$set(data, "selected", hasRequiredItem)
          },
          // 将固定细项分组,根据有无必选细项以及type
          groupByHasRequiredItem(list = []) {},
          // 新版切换语言
          onLanSelect(value) {
            this.openTable = {
              ...this.openTable,
              language: value
            }
            let { performType, tableNumber, isEnableTimeSimulation } = this.openTable

            let isTakeAwayMode = this.isTakeAway && performType == 2
            let isTestTimeSimulationMode = this.isTestTable && isEnableTimeSimulation

            if ((isTakeAwayMode || isTestTimeSimulationMode) && this.initTimePickerState) {
              DeliveryTimePicker.destroy()
              this.initTimePicker(value, this.openTable.pickupTime)
            }
            sessionStorage.setItem("openTable", JSON.stringify(this.openTable))
            this.fixLan()
          },

          // 计算文字宽度决定是否滚动
          textSize(fontSize, fontFamily, text) {
            var span = document.createElement("span")
            var result = {}
            result.width = span.offsetWidth
            result.height = span.offsetHeight
            span.style.visibility = "hidden"
            span.style.fontSize = fontSize
            span.style.fontFamily = fontFamily
            span.style.display = "inline-block"
            document.body.appendChild(span)
            if (typeof span.textContent != "undefined") {
              span.textContent = text
            } else {
              span.innerText = text
            }
            result.width = parseFloat(window.getComputedStyle(span).width) - result.width
            result.height = parseFloat(window.getComputedStyle(span).height) - result.height
            document.body.removeChild(span)
            return result
          },
          blackbtn() {
            // window.scrollTo(0, 1);
            // 当开启动画过渡时,在动画未结束时点击图片区域会触发预览图片事件
            $(".food_info_img_Tag").css("pointer-events", "none")
            this.showFoodWarp = false
            this.joinType = "" //重置编辑状态(用户编辑没点击确认直接返回状态未重置)
          },

          /**
           * @description 根据food返回已选中的食品html
           * @param {Object} item food
           * @param {String} xiType 'xi'| 'optonXi' xi为购物车标识.optonXi为套餐细项内标识
           * @param {String} endXi 'inXi' | 'endXi'  item下的第子级标识;inXi:子级的type, endXi: 子级type下的list
           * @returns {String} 返回的html字符串
           * */
          showlogic(item, xiType, endXi = null) {
            let arry = this.flatMyXiArry(item) //扁平化我的细项数组
            let arryText = []
            // console.log(JSON.parse(JSON.stringify(arry)), 'arry')
            let currencyWay = this.currencyWay || "$"
            arry.forEach(item => {
              let itemPrice
              if (item.desc1) {
                itemPrice = item.upa1 && item.upa1 != 0 ? `(${this.showXiPrice(item.upa1)})` : ""
              } else {
                itemPrice = item.price && item.price != 0 ? `(${this.showXiPrice(item.price)})` : ""
              }
              let name = this.inListTitle(item)

              let data = `${name} ${itemPrice}`
              arryText.push(data + this.showlogic(item, "myXi", endXi ? "endXi" : "inXi"))
            })
            let str = ""
            arryText.forEach((item, i) => {
              if (xiType == "myXi" || xiType == "optonXi") {
                if (endXi == "endXi") {
                  str += item + "+"
                } else {
                  str += `<p class='littleitem-inXi'>${item}</p>`
                }
              } else {
                str += `<p>${item}</p>`
              }
            })
            // console.log(str, 'arryText');
            if (str.length > 0) {
              if (str[str.length - 1] === "+") {
                str = str.substring(0, str.length - 1)
              }
              if (xiType == "myXi") {
                if (endXi == "endXi") {
                  return `<p class='littleitem-endXi'>[${str}]</p>`
                } else {
                  return `<p class='littleitem-inXi'>${str}</p>`
                }
              } else {
                return str
              }
            } else {
              return ""
            }
          },
          // 计算总价时的排除逻辑处理
          calculatedTotalExclude(exclude) {
            //需要排除的fcode/ftcode
            let excludeFcodes = []
            let excludeFtcodes = []
            let excludeMlcodes = []
            let excludeMtcodes = []
            exclude.forEach(el => {
              if (el.fcode) {
                excludeFcodes.push(...el.fcode)
              }
              if (el.ftcode) {
                excludeFtcodes.push(...el.ftcode)
              }
              if (el.mlcode) {
                excludeMlcodes.push(...el.mlcode)
              }
              if (el.mtcode) {
                excludeMtcodes.push(...el.mtcode)
              }
            })

            return { excludeFcodes, excludeFtcodes, excludeMlcodes, excludeMtcodes }
          },
          // 计算总价格（包含细项）
          //tip:Boolean 用于区分模板显示(true)还是函数逻辑计算
          calculatedTotal(cartItem, exclude = []) {
            //需要排除的fcode/ftcode
            let { excludeFcodes, excludeFtcodes, excludeMlcodes, excludeMtcodes } =
              this.calculatedTotalExclude(exclude)
            // 若排除列表中存在cartItem的fcode,则food价格按照0计算
            let basePrice =
              excludeFcodes.includes(cartItem.fCode) || excludeFtcodes.includes(cartItem.ftCode)
                ? 0
                : cartItem.upa1 || 0
            let arry = this.flatAllXi(cartItem)
            arry.forEach(priceItem => {
              if (priceItem.upa1 && priceItem.upa1 != 0) {
                // 排除列表都不存在,则加上价格
                let allowed = !(
                  excludeFcodes.includes(priceItem.fCode) ||
                  excludeFtcodes.includes(priceItem.ftCode)
                )
                if (allowed) {
                  basePrice = floatAdd(basePrice, priceItem.upa1)
                }
              } else if (priceItem.price && priceItem.price != 0) {
                let mtcode = this.matchMtycode(priceItem.code)
                // 排除列表都不存在,则加上价格
                let allowed = !(
                  excludeMlcodes.includes(priceItem.code) || excludeMtcodes.includes(mtcode)
                )
                if (allowed) {
                  basePrice = floatAdd(basePrice, priceItem.price)
                }
              }
            })
            let isNull =
              cartItem.qty1 == "false" || cartItem.qty1 === null || cartItem.qty1 === void 0
            // ?? 空值合并运算符support chrome 80
            cartItem.qty1 = isNull ? 1 : cartItem.qty1
            let totalQty = cartItem.qty1 * (cartItem.takeUpQty || 1)
            return retainSignificantDecimals(floatMultiply(basePrice, totalQty))

            // return basePrice * cartItem.qty1
          },
          // 扁平化到第一层
          flatMyXiArry(item) {
            // 全部用finalSort排序,固定返回字段
            const sort = (list = []) => list.toSorted((a, b) => a.finalSort - b.finalSort)
            let arry = sort([
              ...(item.newOrderItemFoodList || []),
              ...(item.newOrderItemMListList || [])
            ])
            let newOrderItemFoodTypeList = item.newOrderItemFoodTypeList || []
            let newOrderItemMTypeList = item.newOrderItemMTypeList || []
            let allTypeArry = sort([...newOrderItemFoodTypeList, ...newOrderItemMTypeList])
            if (allTypeArry.length != 0) {
              allTypeArry.forEach(item => {
                if (item.newOrderItemFoodList && item.newOrderItemFoodList.length != 0) {
                  item.newOrderItemFoodList.forEach(i => {
                    arry.push(i)
                  })
                } else if (item.newOrderItemMListList && item.newOrderItemMListList.length != 0) {
                  item.newOrderItemMListList.forEach(i => {
                    arry.push(i)
                  })
                }
              })
            }
            if (this.useTakeAwayPackag && item.packingBoxMList) {
              arry.push(item.packingBoxMList)
            }
            return arry
          },
          // 扁平化第一层后面所有
          flatAllXi(cartItem) {
            if (!cartItem) return
            let allItem = []
            let cartItemNewFoodList = cartItem.newOrderItemFoodList || []
            let cartItemNewMList = cartItem.newOrderItemMListList || []
            let cartItemNewFtyList = cartItem.newOrderItemFoodTypeList || []
            let cartItemNewMtyList = cartItem.newOrderItemMTypeList || []
            cartItemNewFoodList.forEach(e => {
              let i = this.flatAllXi(e)
              allItem.push(e, ...i)
            })
            cartItemNewMList.forEach(e => {
              let i = this.flatAllXi(e)
              allItem.push(e, ...i)
            })
            cartItemNewFtyList.forEach(e => {
              let i = this.flatAllXi(e)
              allItem.push(...i)
            })
            cartItemNewMtyList.forEach(e => {
              let i = this.flatAllXi(e)
              allItem.push(...i)
            })
            if (this.useTakeAwayPackag && cartItem.packingBoxMList) {
              allItem.push(cartItem.packingBoxMList)
            }
            return allItem
          },

          // 是否符合时间
          /*
           * return boolean
           * true: 未过期
           * false :已过期
           * @params data: 后台返回的数据
           * @params pickupTime: 取餐预约时间 yyyy/mm/dd HH:mm
           * */
          consistentTimePeriod(data, pickupTime) {
            if (
              !(
                data.use_dow2 ||
                data.use_date2 ||
                data.use_time2 ||
                data.use_dow ||
                data.use_date ||
                data.use_time
              )
            )
              return true
            // if (!(use_dow || use_date || use_time)) return true; //直接显示
            let timeStamp = moment().valueOf()
            // let timeStamp = moment(undefined).valueOf();
            //后台返回时间+启动计时器时间
            let currentTime = pickupTime ? pickupTime : moment(timeStamp).format("YYYY/MM/DD HH:mm")
            let currentTimeArry = currentTime.trim().split(" ") //獲取系统时间 2020/09/16
            let nowCurrentDate = currentTimeArry[0]
            let nowCurrentTime = currentTimeArry[1]
            // 三个其中某个有值或者都有值分别判断三个值是否符合
            let useDowRes = this.checkUseDowRes(
              data.use_dow2 || data.use_dow,
              timeStamp,
              nowCurrentDate
            )
            let useDateRes = this.checkUseDateRes(
              data.use_date2 || data.use_date,
              currentTime,
              nowCurrentDate
            )
            let useDowAndTime = this.checkUseDowAndTime(
              data.use_time2 || data.use_time,
              timeStamp,
              nowCurrentTime,
              useDowRes,
              nowCurrentDate
            )
            return useDateRes && useDowAndTime
          },
          checkUseDowRes(use_dow, timeStamp, nowCurrentDate) {
            if (use_dow) {
              let getDay = timeStamp ? moment(timeStamp).day() : moment(nowCurrentDate).day() //獲取當天星期幾
              let replaceUse_dow = use_dow.replace("7", "0") //转换数据7=>0 一周七天
              // console.log(replaceUse_dow, '星期')
              let useDowRes = false
              inHolidayData = false //当天日期是否在假期日期内
              let { holidayList } = this.openTable
              // 判断当天日期是否在假期日期内
              if (holidayList && holidayList.length != 0) {
                for (let i = 0; i < holidayList.length; i++) {
                  const item = holidayList[i]
                  let t1 = moment(nowCurrentDate).format("YYYY/MM/DD")
                  if (item.fmDate && item.toDate) {
                    // 两种日期都有
                    let t2 = moment(item.fmDate).format("YYYY/MM/DD")
                    let t3 = moment(item.toDate).format("YYYY/MM/DD")
                    if (moment(t1).isBetween(t2, t3, null, "[]")) {
                      inHolidayData = true
                      break
                    }
                  } else if (item.fmDate) {
                    // 只存在fmDate
                    if (moment(item.fmDate).isSameOrBefore(t1)) {
                      inHolidayData = true
                      break
                    }
                  } else {
                    // 只存在toDate
                    if (moment(t1).isSameOrBefore(item.toDate)) {
                      inHolidayData = true
                      break
                    }
                  }
                }
              }
              // console.log(inHolidayData, '是否在假期内')
              if (use_dow.indexOf("H") != -1) {
                if (inHolidayData) {
                  //有H且在假期日期内
                  useDowRes = true
                } else {
                  //有H且不在假期日期内,判断1-7
                  if (replaceUse_dow.indexOf(getDay) != -1) useDowRes = true
                }
              } else {
                //无H
                if (inHolidayData) {
                  useDowRes = false //在假期内的日期但没H所以要隐藏
                } else {
                  // 不在假期日期内,判断当天星期几是否在设置的星期内
                  if (replaceUse_dow.indexOf(getDay) != -1) useDowRes = true
                }
              }
              // console.log(useDowRes, 'useDowRes')
              return useDowRes
            } else {
              return true
            }
          },

          checkUseDateRes(use_date, currentTime, nowCurrentDate) {
            if (use_date) {
              let useDateArr = use_date.split(";")
              useDateArr = useDateArr.filter(i => i && i.trim()) //过滤空
              let getUseDate = false
              for (let i = 0; i < useDateArr.length; i++) {
                const item = useDateArr[i].replace(/\./g, "/")
                let useDateInArr = item.split("-") //獲取日期 2020.06.01-2020.07.01
                let t1 = moment(nowCurrentDate).format("YYYY/MM/DD")
                let t2 = moment(useDateInArr[0]).format("YYYY/MM/DD")
                let t3 = moment(useDateInArr[1]).format("YYYY/MM/DD")
                let validMinDate = this.validFormat(useDateInArr[0], "YYYY/MM/DD")
                let validMaxDate = this.validFormat(useDateInArr[1], "YYYY/MM/DD")
                if (!validMinDate && validMaxDate) {
                  // 第一个日期不符合,第二个日期符合,判断是否第二日期以前
                  if (moment(t1).isSameOrBefore(t3)) {
                    getUseDate = true
                    break
                  }
                } else if (validMinDate && !validMaxDate) {
                  // 第一个日期符合,第二个日期不符合,判断是否第一日期以后
                  if (moment(t1).isSameOrAfter(t2)) {
                    getUseDate = true
                    break
                  }
                } else if (!validMinDate && !validMaxDate) {
                  // 都不符合(任何时候都可以用)
                  getUseDate = true
                  break
                } else {
                  // 都符合
                  if (moment(t1).isBetween(t2, t3, null, "[]")) {
                    getUseDate = true
                    break
                  }
                }
              }
              return getUseDate
            } else {
              return true
            }
          },

          //兩個時間對比 格式：02:00:00
          checkUseDowAndTime(use_time, timeStamp, nowCurrentTime, useDowRes, nowCurrentDate) {
            // 优先判断当前星期几是否符合use_dow2
            if (useDowRes) {
              // 符合星期几
              if (use_time) {
                let useTimeArr = use_time.split(";")
                // console.log(useTimeArr, 'useTimeArr');
                let noDateTime = false //纯小时-分钟数据格式
                let hasDateTime = false //日期/小时:分钟格式
                let noDateTimeArr = [] //没有日期/小时:分钟格式数据的数组
                let hasDateTimeArr = [] //有日期/小时:分钟格式数据的数组
                let hasDateOfTheDay = false // 日期/小时:分钟格式的日期数据
                for (let i = 0; i < useTimeArr.length; i++) {
                  const item = useTimeArr[i]
                  if (!item) continue
                  if (item.indexOf("/") != -1) {
                    hasDateTimeArr.push(item)
                  } else {
                    noDateTimeArr.push(item)
                  }
                }
                // 存在日期/小时:分钟格式数据(i.g. 23/00:00-23/23:59)
                if (hasDateTimeArr.length) {
                  // 遍历日期/小时:分钟格式数据判断是否符合时间
                  for (let i = 0; i < hasDateTimeArr.length; i++) {
                    const item = hasDateTimeArr[i]
                    const dateTimeCombination = item.split("/")
                    const combinationDate = dateTimeCombination[0] // use_time2日期
                    const combinationTime = dateTimeCombination[1] // use_time2时间
                    const checkCombinationDate = this.checkUseDowRes(
                      combinationDate,
                      timeStamp,
                      nowCurrentDate
                    )
                    // console.log(combinationDate, checkCombinationDate, '/日期今天是否符合');
                    if (checkCombinationDate) {
                      // 符合星期几,判断时分是否符合
                      hasDateOfTheDay = true // 存在符合当天日期的日期/小时:分钟格式数据
                      const checkHoursMinutes = this.individualTimeJudgment(
                        combinationTime,
                        nowCurrentTime
                      )
                      if (checkHoursMinutes) {
                        // 当前时间也符合
                        hasDateTime = true
                        break
                      }
                    } else {
                      // console.log('日期不符合直接pass');
                    }
                  }
                }
                // 存在纯小时-分钟数据格式(i.g. 00:00-23:59)
                if (noDateTimeArr.length) {
                  // 遍历纯小时-分钟数据格式判断是否符合时间
                  for (let i = 0; i < noDateTimeArr.length; i++) {
                    const item = noDateTimeArr[i]
                    const checkHoursMinutes = this.individualTimeJudgment(item, nowCurrentTime)
                    if (checkHoursMinutes) {
                      // 当前时间也符合
                      noDateTime = true
                      break
                    }
                  }
                } else {
                  // 不存在纯小时-分钟数据格式并且当天日期/小时:分钟格式数据存在
                  // i.g. 23/00:00-23/23:59 当天星期2
                  if (!hasDateOfTheDay) noDateTime = true
                }
                // console.log(noDateTime, hasDateTime, 'noDateTime,hasDateTime');
                return noDateTime || hasDateTime
              } else {
                //use_time2无数据则全天显示
                return true
              }
            } else {
              // 不符合use_dow2的日期直接返回false
              return false
            }
          },

          // 验证是否符合日期格式
          validFormat(date, inputFormat) {
            var validation = moment(moment(date).format(inputFormat)).inspect()
            if (validation.indexOf("invalid") < 0) return true
            else return false
          },
          // 单独进行时间判断
          individualTimeJudgment(item, nowCurrentTime) {
            let useTimeInArr = item.split("-") //獲取時間 09:00-12:00
            let format = "hh:mm"
            let time = moment(nowCurrentTime, format),
              beforeTime = moment(useTimeInArr[0], format),
              afterTime = moment(useTimeInArr[1], format)
            // 超过一天的时间判断
            if (afterTime.isBefore(beforeTime)) {
              if (time.isAfter(afterTime)) afterTime.add(1, "days")
              else beforeTime.subtract(1, "days")
            }
            if (time.isBetween(beforeTime, afterTime, null, "[]")) {
              return true
            } else {
              return false
            }
          },
          headerTouchStart(e) {
            // 固定元素滑动，浮动起来
            this.fixed = true
          },
          headerTouchMove(e) {
            // 组织默认事件，防止跳动
            e.preventDefault()
          },
          headerTouchEnd(e) {
            // 互动结束，浮动解除，防止滚动元素无法滚动
            this.fixed = false
          },
          contentTouchStart(e) {
            // console.log(e);
            this.contentStartY = e.changedTouches[0].clientY
            this.contentStartX = e.changedTouches[0].pageX
          },
          contentTouchMove(e) {
            let endY = e.changedTouches[0].clientY
            // 获取滚动的距离
            let diff = endY - this.contentStartY
            let elName = this.contentWarpStyle[this.clickLayout]
            let scrollTop = $(elName).scrollTop()
            // 如果拉到顶了还继续往下拉
            if (diff > 0 && scrollTop <= 0) {
              this.fixed = true
              e.preventDefault()
            } else if (diff < 0) {
              // 如果没有拉到顶，则正常滑动内容栏
              this.fixed = false
            }
          },
          contentTouchEnd(e) {
            this.fixed = false
          },
          // 是否展开合并fty的弹窗
          openMergeFtyWrap(type) {
            return this.merge_fty.hasOwnProperty(type.code) && !this.openTable.combinedNavigation
          },
          // 新增fty主页
          onfty(index, data) {
            this.tabData = data
            if (this.openMergeFtyWrap(data)) {
              this.fty_data = data
              this.openFty_layer = true
              layer.open({
                type: 1,
                title: false,
                closeBtn: 0,
                shadeClose: true,
                area: ["85%", "70vh"],
                skin: "tip_Fty_merge",
                content: $(".merge_content")
              })
              //
            } else {
              this.ftSwitch = false
              this.onfty_anchor(data.code)
            }
          },
          // 跳转到某个fty锚点
          onfty_anchor(code) {
            this.tabIsActive = code
            // console.log("🚀 ~ onfty_anchor ~ this.tabIsActive:", this.tabIsActive)
            let foodType = this.allDataList.find(el => el.code === code)
            //index与循环的index对应可自由设置动态获取
            this.foodList = foodType.foodList
            this.clickLayout = foodType.display_column
            this.$nextTick(() => {
              let { verticalOrderLayout, infiniteLoop } = this.openTable
              if (verticalOrderLayout || infiniteLoop) {
                this.goAnchor(code)
              }
            })
          },

          //fty分类进入主页
          onfty_merge(data) {
            if (data.hasOwnProperty("isExpired") && data.isExpired) {
              return
            }
            // let index = this.allDataList.findIndex(el => {
            //   return el.name === data.name && el.code === data.code
            // })
            layer.close(layer.index)
            this.ftSwitch = false
            layer.closeAll()
            this.onfty_anchor(data.code)
          },
          // 返回fty主页按钮
          backFtyBtn() {
            this.ftSwitch = true
            // redirectAfterLogin 存在 则返回fty页时 滚当到当前fty
            let { ftyPage, redirectAfterLogin } = this.openTable
            if (!ftyPage || !redirectAfterLogin) return
            this.backFtyPageRedirectFty()
          },
          backFtyPageRedirectFty() {
            let { redirectAfterLogin } = this.openTable
            let redirectCode = redirectAfterLogin //若存在合并,最终滚动的位置的code,可能是父盒子和它本身
            let isMergeRedirectChildCode = this.merge_codes.includes(redirectAfterLogin)
            let isMergeRedirectParentCode = this.merge_fty.hasOwnProperty(redirectAfterLogin)
            //  存在300ms的动画
            setTimeout(() => {
              // 找到当前滚动目标fty的index,滚动到位置:
              //   若滚动fty被合并,找到主盒子
              if (isMergeRedirectChildCode) {
                for (let code in this.merge_fty) {
                  if (this.merge_fty[code].includes(redirectAfterLogin)) {
                    redirectCode = code
                    break
                  }
                }
              }
              // 滚动目标fty index
              let scrollIndex = this.allDataList.findIndex(fty => fty.code == redirectCode)
              let scrollData = this.allDataList[scrollIndex]
              let scrollDOM = $(`.app_fty_container >[code=${redirectCode}]`)
              let redirectDOM = scrollDOM
              scrollDOM[0].scrollIntoView({
                behavior: "instant",
                block: "center",
                inline: "nearest"
              }) //滚动

              // 当开启组合Tab栏目时,不会弹出合并盒子弹窗
              if (!this.openTable.combinedNavigation) {
                // 若为合并fty,打开盒子弹窗
                if (isMergeRedirectParentCode || isMergeRedirectChildCode) {
                  this.onfty(scrollIndex, scrollData)
                }
                // 若 redirectCode ===  redirectAfterLogin ,则为滚动目标为重定向目标(没有合并|| 合并本身)
                if (redirectCode !== redirectAfterLogin) {
                  // 试图改变后(盒子打开),找到重定向dom, 添加动画
                  this.$nextTick(() => {
                    let mergeIndex = this.getMerge_data(scrollData).findIndex(
                      fty => fty.code === redirectAfterLogin
                    )
                    redirectDOM = $(".tip_Fty_merge .merge_box").children().eq(mergeIndex)
                  })
                }
              }

              this.$nextTick(() => {
                if (!isMergeRedirectParentCode) {
                  redirectDOM.addClass("stressKeyFtyAnimate")
                }
                // 2个半循环后移除闪烁动画
                redirectDOM.delay(3000).queue(function () {
                  $(this).removeClass("stressKeyFtyAnimate").dequeue()
                })
              })
            }, 310)
          },

          checkImgExists(imgurl) {
            return new Promise(function (resolve, reject) {
              var ImgObj = new Image()
              ImgObj.src = imgurl
              ImgObj.onload = function (res) {
                resolve(res)
              }
              ImgObj.onerror = function (err) {
                reject(err)
              }
            })
          },
          aboutFtyImg(ftyItem) {
            if (!ftyItem) return
            let { code, photoTime, bannerPhotoTime, bannerPhotoSuffix } = ftyItem
            this.$set(ftyItem, "showFtyimg", true) //塞ftyimg字段默认为true
            //处理Ftybanner图片
            if (this.openTable.infiniteLoop && bannerPhotoTime) {
              let bannerImgUrl = `${this.ftyBaseUrl}banner/${code}.${bannerPhotoSuffix}?x-oss-process=image/resize,h_200,${bannerPhotoTime}`
              this.$set(ftyItem, "bannerImgUrl", bannerImgUrl) //塞ftyimg字段默认为true
            }
          },
          //无效fty图片回调
          errorFtyImgCallback(ftyItem, type) {
            // console.log("图片加载失败", ftyItem.code, ftyItem.fType_nameB)
            if (type == "banner") {
              this.$set(ftyItem, "bannerImgUrl", "")
            } else {
              this.$set(ftyItem, "showFtyimg", false)
            }
          },
          // 购物车页面
          onBack() {
            if (this.showCartTab) {
              let { editCartDataTip } = this.systemLanguage
              let shopCartList = this.shopCartList
              if (shopCartList.length != 0 && this.cartEditXi) {
                this.layerDia(editCartDataTip)
                return
              }
              this.showCartTab = false
            }
            if (this.showSendOrderView) {
              this.showSendOrderView = false
              this.showCartTab = true
            }
          },

          //计算重复food
          dittoFood(data = []) {
            console.log(data, "datadata")
            let lan = this.openTable.language
            let descStr = ""
            data.forEach(el => {
              descStr += this.inListTitle(el) + ", "
            })
            descStr = descStr.substring(0, descStr.lastIndexOf(","))
            return descStr
          },

          itemCtrlItemName(data) {
            if (!data) return
            let { itemCtrlError, itemCtrlAmountText } = this.systemLanguage
            let lan = this.openTable.language
            let str = ""
            for (let key in data) {
              str += `<strong>${key}</strong> ${itemCtrlAmountText}<strong> ${data[key]}</strong> <br/> `
            }
            str += itemCtrlError
            return str
          },
          // 下单失败状态提示
          subOrderErrorTip(resultObj, data) {
            let {
              errorMsg,
              errorTimeout,
              errorDesc,
              errorFoodTimeOut,
              errorVerifyTime,
              foodRepeatMsg,
              confirmTitle,
              confirmOrderAgain,
              cancelBtn,
              itemCtrlError,
              errorQrCodeTip,
              errorPreOrderTip,
              errorFindHistoryTip,
              errorServiceNotOpenTip,
              errorCashierNotOpenTip,
              errorServiceTip,
              errorPaymentService,
              errorGiveAway,
              errorDiscount,
              errorTableKey,
              staticQRCodeNoOpenTableError,
              cutleryNotOpenText,
              noTouchNotOpenText,
              takeawayTimeoutPrompt,
              takeawayClosePrompt,
              dineInTimeoutPrompts,
              dineInPrompts,
              pickupTimeTimeoutPrompt,
              PCLoginMemberNotOpen,
              PCLoginMemberTimeout,
              totalPriceTooLow,
              additionalItemsErr,
              unprocessedOrderTip,
              MDOrderStartTimeout,
              btnTxtForCancel,
              btnTxtForConfirm,
              maxSalesCountError,
              orderDiscountConflictMsg,
              captchaError,
              insufficientBalance,
              accountNotFoundText,
              memberExpired,
              discountPriceMismatch,
              autoFindNewDiscount,
              cartDataVersionInvalid
            } = this.systemLanguage
            switch (resultObj.statusCode) {
              case 210: //有未处理订单，是否继续下单
                let repeatParams = { ...data, checkMerchantRef: false }
                this.orderAgainConfirm(unprocessedOrderTip, repeatParams)
                break
              case 2002: //折扣优惠券与其他存在冲突
                this.msgTips(orderDiscountConflictMsg, "#ef5350")
                break
              case 400:
                // 若为会员折扣,接口错误则不提示弹窗
                if (!this.isMemberDiscount()) {
                  this.msgTips(this.systemLanguage.discountPamErr, "#ff5252")
                }
                break
              case 4003: //相隔时间内不允许下单
                let timeStr = this.error4003TimeStr(parseInt(resultObj.errorDesc))
                layer.confirm(errorTimeout + " " + timeStr, {
                  title: false,
                  closeBtn: false,
                  skin: "baseLayer layui-custom-style",
                  area: ["85%"],
                  btn: [btnTxtForCancel] //按钮
                })
                break
              case 4004:
                let { language } = this.openTable
                let lanObj = {
                  en: 0,
                  zh: 1,
                  thirdLan: 2
                }
                let index = lanObj[language]
                let msg = resultObj.timeOutName
                  ? resultObj.timeOutName[index] + " " + errorFoodTimeOut
                  : errorFoodTimeOut
                this.layerDia(msg)
                break
              case 4005:
                layer.msg(errorVerifyTime) //提示层
                break
              case 401:
                layer.msg(errorQrCodeTip) //提示层
                break
              case 402:
                layer.msg(errorFindHistoryTip) //提示层
                break
              case 403:
              case 4008:
                layer.msg(errorMsg) //提示层
                break
              case 4013:
                layer.msg(discountPriceMismatch)
                break
              case 404:
                layer.msg(errorServiceNotOpenTip) //提示层
                break
              case 405:
                layer.msg(errorPreOrderTip) //提示层
                break
              case 407:
                layer.msg(errorCashierNotOpenTip) //提示层
                break
              case 408:
                let autoDiscount = sessionStorage.getItem("couponUseType") === "auto"
                this.error408CallBack(resultObj, autoDiscount)
                if (autoDiscount) {
                  layer.msg(autoFindNewDiscount) //提示层
                  this.checkDiscountAgain()
                }
                break
              case 409:
                layer.msg(MDOrderStartTimeout)
                break
              case 411:
                layer.msg(maxSalesCountError)
                break
              case 4006: //外卖/堂食;店铺超时
                let timeoutPrompt = this.isTakeAway
                  ? resultObj.errorDesc || takeawayTimeoutPrompt
                  : resultObj.errorDesc || dineInTimeoutPrompts
                return timeOutPromptPop(timeoutPrompt, "menuPage")
              case 4007:
                layer.msg(errorGiveAway)
                break
              case 4011: //外卖/堂食;店铺关闭
                let closePrompt = this.isTakeAway ? takeawayClosePrompt : dineInPrompts
                return timeOutPromptPop(closePrompt, "menuPage")
                break
              case 4012: //预约的时间早于店铺当前的时间
                layer.msg(pickupTimeTimeoutPrompt)
                break
              case 4014: //未登录但已选会员food
                this.msgTips(this.systemLanguage.memberFoodErr, "#ff5252", false)
                setTimeout(this.showUserPopup, 1600)
                // this.showUserPopup()
                break
              case 4015: // FB外卖模式下Additional items for takeaway数据错误
                layer.msg(additionalItemsErr)
                break
              case 4016: // 限制主food最大下单数量
                let { foodName, maxNum } = resultObj
                let tip = this.error4016Str(foodName, maxNum)
                layer.confirm(tip, {
                  title: false,
                  closeBtn: false,
                  skin: "baseLayer layui-custom-style",
                  area: ["85%"],
                  btn: [btnTxtForCancel] //按钮
                })
                break
              case 4017:
                layer.msg(totalPriceTooLow)
                break
              case 501:
                layer.msg(errorPaymentService)
                break
              case 500:
                layer.msg(errorServiceTip)
                break
              case 507:
                let str = this.itemCtrlItemName(resultObj.itemCtrlMap)
                this.layerDia(str)
                break
              case 5001: // 服务端cookie已经失效,需要重新登录
                let call = sessionStorage.getItem("notInviteLogin") ? () => {} : this.showUserPopup
                layer.msg(PCLoginMemberTimeout, call)
                break
              case 5003:
                layer.msg(errorTableKey)
                break
              case 5005:
                let { tableNumber } = this.openTable
                //staticQRCodeNoOpenTableError字符串里面替换#tableNumber为当前桌号
                layer.msg(staticQRCodeNoOpenTableError.replace("#tableNumber", tableNumber)) //未开台)
                break
              case 5101:
                layer.msg(captchaError) //钱包验证码错误
                break
              case 5102:
                layer.msg(insufficientBalance) //钱包余额不足
                break
              case 5103:
                layer.msg(memberExpired) //会员过期
                break
              case 5104:
                layer.msg(accountNotFoundText) //查无账号
                break
              case 6006: {
                layer.msg(errorDesc) //提示层
                break
              }
              case 7001: {
                let content =
                  this.dittoFood(resultObj.newOrderItemFoodTemp) + "<br>" + foodRepeatMsg
                let repeatParams = {
                  ...data,
                  repeatFoodCode: resultObj.repeatFoodCode
                }
                this.orderAgainConfirm(content, repeatParams)

                break
              }
              case 1101: {
                //购物车版本数据已落单
                const { version } = resultObj.shoppingCart
                if (version !== this.sseCartVersion) {
                  this.reconnectSSE()
                }
                layer.msg(cartDataVersionInvalid)
                break
              }

              default:
                layer.msg(errorMsg) //提示层
            }
          },

          /**
           * @description 验证店铺是否打烊
           * @return {boolean} true:打烊 false:未打烊
           *  */
          verifyStoreClosed() {
            let { openingDates = [], openingHours = [] } = this.openTable
            //   openingDates: ["2020-01-01", "2020-01-02"], openingHours:["00:00:00","23:59:59"]
            let formatDate = "YYYY-MM-DD"
            let formatTime = "HH:mm:ss"

            const now = moment() // 获取当前时间
            let isBetween = true // 初始化 isBetween 变量为 true

            // 判断开始日期和结束日期是否存在，如果存在则判断当前日期是否在时间段之间
            if (openingDates.length === 2) {
              const startDate = moment(openingDates[0])
              const endDate = moment(openingDates[1])
              isBetween = now.isBetween(startDate, endDate, "day", "[]")
            }

            // 判断开始时间和结束时间是否存在，如果存在则判断当前时间是否在时间段之间
            if (openingHours.length === 2) {
              const startTime = moment(openingHours[0], formatTime)
              const endTime = moment(openingHours[1], formatTime)
              if (startTime.isValid() && endTime.isValid()) {
                isBetween = isBetween && now.isBetween(startTime, endTime, "second", "[]")
              }
            }
            return !isBetween
          },
          //点击送单按钮
          onSubOrderBtn() {
            const toOrder = async () => {
              const allowedOrder = await this.checkBeforeOrder()
              if (!allowedOrder) return
              this.sendOrder()
            }
            // MD / ...
            if (this.openTable.performType !== 2) {
              toOrder()
              return false
            }
            //FB
            if (this.showSendOrderView) {
              toOrder()
              return false
            }

            // 已下过单||购物车编辑状态||购物车无内容
            if (this.disableSendOrder || this.cartEditXi || !this.shopCartList.length) {
              return false
            }
            // 打开提交订单页面
            const canShowOrderView =
              this.allPriceCache != 0 ? this.getAllPayMethod.length : this.hasRequiredPersonalInfo
            this.showSendOrderView = !this.showSendOrderView && canShowOrderView

            // 兼容foodCourt 模式  takeaway sub dialog 图片替换
            if (this.isFoodCourtMode && this.isTakeAway && !this.storePhotoUrl.includes("/*/")) {
              this.storePhotoUrl = null
              this.getConfigureImg(["Store photo"])
            }
            if (!this.choosePayMethod) this.choosePayMethod = this.getAllPayMethod[0]

            // 没有支付方式/零元落单
            if (!this.showSendOrderView) {
              toOrder()
            }
          },
          // 落单前的校验
          async checkBeforeOrder() {
            const {
              tableNumber,
              performType,
              language,
              timeoutPrompts,
              confirmationPopup,
              serviceCharges: { displayInShoppingCartPage } = {},
              billTax
            } = this.openTable
            const {
              clearText,
              orderDiscountConflictMsg,
              unvalTaxSvcCharges,
              btnTxtForConfirm,
              discountAvailableTip,
              missingPaymentMethod,
              MDOrderStartTimeout
            } = this.systemLanguage
            const FBMode = performType === 2
            //禁用/编辑
            if (this.disableSendOrder || this.cartEditXi) {
              return false
            }
            // 购物车为空
            if (!this.allshoplNumber) {
              this.msgTips(clearText)
              return false
            }
            // 没有支付方式
            if (FBMode && !this.getAllPayMethod.length && this.allPriceCache != 0) {
              this.msgTips(missingPaymentMethod)
              return false
            }

            // 购物车数据存在税率但未校验税率(存在checkCode接口非正常失败)
            if ((this.includedTax || billTax) && this.hasCheckCodeFailed) {
              layer.alert(unvalTaxSvcCharges, {
                title: false,
                skin: "defaultLayer",
                closeBtn: 0,
                btn: [btnTxtForConfirm],
                yes: i => {
                  layer.close(i)
                  this.onRequestDiscount()
                }
              })
              return false
            }

            //购物车数据存在服务费但未校验服务费(存在checkCode接口非正常失败)
            if (displayInShoppingCartPage && this.hasCheckCodeFailed) {
              layer.alert(unvalTaxSvcCharges, {
                title: false,
                skin: "defaultLayer",
                closeBtn: 0,
                btn: [btnTxtForConfirm],
                yes: i => {
                  layer.close(i)
                  this.onRequestDiscount()
                }
              })
              return false
            }

            // 有可用折扣弹窗(存在checkCode接口非正常失败)
            const enableDiscount =
              this.openTable.promotionDiscount && this.choosePayMethod !== "payAtCashier"
            const discount = this.checkDiscountExclude()
            // 启用折扣功能+有可用折扣+不忽略校验
            const toPopup =
              enableDiscount && discount && !this.confirmDiscountCode && !this.ignoreAutoDiscount
            if (FBMode && toPopup) {
              let { directOrderBtn, useDiscountBtn } = this.systemLanguage
              layer.alert(discountAvailableTip, {
                title: false,
                skin: "defaultLayer discountAvailableLayer",
                closeBtn: 0,
                btn: [directOrderBtn, useDiscountBtn],
                btn1: index => {
                  if (discount.compulsory) return false
                  // 直接下单(忽略校验自动折扣是否存在)
                  this.ignoreAutoDiscount = true
                  this.onSubOrderBtn()
                  layer.close(index)
                },
                btn2: index => {
                  layer.close(index)
                  this.sendOrderForm.discountCode = discount.code
                  this.onRequestDiscount()
                },
                // 添加按钮样式
                success: layero => {
                  this.$nextTick(() => {
                    // 获取第二个按钮并添加禁用样式
                    const btn0 = $(".layui-layer.discountAvailableLayer .layui-layer-btn0")
                    // 根据条件判断是否禁用
                    if (discount.compulsory) {
                      btn0.addClass("layui-btn-disabled")
                      // 移除点击事件
                      btn0.unbind("click")
                    }
                  })
                }
              })
              return false
            }

            // 预点餐要选择table
            if (tableNumber === "PREORDER") {
              this.showPreOrderDia()
              return false
            }

            // 非预约模式下,若打样则不可提交
            if (!this.initTimePickerState && this.verifyStoreClosed()) {
              try {
                let txt = JSON.parse(timeoutPrompts)
                this.layerDia(txt[language])
              } catch (e) {
                console.error("return: 未配置店铺打烊提示语")
              }
              return false
            }

            //校验是否存在附加项与折扣冲突
            if (this.additionalDisConfStatus) {
              this.msgTips(orderDiscountConflictMsg, "#ef5350")
              this.$nextTick(() => {
                let scrollDom = this.showSendOrderView
                  ? ".additionalDisConfScrollDom2"
                  : ".additionalDisConfScrollDom1"
                document.querySelector(scrollDom).scrollIntoView(true)
              })
              return false
            }

            // 允许落单时间 MD
            if (this.showOrderCountdown && this.orderTimerStatus === 0) {
              this.msgTips(MDOrderStartTimeout)
              return false
            }

            //支付方法页面校验钱包支付是否登录
            if (this.choosePayMethod == "wallet") {
              const walletLogined = await this.walletLogined()
              if (!walletLogined || this.choosePayMethod != "wallet") return false //登录完不符合钱包支付会改变支付方式
            }

            // 校验表单数据
            if (FBMode && this.showSendOrderView) {
              const verifyRes = this.$refs.sendOrderForm.validate()
              if (!verifyRes) {
                // 表单验证失败,跳转到失败位置
                this.$nextTick(() => {
                  document.querySelector("#send-order-view .error--text").scrollIntoView(true)
                })
                return false
              }
            }
            // takeaway 落单弹窗 显示店铺logo
            if (this.showTakeAwaySubDia) {
              this.$refs.takeAwaySubDiaDom.showDialog(this.showSendOrderView)
              return false
            }
            // 正常模式落单前确认弹窗
            if (!this.isTakeAway && confirmationPopup) {
              const confirmStatus = await this.confirmOrderDia(this.choosePayMethod) //正常模式下单弹窗确认
              if (!confirmStatus) return false
            }
            return true
          },
          // 获取送单参数
          combineOrderParams() {
            const {
              tableNumber: tn,
              tableKey: tk = "",
              language = "en",
              companyName,
              postOrderPopup,
              initialTableNum,
              promotionDiscount,
              performType,
              staffCode,
              staffPassword,
              urlPrefix
            } = this.openTable
            const { tableNum, tableKey: preTableKey } = this.preOrderObj
            const storeNumber = this.isFoodCourtMode ? "*" : this.openTable.storeNumber
            const tableNumber = tn === "PREORDER" ? tableNum : tn
            const tableKey = tk === "PREORDER" ? preTableKey : tk
            const newOrderItem_foodListJson = sessionStorage.getItem("shopCartList")
            const {
              phone: suffixPhone,
              areaCode,
              name,
              verificationCode,
              email
            } = this.sendOrderForm

            // FB的特有参数
            const FBOrderParams = {}
            if (performType === 2 && this.showSendOrderView) {
              const customerPhone = suffixPhone != "" ? `+${areaCode.toString()}${suffixPhone}` : "" //后缀电话不为空才加入区号拼接,否则传空参数
              FBOrderParams.name = name
              FBOrderParams.customerPhone = customerPhone
              if (this.getAllPayMethod.length) {
                const payMentIdnex = this.payTypeObj.findIndex(
                  item => item.value == this.choosePayMethod
                )
                FBOrderParams.payVal = this.choosePayMethod
                FBOrderParams.payType = this.payTypeObj[payMentIdnex].belong
              }
              FBOrderParams.isPreOrder = tableNumber === "PREORDER" ? true : void 0
              if (this.choosePayMethod === "iPay88") FBOrderParams.payVal = "iPay88"
              if (this.choosePayMethod === "payAtCashier") layer.closeAll()
              // 折扣码
              if (promotionDiscount) FBOrderParams.promotionDiscountCode = this.confirmDiscountCode
              if (verificationCode) FBOrderParams.verificationCode = verificationCode
            }
            //FB下存在人数发送(用于统计下单人数)
            if (sessionStorage.getItem("FBPaxCount") && performType === 2) {
              FBOrderParams.pax = sessionStorage.getItem("FBPaxCount")
            }

            const data = {
              repeatFoodCode: "",
              checkMerchantRef: true, //默认校验订单号防止存在未处理订单,重复下单则覆盖字段值为false
              language,
              companyName,
              storeNumber,
              newOrderItem_foodListJson,
              performType,
              tableNumber,
              tableKey,
              urlPrefix,
              ...FBOrderParams
            }

            if (this.isTakeAway && performType == 2 && this.initTimePickerState) {
              let { hourStrAndMinute = "", pickupTime, startTimeSecond } = this.openTable.storeData
              let timeList = hourStrAndMinute.split("-")
              if (timeList.length === 2) {
                data.pickupTime = formatPickupTime(pickupTime, startTimeSecond)
              }
            }
            if (tableNumber === "TAKEAWAY") {
              data.additionalItemsForTakeawayAutoList = JSON.stringify(
                this.getSelectedAdditionalItemsData(false)
              )
              data.additionalItemsForTakeawayFixedList = JSON.stringify(
                this.getSelectedAdditionalItemsData(true)
              )
            }
            switch (initialTableNum) {
              case "lbsMode":
                data.lbs = true //pad模式下单增加lbs字段后端更新tableKey
                break
              case "StaffMode":
                data.mode = "StaffMode"
                data.staffCode = staffCode
                data.staffPassword = staffPassword
                break
              case "AssistMode":
                data.mode = initialTableNum
                break
              case "EnhAssistMode":
                data.mode = initialTableNum
                break
              default:
                break
            }

            //满足赠送的list,传id
            if ("promotionOfferList" in this.openTable && this.giveAwayFcodeList.length) {
              let str = this.useGiveAwayList.reduce((pre, cur) => {
                return pre + cur.id + ","
              }, "")
              const value = str.substring(0, idStr.lastIndexOf(","))
              if (this.isFoodCourtMode) {
                // 暂不处理foodCourt模式
              }
              data.promotionOfferListJson = this.isFoodCourtMode ? void 0 : value
            }

            // email
            if (this.choosePayMethod !== "iPay88") {
              data.email = email || ""
            } else {
              delete data.email
            }
            // 购物车版本号,根据SSE获取
            if (this.sseCartVersion) {
              data.shoppingCartVersion = this.sseCartVersion
            }
            if (this.SSEId) {
              data.sseId = this.SSEId
            }

            return data
          },
          sendOrder(params = {}) {
            const data = { ...this.combineOrderParams(), ...params }
            const loadingIndex = layer.load(2)
            return $.ajax({
              type: "POST",
              url: "../store/saveTableOrder",
              contentType: "application/json",
              data: JSON.stringify(data),
              dataType: "json",
              error: err => {
                let { errorMsg } = this.systemLanguage
                layer.close(loadingIndex)
                layer.msg(errorMsg)
                console.error("落单失败:", err)
              },
              dataFilter: (res, dataType) => res,
              success: res => this.handleSendOrderSuccess(res, data),
              complete: (xhr, status) => {
                layer.close(loadingIndex)
                try {
                  const { statusCode } = JSON.parse(xhr.responseText)
                  const { performType } = this.openTable
                  const hasError = status !== "success" || statusCode !== 200
                  // 若购物车同步启用时复原,可能导致多用户数据不一致
                  if (performType === 1 && hasError && !this.enableSSECartSync) {
                    sessionStorage.setItem("shopCartList", data.newOrderItem_foodListJson)
                  }
                } catch {}
              }
            })
          },
          // 落單接口成功回調
          handleSendOrderSuccess(result, data) {
            let { tableNumber, postOrderPopup, performType, initialTableNum } = this.openTable
            let { errorMsg, defSuccessMsg } = this.systemLanguage
            const { statusCode, tempOrderRefNo, sessionId, merchantRef, refNo, payVal } = result
            if (statusCode !== 200) {
              this.subOrderErrorTip(result, data)
              return false
            }
            // 删除优惠券本地缓存
            sessionStorage.removeItem("tempSendOrderForm")
            sessionStorage.removeItem("shopCartSourceList")

            this.disableSendOrder = true //下单成功禁止多次点击

            // 下单成功不跳转页面弹窗逻辑(condition:附加条件)
            const showTipsInPage = (condition = true) => {
              if (postOrderPopup && condition) {
                this.showPostOrderPopup(result, data)
              } else {
                this.showDefSuccessMsg(defSuccessMsg, result, data)
              }
              sessionStorage.removeItem("shopCartList")
            }
            if (this.enableSSECartSync) {
              this.sseCartVersion =
                (result.shoppingCart && result.shoppingCart.version.toString()) ||
                this.sseCartVersion
              sessionStorage.setItem("sseCartVersion", this.sseCartVersion)
            }

            if ("test" === tableNumber) {
              showTipsInPage()
              return false
            }

            //柜台支付
            if (tempOrderRefNo) {
              this.payAtCashierNum = tempOrderRefNo
              this.showPayAtCashierNum()
              return false
            }
            // EFTPOS支付
            if (sessionId) {
              this.setCookie(merchantRef)
              this.initEFTPay(sessionId)
              sessionStorage.setItem("sendOrderForm", JSON.stringify(this.sendOrderForm))
              return false
            }
            // ipay88支付
            if (refNo) {
              this.setCookie(refNo)
              this.iPay88Online(result)
              return false
            }
            // 中国银行支付
            if (payVal === "boc") {
              // 没有url按照MD下单成功逻辑
              if (!result.url) {
                showTipsInPage()
                return false
              }
              this.redirectPaySuccessPage(result)
              return false
            }

            // razer支付
            if (payVal === "razer") {
              this.razerPay(result)
              return false
            }
            // windcave/wattle支付
            if (data.payType === "windcave" || data.payVal === "wallet") {
              // 没有url按照MD下单成功逻辑
              if (!result.url) {
                showTipsInPage()
                return false
              }
              this.redirectPaySuccessPage(result, !!result.merchantRef)
              return false
            }

            // 除各种支付方式外(包括0元下单)下单成功提示窗口判断(自定义下单成功提示弹窗)
            let specialMode = ["AssistMode", "StaffMode", "EnhAssistMode"].includes(initialTableNum)
            showTipsInPage(!specialMode)
            return false
          },

          // layer弹窗提示
          layerDia(txt, fn) {
            let index = layer.msg(txt, {
              scrollbar: false,
              anim: 6,
              skin: "tipDia",
              closeBtn: 2,
              time: 0,
              shade: [0],
              shadeClose: true,
              end: function () {
                fn && fn()
              }
            })
          },
          // 选择提示
          selectPrompt(minQty, maxQty) {
            // 判断是否是数字
            minQty = typeof minQty === "number" ? minQty : -Infinity
            maxQty = typeof maxQty === "number" ? maxQty : Infinity
            if (minQty == -Infinity) {
              return this.systemLanguage.maxChooseQtyItem.replace("#qty", maxQty)
            } else if (maxQty == Infinity) {
              return this.systemLanguage.minChooseQtyItem.replace("#qty", minQty)
            } else if (minQty == maxQty) {
              return this.systemLanguage.chooseClearQtyItem.replace("#qty", minQty)
            } else if (minQty != maxQty) {
              let text = this.systemLanguage.chooseClearRangeQtyItem
              text = text.replace("#min", minQty)
              text = text.replace("#max", maxQty)
              return text
            }
          },
          // 回显已选细项（包括我的细项）
          showXiItem(outCode, Incode, type, hierarchy = "first") {
            let typeObj = {
              foodtypeInList: {
                localArry:
                  hierarchy == "first" ? this.localfoodTypeListArry : this.localAddSMXiFtyArry,
                InListCode: "fCode",
                newOrderArry: "newOrderItemFoodList"
              },
              mtypeInList: {
                localArry:
                  hierarchy == "first" ? this.localmtypeListArry : this.localAddSMXiMtyArry,
                InListCode: "code",
                newOrderArry: "newOrderItemMListList"
              },
              foodList: {
                localArry: hierarchy == "first" ? this.localfoodListArry : this.localXiItem,
                InListCode: "fCode",
                newOrderArry: "newOrderItemFoodList"
              },
              mListList: {
                localArry: hierarchy == "first" ? this.localmlistListArry : this.localXiItem,
                InListCode: "code",
                newOrderArry: "newOrderItemMListList"
              }
            }
            let targetObj = typeObj[type]
            // 找到对应外层index(找到已选数组new**arry里面的父数据;foodList自身标识为fCode)
            let index = targetObj["localArry"].findIndex(inList => {
              let selfCode = type == "foodList" ? inList.fCode : inList.code
              return selfCode == outCode
            })
            // let index = targetObj["localArry"].findIndex(inList => inList.code == outCode)
            let targetItem = targetObj["localArry"][index]
            // 没有targetItem代表弹窗第二层固定细项没有赋值父层数据this.localXiItem,只有选中了才会添加newOrderItemFoodList和newOrderItemMListList数组
            if (!targetItem) {
              // Vue.set(this.myXiClickXiOutItem, "newOrderItemFoodList", [...(this.onDisableItem(this.myXiClickXiOutItem.foodList) || [])])
              // Vue.set(this.myXiClickXiOutItem, "newOrderItemMListList", [...(this.onDisableItem(this.myXiClickXiOutItem.mListList) || [])])
              this.myXiClickXiOutItem.newOrderItemFoodList = [
                ...(this.onDisableItem(this.myXiClickXiOutItem.foodList) || [])
              ]
              this.myXiClickXiOutItem.newOrderItemMListList = [
                ...(this.onDisableItem(this.myXiClickXiOutItem.mListList) || [])
              ]
              targetItem = this.myXiClickXiOutItem
            }
            let fListItem = targetItem[targetObj["newOrderArry"]] //fty对象下所有foodlist
            let arry = []
            fListItem.forEach(item => {
              if (item[targetObj["InListCode"]] == Incode) {
                arry.push(item)
              }
            })
            if (hierarchy == "first") {
              this.localXiItem = arry
              this.clickOnSource = "fromOption"
              console.log(this.localXiItem, "this.localXiItem")
            } else {
              this.secLocalXiItem = arry
              this.addDataState = "fromOption"
              // console.log(this.secLocalXiItem, 'this.secLocalXiItem')
            }
          },

          // 单个细项选项按钮(弹出我的细项弹窗)
          showMyxiDia(editItem, type) {
            this.editXiItem = editItem //点击的细项
            this.localAddSMXiFtyArry = JSON.parse(
              JSON.stringify(editItem.newOrderItemFoodTypeList || [])
            ) //已经选中的数据(深拷贝)
            this.localAddSMXiMtyArry = JSON.parse(
              JSON.stringify(editItem.newOrderItemMTypeList || [])
            ) ///已经选中的数据(深拷贝)
            console.log(editItem, this.clickXiItem, "点击选项editItem,this.clickXiItem")
            console.log(this.localXiItem, "点击选项localXiItem")
            let clickXiItem = this.clickXiItem

            let selectFtyArry = editItem.newOrderItemFoodTypeList || [] // 追加套餐选中的数据
            let selectMtyArry = editItem.newOrderItemMTypeList || [] // 追加套餐选中的数据
            let selectFtyCode = []
            let selectMtyCode = []
            selectFtyArry.forEach(item => {
              item.newOrderItemFoodList.forEach(u => {
                selectFtyCode.push(u.fCode)
              })
            })
            selectMtyArry.forEach(item => {
              item.newOrderItemMListList.forEach(u => {
                selectMtyCode.push(u.code)
              })
            })
            // 回显选中项
            let clickXiItemAllArry = clickXiItem.allTypeArry || []
            clickXiItemAllArry.forEach(item => {
              if (item.typeName == "ftyItem") {
                item.foodList.forEach(inItem => {
                  if (selectFtyCode.includes(inItem.fCode)) {
                    Vue.set(inItem, "selected", true)
                    inItem.qty1 = this.cartRepeatNum(selectFtyCode, inItem.fCode) //重置数量(重构抛弃)
                  } else {
                    Vue.set(inItem, "selected", false)
                  }
                })
              }
              if (item.typeName == "mtyItem") {
                item.mListList.forEach(inItem => {
                  if (selectMtyCode.includes(inItem.code)) {
                    Vue.set(inItem, "selected", true)
                    inItem.qty1 = this.cartRepeatNum(selectMtyCode, inItem.code)
                  } else {
                    Vue.set(inItem, "selected", false)
                  }
                })
              }
            })

            this.$nextTick(() => {
              document.getElementById("myxiDialog").scrollTop = 0
            })
            let that = this
            layui.use("layer", function () {
              var layer = layui.layer
              layer.ready(function () {
                layer.open({
                  skin: "foodDialogLayer",
                  type: 1,
                  shade: [0.1, "#fff"],

                  title: false, //不显示标题
                  content: $(".foodDialog"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
                  success: function (layero, index) {
                    that.foodDialogIndex = index
                    that.addShowInFcodeState = true //选项卡打开的弹窗ShowInCode的数量预算不增加上一级
                  },
                  cancel: function (index, layero) {
                    that.localAllCode = that.oldLocalAllCode
                    console.log(that.oldLocalAllCode, "关闭2")
                  },
                  end: function () {
                    that.localAddSMXiFtyArry = []
                    that.localAddSMXiMtyArry = []
                    // that.localXiItem = [] //重置数据避免大food下固定细项点击后污染可选细项里的固定细项
                    that.addShowInFcodeState = false
                    that.setLocalAllCodeObj("openFoodInfo")
                    that.initFoldType()
                  }
                })
              })
            })
          },
          // 单个细项选项按钮(弹出我的细项弹窗)
          secShowMyxiDia(editItem, type) {
            this.twoDiaAllCode = JSON.parse(JSON.stringify(this.localAllCode))
            this.myXiEditXiItem = editItem //点击的细项
            this.localSecAddSMXiFtyArry = JSON.parse(
              JSON.stringify(editItem.newOrderItemFoodTypeList || [])
            ) //已经选中的数据(深拷贝)
            this.localSecAddSMXiMtyArry = JSON.parse(
              JSON.stringify(editItem.newOrderItemMTypeList || [])
            ) ///已经选中的数据(深拷贝)
            let clickXiItem = this.myXiClickXiItem
            let selectFtyArry = editItem.newOrderItemFoodTypeList || [] // 追加套餐选中的数据
            let selectMtyArry = editItem.newOrderItemMTypeList || [] // 追加套餐选中的数据
            let selectFtyCode = []
            let selectMtyCode = []
            selectFtyArry.forEach(item => {
              item.newOrderItemFoodList.forEach(u => {
                selectFtyCode.push(u.fCode)
              })
            })
            selectMtyArry.forEach(item => {
              item.newOrderItemMListList.forEach(u => {
                selectMtyCode.push(u.code)
              })
            })
            // 回显选中项
            let clickXiItemAllArry = clickXiItem.allTypeArry || []
            clickXiItemAllArry.forEach(item => {
              if (item.typeName == "ftyItem") {
                item.foodList.forEach(inItem => {
                  if (selectFtyCode.includes(inItem.fCode)) {
                    Vue.set(inItem, "selected", true)
                    inItem.qty1 = this.cartRepeatNum(selectFtyCode, inItem.fCode) //重置数量(重构抛弃)
                  } else {
                    Vue.set(inItem, "selected", false)
                  }
                })
              }
              if (item.typeName == "mtyItem") {
                item.mListList.forEach(inItem => {
                  if (selectMtyCode.includes(inItem.code)) {
                    Vue.set(inItem, "selected", true)
                    inItem.qty1 = this.cartRepeatNum(selectMtyCode, inItem.code)
                  } else {
                    Vue.set(inItem, "selected", false)
                  }
                })
              }
            })

            this.$nextTick(() => {
              document.getElementById("secXiDialog").scrollTop = 0
            })
            let that = this
            layui.use("layer", function () {
              var layer = layui.layer
              layer.ready(function () {
                layer.open({
                  skin: "secondDialogLayer",
                  type: 1,
                  shade: [0.1, "#fff"],
                  title: false, //不显示标题
                  scrollbar: false,
                  content: $(".secondDialog"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
                  success: function (layero, index) {
                    that.secondDialogIndex = index
                    that.secAddShowInFcodeState = true //选项卡打开的弹窗ShowInCode的数量预算不增加上一级
                  },
                  cancel: function (index, layero) {
                    that.localAllCode = that.twoDiaAllCode
                    console.log(that.localAllCode, "关闭")
                  },
                  end: function () {
                    that.localSecAddSMXiFtyArry = []
                    that.localSecAddSMXiMtyArry = []
                    that.secAddShowInFcodeState = false
                    that.initFoldType(1)
                  }
                })
              })
            })
          },
          // 封装显示细项tile
          inListTitle(item) {
            let { language } = this.openTable
            // foodlist:desc1,desc2,multi1
            // mListList:name,name2,multi1
            let xiTitle = ""

            let isFoodList = item.hasOwnProperty("desc1") || item.hasOwnProperty("desc2")
            const getTitle = (foodListTitle, mlistTitle) => {
              return isFoodList ? foodListTitle : mlistTitle
            }

            if (language === "en") {
              xiTitle = getTitle(item.nameA || item.desc1, item.nameA || item.name)
            } else if (language === "zh") {
              xiTitle = getTitle(item.nameB || item.desc2, item.nameB || item.name2)
            } else {
              xiTitle = item.multi1 || getTitle(item.nameA || item.desc1, item.nameA || item.name)
            }

            return xiTitle
          },
          // 封装显示细项外层Type的名称
          outListTitle(item) {
            let { language } = this.openTable
            // fty:name,name2,multi1
            // mty:desc,desc2,multi1
            let isFty = item.hasOwnProperty("name") || item.hasOwnProperty("name2")
            let xiTitle = ""

            const getTitle = (ftyTitle, mtyTitle) => {
              return isFty ? ftyTitle : mtyTitle
            }

            if (language === "en") {
              xiTitle = getTitle(item.fType_nameA || item.name, item.nameA || item.desc)
            } else if (language === "zh") {
              xiTitle = getTitle(item.fType_nameB || item.name2, item.nameB || item.desc2)
            } else {
              xiTitle =
                item.multi1 || getTitle(item.fType_nameA || item.name, item.nameA || item.desc)
            }
            return xiTitle
          },

          //验证select为true的选择,是否超出及超出后逻辑
          exceedMaximumBySelected(data) {
            let {
              localList,
              clickXiOutCode,
              vItem,
              item,
              clickXiType,
              qtySum,
              index,
              t,
              next,
              codeTxt,
              vItemList
            } = data
            let { maxQty: max } = vItem
            //超出,则从头循环删除非本身的数据,知道满足新增条件
            if (!this.verifyAddXiItem(localList, clickXiOutCode, vItem, clickXiType, next)) {
              let removeNum = qtySum + next - max
              let j = 0 //len的索引
              let len = localList[index][t].length
              let all = localList[index][t].every(e => e[codeTxt] === item[codeTxt])
              if (all) {
                console.log("全部相同,点击重复选项")
                return true
              } else {
                let existedVal = localList[index][t]
                  .filter(e => e[codeTxt] === item[codeTxt])
                  .reduce((a, b) => a + (b.takeUpQty || 1), 0)
                if (max - existedVal < next) {
                  console.log("移除非本身的其他都无法满足新增条件")
                  return true
                }
              }

              if (localList[index][t].find(e => e[codeTxt] === item[codeTxt]) && max < 2 * next) {
                console.log("无法容纳2个相同的item")
                return true
              }
              for (let i = 0; i < len; i++) {
                if (localList[index][t][j][codeTxt] === item[codeTxt]) {
                  j += 1
                } else {
                  let reCode = localList[index][t][j][codeTxt] //需要移除的code
                  let list = localList[index][t].filter(e => e[codeTxt] === reCode) //剩余code数量
                  if (list.length === 1) {
                    vItemList.forEach(e => {
                      if (e[codeTxt] === reCode) {
                        this.$set(e, "selected", false)
                        e.qty1 = e.resetQty //重置数量
                      }
                    })
                  } else {
                    vItemList.find(e => e[codeTxt] === reCode).qty1 = list.length - 1
                  }
                  removeNum -= localList[index][t][j].takeUpQty || 1
                  localList[index][t].splice(j, 1)
                  if (removeNum <= 0) break
                }
              }
            }
          },
          // 大food下增加数量(数量增加并不是同个对象qty1+1,而是增多一个item)
          addXiItem(item, type, vItem, newOFtyArry, newOMtyArry) {
            // 判断是否具备新增条件
            let clickXiItem = item
            let clickXiType = type
            let { code: clickXiOutCode } = vItem
            let codeTxt = type === "foodtypeInList" ? "fCode" : "code"
            let t = type === "foodtypeInList" ? "newOrderItemFoodList" : "newOrderItemMListList"
            let vItemList = type === "foodtypeInList" ? vItem.foodList : vItem.mListList
            let localList =
              type === "foodtypeInList" ? this.localfoodTypeListArry : this.localmtypeListArry
            let index = localList.findIndex(item => item.code === clickXiOutCode)
            let qtySum = localList[index][t].reduce((total, item) => {
              return total + (item.takeUpQty || 1)
            }, 0)
            let obj
            if (clickXiType == "foodtypeInList") {
              obj = {
                desc1: clickXiItem.desc1,
                desc2: clickXiItem.desc2,
                nameA: clickXiItem.nameA,
                nameB: clickXiItem.nameB,
                multi1: clickXiItem.multi1,
                fCode: clickXiItem.fCode,
                k1: clickXiItem.k1,
                kpName: clickXiItem.kpName,
                qty1: clickXiItem.resetQty,
                single: clickXiItem.single,
                mapCode: clickXiItem.mapCode,
                seq: clickXiItem.seq,
                finalSort: clickXiItem.finalSort,
                t_able: clickXiItem.t_able,
                sc_able: clickXiItem.sc_able,
                discount: clickXiItem.discount,
                ftCode: clickXiItem.ftCode,
                use_dow: clickXiItem.use_dow,
                use_date: clickXiItem.use_date,
                use_time: clickXiItem.use_time,
                use_dow2: clickXiItem.use_dow2,
                use_date2: clickXiItem.use_date2,
                use_time2: clickXiItem.use_time2,
                minQty: clickXiItem.minQty,
                maxQty: clickXiItem.maxQty,
                minQty2: clickXiItem.minQty2,
                maxQty2: clickXiItem.maxQty2,
                show_In_FCodes: clickXiItem.show_In_FCodes,
                not_Show_With_FCodes: clickXiItem.not_Show_With_FCodes,
                takeUpQty: clickXiItem.takeUpQty,
                upa1: clickXiItem.upa1,
                listSelect: clickXiItem.listSelect || false, // 附带的我的细项
                newOrderItemFoodList: [...this.onDisableItem(clickXiItem.foodList)],
                newOrderItemMListList: [...this.onDisableItem(clickXiItem.mListList)],
                newOrderItemFoodTypeList: [...(newOFtyArry || [])],
                newOrderItemMTypeList: [...(newOMtyArry || [])],
                packingBoxMList: this.useTakeAwayPackag ? clickXiItem.packingBoxMList : null,
                packingBoxMListCode: this.useTakeAwayPackag
                  ? clickXiItem.packingBoxMListCode
                  : null,
                ableDiscount: clickXiItem.ableDiscount,
                tax1: clickXiItem.tax1,
                tax2: clickXiItem.tax2,
                tax3: clickXiItem.tax3
              }
            } else {
              obj = {
                price: clickXiItem.price, //传入动态价格字段
                code: clickXiItem.code,
                k1: clickXiItem.k1,
                kpName: clickXiItem.kpName,
                name: clickXiItem.name,
                name2: clickXiItem.name2,
                name3: clickXiItem.name3,
                nameA: clickXiItem.nameA,
                nameB: clickXiItem.nameB,
                multi1: clickXiItem.multi1,
                qty1: clickXiItem.resetQty,
                single: clickXiItem.single,
                mapCode: clickXiItem.mapCode,
                seq: clickXiItem.seq,
                finalSort: clickXiItem.finalSort,
                fGroup: clickXiItem.fGroup,
                level: clickXiItem.level,
                points: clickXiItem.points,
                discType: clickXiItem.discType,
                listSelect: clickXiItem.listSelect || false, // 附带的我的细项
                ftCode: clickXiItem.ftCode,
                use_dow: clickXiItem.use_dow,
                use_date: clickXiItem.use_date,
                use_time: clickXiItem.use_time,
                takeUpQty: clickXiItem.takeUpQty,
                packingBoxMList: this.useTakeAwayPackag ? clickXiItem.packingBoxMList : null,
                packingBoxMListCode: this.useTakeAwayPackag
                  ? clickXiItem.packingBoxMListCode
                  : null,
                newOrderItemMListList: [...this.onDisableItem(clickXiItem.mListList)],
                newOrderItemMTypeList: [...(newOMtyArry || [])],
                ableDiscount: clickXiItem.ableDiscount
              }
            }
            let targetData = {
              localList,
              clickXiOutCode,
              vItem,
              item,
              clickXiType,
              qtySum,
              index,
              t,
              next: item.takeUpQty || 1,
              codeTxt,
              vItemList
            }
            if (this.exceedMaximumBySelected(targetData)) {
              return
            }
            localList[index][t].push(obj)
            this.showXiItem(clickXiOutCode, clickXiItem[codeTxt], clickXiType)
            clickXiItem.qty1++
          },

          // 删除细项数量
          delXiItem(item, type, outCode) {
            console.log(item, "删除细项")
            // 反向删除,过滤掉不需要的item再添加进去
            let clickXiOutCode = outCode
            let clickXiItem = item
            let localXiItem = this.localXiItem //本地细项数组(包含有我的细项)
            this.localXiItem = [] //显示细项框localXiItem数组置空(一旦点击删除图标则全部删除)
            // 删除localTypeListArry
            let delItemArry = []
            if (type == "foodtypeInList") {
              let localftyList = this.localfoodTypeListArry
              localftyList.forEach(vItem => {
                if (vItem.code == clickXiOutCode) {
                  // 获取删除的item
                  delItemArry = vItem.newOrderItemFoodList.filter((e, i) => {
                    return e.fCode == clickXiItem.fCode
                  })
                  let foodListArry = vItem.newOrderItemFoodList.filter((e, i) => {
                    return e.fCode != clickXiItem.fCode
                  })
                  vItem.newOrderItemFoodList = [...foodListArry]
                }
              })
            } else if (type == "mtypeInList") {
              let localmtyList = this.localmtypeListArry
              localmtyList.forEach(vItem => {
                if (vItem.code == clickXiOutCode) {
                  let mListArry = vItem.newOrderItemMListList.filter((e, i) => {
                    return e.code != clickXiItem.code
                  })
                  vItem.newOrderItemMListList = [...mListArry]
                }
              })
            }
            this.filterEmptyData() // 过滤空数据fty,mty
            // console.log(this.localfoodTypeListArry, this.localmtypeListArry, "本地ftymty数据")
            // 取消选中
            clickXiItem.selected = false
            clickXiItem.qty1 = clickXiItem.resetQty
            // 计算删除后的本地总数量
            this.getDelLocalNum(item, delItemArry)
          },
          // 验证是否符合新增细项
          verifyAddXiItem(localTypeList, clickXiOutCode, vItem, type, next) {
            let Index = (localTypeList || []).findIndex(inList => inList.code == clickXiOutCode)
            if (Index != -1) {
              if (type == "foodtypeInList") {
                let outItemTotal = localTypeList[Index].newOrderItemFoodList.reduce(
                  (total, item) => {
                    return total + (item.takeUpQty || 1)
                  },
                  0
                )
                return outItemTotal + +next <= (vItem.maxQty || Infinity)
              } else {
                let outItemTotal = localTypeList[Index].newOrderItemMListList.reduce(
                  (total, item) => {
                    return total + (item.takeUpQty || 1)
                  },
                  0
                )
                return outItemTotal + 1 <= (vItem.maxQty || Infinity)
              }
            } else {
              return true // fty/mty未选
            }
          },
          // 固定细项铅笔图标
          fixXiShowDia(item, type) {
            if (item.isExpired) {
              this.showOutTimeTips()
              return
            }
            // 初始clickOnSource,弹窗确认逻辑
            this.clickOnSource = ""
            //
            this.clickXiItem = item
            this.clickXiType = type
            let targetArray, localCode, Incode
            if (type == "foodList") {
              targetArray = this.localfoodListArry
              localCode = "fCode"
              Incode = "fCode"
            } else {
              targetArray = this.localmlistListArry
              localCode = "code"
              Incode = "code"
            }
            // 找到对应food
            let index = targetArray.findIndex(inList => inList[localCode] == item[Incode])
            let targetItem = targetArray[index]
            this.localXiItem = [targetItem] // 赋值第二层弹窗固定细项父数据
            this.showMyxiDia(targetItem, type)
          },
          // 追加套餐下增加细项数量
          addSMXiAddXiItem(item, type, vItem, newOFtyArry, newOMtyArry) {
            let clickXiType = type
            let { code: clickXiOutCode } = vItem
            let codeTxt = type === "foodtypeInList" ? "fCode" : "code"
            let t = type === "foodtypeInList" ? "newOrderItemFoodList" : "newOrderItemMListList"
            let vItemList = type === "foodtypeInList" ? vItem.foodList : vItem.mListList
            let localList =
              type === "foodtypeInList" ? this.localAddSMXiFtyArry : this.localAddSMXiMtyArry
            let index = localList.findIndex(item => item.code === clickXiOutCode)
            let qtySum = localList[index][t].reduce((total, item) => {
              return total + (item.takeUpQty || 1)
            }, 0)

            let obj
            if (type == "foodtypeInList") {
              obj = {
                upa1: item.upa1,
                desc1: item.desc1,
                desc2: item.desc2,
                nameA: item.nameA,
                nameB: item.nameB,
                multi1: item.multi1,
                fCode: item.fCode,
                k1: item.k1,
                kpName: item.kpName,
                single: item.single,
                mapCode: item.mapCode,
                seq: item.seq,
                finalSort: item.finalSort,
                t_able: item.t_able,
                sc_able: item.sc_able,
                discount: item.discount,
                qty1: item.resetQty,
                listSelect: item.listSelect || false, // 附带的我的细项
                newOrderItemFoodList: [...this.onDisableItem(item.foodList)],
                newOrderItemMListList: [...this.onDisableItem(item.mListList || [])],
                newOrderItemFoodTypeList: [...(newOFtyArry || [])],
                newOrderItemMTypeList: [...(newOMtyArry || [])],
                ftCode: item.ftCode,
                use_dow: item.use_dow,
                use_date: item.use_date,
                use_time: item.use_time,
                use_dow2: item.use_dow2,
                use_date2: item.use_date2,
                use_time2: item.use_time2,
                minQty: item.minQty,
                maxQty: item.maxQty,
                minQty2: item.minQty2,
                maxQty2: item.maxQty2,
                show_In_FCodes: item.show_In_FCodes,
                not_Show_With_FCodes: item.not_Show_With_FCodes,
                takeUpQty: item.takeUpQty,
                packingBoxMList: this.useTakeAwayPackag ? item.packingBoxMList : null,
                packingBoxMListCode: this.useTakeAwayPackag ? item.packingBoxMListCode : null,
                ableDiscount: item.ableDiscount,
                tax1: item.tax1,
                tax2: item.tax2,
                tax3: item.tax3
              }
            } else {
              obj = {
                price: item.price,
                code: item.code,
                k1: item.k1,
                kpName: item.kpName,
                name: item.name,
                name2: item.name2,
                name3: item.name3,
                nameA: item.nameA,
                nameB: item.nameB,
                multi1: item.multi1,
                single: item.single,
                mapCode: item.mapCode,
                seq: item.seq,
                finalSort: item.finalSort,
                fGroup: item.fGroup,
                level: item.level,
                points: item.points,
                discType: item.discType,
                qty1: item.resetQty,
                ftCode: item.ftCode,
                use_dow: item.use_dow,
                use_date: item.use_date,
                use_time: item.use_time,
                takeUpQty: item.takeUpQty,
                newOrderItemMListList: [...this.onDisableItem(item.mListList || [])],
                newOrderItemMTypeList: [...(newOMtyArry || [])]
              }
            }
            let targetData = {
              localList,
              clickXiOutCode,
              vItem,
              item,
              clickXiType,
              qtySum,
              index,
              t,
              next: item.takeUpQty || 1,
              codeTxt,
              vItemList
            }
            if (this.exceedMaximumBySelected(targetData)) {
              return
            }
            localList[index][t].push(obj)
            this.showXiItem(clickXiOutCode, item.code, type, "second")
            item.qty1++
            console.log(
              this.clickXiItem,
              this.localAddSMXiFtyArry,
              this.localAddSMXiMtyArry,
              "点击增加细项数量后"
            )
          },
          // 删除细项数量
          addSMXiDelXiItem(item, type, outCode) {
            let clickXiOutCode = outCode
            let clickXiItem = item
            let delItemArry = []
            if (type == "foodtypeInList") {
              let localftyList = this.localAddSMXiFtyArry
              localftyList.forEach(vItem => {
                if (vItem.code == clickXiOutCode) {
                  // 获取删除的item
                  delItemArry = vItem.newOrderItemFoodList.filter((e, i) => {
                    return e.fCode == clickXiItem.fCode
                  })
                  let foodListArry = vItem.newOrderItemFoodList.filter((e, i) => {
                    return e.fCode != clickXiItem.fCode
                  })

                  vItem.newOrderItemFoodList = [...foodListArry]
                }
              })
              // 过滤空数据
              this.localAddSMXiFtyArry = this.localAddSMXiFtyArry.filter(item => {
                return item.newOrderItemFoodList.length != 0
              })

              // 计算删除后的本地总数量
              this.getDelLocalNum(item, delItemArry)
            } else if (type == "mtypeInList") {
              let localmtyList = this.localAddSMXiMtyArry
              localmtyList.forEach(vItem => {
                if (vItem.code == clickXiOutCode) {
                  let mListArry = vItem.newOrderItemMListList.filter((e, i) => {
                    return e.code != clickXiItem.code
                  })
                  vItem.newOrderItemMListList = [...mListArry]
                }
              })
              this.localAddSMXiMtyArry = this.localAddSMXiMtyArry.filter(item => {
                return item.newOrderItemMListList.length != 0
              })
            }
            clickXiItem.selected = false // 取消选中
            clickXiItem.qty1 = clickXiItem.resetQty
            // 获取删除的所有fcode和fty下的子对象数量
            console.log(this.localfoodTypeListArry, "删除第一层增追加套餐细项")
          },
          // 验证追加套餐minQty2和maxQty2
          addSMXiVerifyQty2(numType, type, nextQty1, vItem) {
            let editItem = this.editXiItem
            let food = editItem.newOrderItemFoodList || this.onDisableItem(editItem.foodList) || []
            let mlist =
              editItem.newOrderItemMListList || this.onDisableItem(editItem.mListList) || []
            let baseLength = food.length + mlist.length
            let FtypeLength = 0,
              MtypeLength = 0
            let minNum = editItem.minQty2 || -Infinity
            let maxNum = editItem.maxQty2 || Infinity
            // let minNum = 0
            // let maxNum = 12
            this.localAddSMXiFtyArry.forEach(item => {
              item.newOrderItemFoodList.forEach(inItem => {
                FtypeLength += inItem.qty1
              })
            })
            this.localAddSMXiMtyArry.forEach(item => {
              item.newOrderItemMListList.forEach(inItem => {
                MtypeLength += inItem.qty1
              })
            })
            let allLength = baseLength + FtypeLength + MtypeLength
            let res
            if (numType == "min") {
              res = allLength < minNum
            } else {
              // 点击前判断下个数字总和是否大于限制
              res = allLength + nextQty1 > maxNum
              // console.log(allLength, nextQty1, '最大值验证');
              // console.log(this.localAddSMXiFtyArry, this.localAddSMXiMtyArry, '最大值验证');
            }
            console.log(res, baseLength, FtypeLength, MtypeLength, "Qty2总数量")
            if (res) this.showNotSatisfiedQtyHint([editItem], false)
            return res
          },

          secVerifyQty2(numType, type, nextQty1, vItem) {
            let editItem = this.myXiEditXiItem
            let food = editItem.newOrderItemFoodList || this.onDisableItem(editItem.foodList) || []
            let mlist =
              editItem.newOrderItemMListList || this.onDisableItem(editItem.mListList) || []
            let baseLength = food.length + mlist.length
            let FtypeLength = 0,
              MtypeLength = 0
            let minNum = editItem.minQty2 || -Infinity
            let maxNum = editItem.maxQty2 || Infinity
            // let minNum = 0
            // let maxNum = 12
            this.localSecAddSMXiFtyArry.forEach(item => {
              item.newOrderItemFoodList.forEach(inItem => {
                FtypeLength += inItem.qty1
              })
            })
            this.localSecAddSMXiMtyArry.forEach(item => {
              item.newOrderItemMListList.forEach(inItem => {
                MtypeLength += inItem.qty1
              })
            })
            let allLength = baseLength + FtypeLength + MtypeLength
            let res
            if (numType == "min") {
              res = allLength < minNum
            } else {
              // 点击前判断下个数字总和是否大于限制
              res = allLength + 1 > maxNum
              console.log(allLength, nextQty1, maxNum, "最大值验证")
              // console.log(this.localAddSMXiFtyArry, this.localAddSMXiMtyArry, '最大值验证');
            }
            if (res) this.showNotSatisfiedQtyHint([editItem], false)

            return res
          },
          //购物车编辑确认按钮
          cartEditBtn() {
            this.cartEditXi = !this.cartEditXi
            this.joinType = ""
          },
          // 修改购物车细项
          editCarFood(item) {
            let copyItem = JSON.parse(JSON.stringify(item))
            this.joinType = "edit"
            this.editCartItemUnique = item.unique
            this.clickCartEditIndex = this.shopCartList.findIndex(el =>
              this.checkSameFt(el, item, "editCart")
            )

            let sourceData = this.isFoodCourtMode
              ? (this.shopCartSourceList || []).find(inList => this.checkSameFt(inList, item))
              : this.findMainFoodByCode(this.allDataList, item.fCode)
            console.log(sourceData, "sourceData")
            // foodList/mListList 有保留原格式
            const foodList = this.onDisableItem(copyItem.newOrderItemFoodList)
            const mListList = this.onDisableItem(copyItem.newOrderItemMListList)
            if (!sourceData) return
            let targetItem = {
              ...sourceData,
              qty1: item.qty1,
              foodList: combineOrderListToSourceList(
                foodList,
                JSON.parse(JSON.stringify(sourceData.foodList))
              ),
              mListList: combineOrderListToSourceList(
                mListList,
                JSON.parse(JSON.stringify(sourceData.mListList))
              ),
              newOrderItemFoodList: [...foodList],
              newOrderItemMListList: [...mListList],
              newOrderItemFoodTypeList: [...copyItem.newOrderItemFoodTypeList],
              newOrderItemMTypeList: [...copyItem.newOrderItemMTypeList],
              unique: item.unique,
              hashRecord: item.hashRecord
            }
            // 修改前的全部Code
            this.yetCode = this.sortCartCode(targetItem)
            this.effectiveNum = this.allshoplNumber - targetItem.qty1 // 减去修改Item的数量为有效值
            this.backupEditItem = copyItem
            this.onfoodInfo(targetItem)
            // console.log(item, "购物车数据", this.foodInfoItem, "过滤出的数据")
          },
          // 购物车回显详情页
          cartSelectShow() {
            // console.log(this.foodInfoItem, '购物车回显food');
            let {
              newOrderItemFoodList = [],
              newOrderItemMListList = [],
              newOrderItemFoodTypeList,
              newOrderItemMTypeList
            } = this.foodInfoItem
            this.localfoodTypeListArry = [...newOrderItemFoodTypeList]
            this.localmtypeListArry = [...newOrderItemMTypeList]
            if (newOrderItemFoodList.length != 0) {
              this.localfoodListArry = newOrderItemFoodList
            }
            if (newOrderItemMListList.length != 0) {
              console.log(
                JSON.parse(JSON.stringify(newOrderItemMListList)),
                "newOrderItemMListList"
              )
              this.localmlistListArry = newOrderItemMListList
            }
            let xiFoodCode = []
            let xiMlistCode = []
            if (newOrderItemFoodTypeList && newOrderItemFoodTypeList.length !== 0) {
              newOrderItemFoodTypeList.forEach(item => {
                item.newOrderItemFoodList.forEach(u => {
                  xiFoodCode.push(u.fCode)
                })
              })
            }
            if (newOrderItemMTypeList && newOrderItemMTypeList.length != 0) {
              newOrderItemMTypeList.forEach(item => {
                item.newOrderItemMListList.forEach(u => {
                  xiMlistCode.push(u.code)
                })
              })
            }
            // console.log(xiFoodCode, "xiFoodCode");
            let foodTypeList = [],
              mTypeList = []
            this.foodInfoItem.allTypeArry &&
              this.foodInfoItem.allTypeArry.forEach(item => {
                if (item.typeName == "ftyItem") {
                  foodTypeList.push(item)
                } else {
                  mTypeList.push(item)
                }
              })
            if (foodTypeList.length != 0) {
              foodTypeList.forEach(item => {
                item.foodList.forEach(inItem => {
                  if (xiFoodCode.includes(inItem.fCode)) {
                    inItem.qty1 = this.cartRepeatNum(xiFoodCode, inItem.fCode)
                    inItem.selected = true
                  }
                })
              })
            }
            if (mTypeList.length != 0) {
              mTypeList.forEach(item => {
                item.mListList.forEach(inItem => {
                  if (xiMlistCode.includes(inItem.code)) {
                    inItem.qty1 = this.cartRepeatNum(xiMlistCode, inItem.code) //多数量重置
                    inItem.selected = true
                  }
                })
              })
            }
          },
          // 多数量重置qty1
          cartRepeatNum(xiTypeCode, targetCode) {
            let repeatNum = 0
            xiTypeCode.forEach(item => {
              if (item == targetCode) {
                repeatNum++
              }
            })
            return repeatNum
          },
          // 购物车Food细项修改/删除/替换
          cartFoodReplace(
            localmtypeListArry,
            localfoodTypeListArry,
            localfoodListArry,
            localmlistListArry
          ) {
            let isEditSome = this.sameArry(this.yetCode, this.sortlocalCode()) //是否和修改前一样
            let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
            //校验购物车是否还存在当前编辑item
            const editIndex = this.getEditItemCartIndex()
            const foodItem = {
              ftCode: this.foodInfoItem.ftCode,
              fCode: this.foodInfoItem.fCode,
              qty1: this.foodInfoItem.qty1,
              newOrderItemFoodTypeList: localfoodTypeListArry,
              newOrderItemMTypeList: localmtypeListArry,
              newOrderItemMListList: localmlistListArry,
              newOrderItemFoodList: localfoodListArry
            }

            if (!this.verificationEditItem(editIndex, foodItem)) {
              this.joinType = ""
              this.showFoodWarp = false
              return false
            }
            if (!isEditSome) {
              let cartFcodeArry = []
              for (let i = 0; i < shopCartList.length; i++) {
                let sameFood = shopCartList[i]
                if (this.checkSameFt(sameFood, shopCartList[editIndex])) {
                  cartFcodeArry.push({
                    cartIndex: i,
                    codeArry: this.sortCartCode(sameFood)
                  })
                }
              }
              if (cartFcodeArry.length != 0) {
                for (let i = 0; i < cartFcodeArry.length; i++) {
                  let localFcodeArry = this.sortlocalCode()
                  let isEqual = this.sameArry(localFcodeArry, cartFcodeArry[i].codeArry)
                  if (isEqual) {
                    if (cartFcodeArry.length != 1) {
                      let index = cartFcodeArry[i].cartIndex
                      let addQty = shopCartList[editIndex].qty1
                      shopCartList[index].qty1 += addQty
                      // 更新hashRecord:细项一致,仅更新Qty
                      this.updateHashRecord(shopCartList[index])
                      shopCartList.splice(editIndex, 1)
                      console.log("修改后和购物车全部相同合并")
                      break
                    }
                    console.log("修改后和购物车全部相同但不改变")
                  } else {
                    shopCartList[editIndex] = {
                      ...shopCartList[editIndex],
                      qty1: this.foodInfoItem.qty1,
                      newOrderItemMTypeList: localmtypeListArry,
                      newOrderItemFoodTypeList: localfoodTypeListArry,
                      newOrderItemFoodList: localfoodListArry,
                      newOrderItemMListList: localmlistListArry
                    }
                    this.updateHashRecord(shopCartList[editIndex])
                    console.log("修改和购物车相同code，不同细项")
                  }
                }
              }
            } else {
              shopCartList[editIndex] = {
                ...shopCartList[editIndex],
                qty1: this.foodInfoItem.qty1
              }
              this.updateHashRecord(shopCartList[editIndex])
            }
            sessionStorage.setItem("shopCartList", JSON.stringify(shopCartList))
            this.shopCartList = JSON.parse(sessionStorage.getItem("shopCartList"))
            this.showFoodWarp = false
            this.joinType = ""
          },

          // 动态显示价格字段
          priceName(type) {
            const priceNum = this.getPriceName.replace("PRICE", "")
            let name =
              type === "foodList" ? `upa${priceNum}` : `price${priceNum === "1" ? "" : priceNum}`
            return name
          },
          // 排序
          sortAllList(arry, codeType, sortType) {
            if (arry.length != 0) {
              let useSort = false
              for (let i = 0; i < arry.length; i++) {
                let item = arry[i]
                if (!item) continue
                if (item[sortType] && item[sortType] != 0) {
                  useSort = true
                  break
                }
              }
              // 实行排序
              if (useSort) {
                arry.sort(function (a, b) {
                  if (a[sortType] == null && b[sortType] == null) {
                    return a[codeType].localeCompare(b[codeType])
                  } else if (a[sortType] == null) {
                    return 1
                  } else if (b[sortType] == null) {
                    return -1
                  } else if (a[sortType] == b[sortType]) {
                    return a[codeType].localeCompare(b[codeType])
                  } else {
                    return a[sortType] - b[sortType]
                  }
                })
              } else {
                arry.sort(function (a, b) {
                  return a[codeType].localeCompare(b[codeType])
                })
              }
            }
          },
          // 递归排序
          recursiveSort(sortItem) {
            if (sortItem.foodList && sortItem.foodList.length != 0) {
              // this.sortAllList(sortItem.foodList, "fCode", "seq")
              sortItem.foodList.forEach(item => {
                this.recursiveSort(item)
              })
            }
            if (sortItem.mListList && sortItem.mListList.length != 0) {
              sortItem.mListList.forEach(item => {
                this.recursiveSort(item)
              })
            }
            if (sortItem.allTypeArry && sortItem.allTypeArry.length != 0) {
              this.sortAllList(sortItem.allTypeArry, "code", "finalSort")
              sortItem.allTypeArry.forEach(item => {
                this.recursiveSort(item)
              })

              sortItem.allTypeArry = sortItem.allTypeArry.filter(item => {
                if (item.typeName == "ftyItem") {
                  return item.foodList.length != 0
                } else {
                  return item.mListList.length != 0
                }
              })
            }
          },
          // 验证qty数字
          regularQty(data) {
            let qty = /^\d+$/.test(data) ? +data : 1 //正则验证数字
            return qty
          },
          // 是否显示最大/小Qty限制提示 type:1 = 弹窗, 2: 默认
          isShowQtyBound(data, type) {
            if (type == 1) {
              //  (弹窗 title )
              return (
                // (data.minQty && data.maxQty) ||
                data.minQty2 &&
                data.maxQty2 &&
                !(data.minQty2 == -Infinity && data.maxQty2 == Infinity)
              )
            } else {
              // vItem
              return (
                (data.maxQty || data.minQty) &&
                !(data.maxQty == Infinity && data.minQty == -Infinity)
              )
            }
          },
          regularQtyBound(data) {
            let { maxQty, minQty } = data
            maxQty = typeof maxQty === "number" ? maxQty : "Infinity"
            minQty = typeof minQty === "number" ? minQty : "-Infinity"
            this.$set(data, "maxQty", maxQty)
            this.$set(data, "minQty", minQty)
          },
          regularQty2Bound(data) {
            let { maxQty2, minQty2 } = data
            maxQty2 = typeof maxQty2 === "number" ? maxQty2 : "Infinity"
            minQty2 = typeof minQty2 === "number" ? minQty2 : "-Infinity"
            this.$set(data, "maxQty2", maxQty2)
            this.$set(data, "minQty2", minQty2)
          },

          // 递归处理数据
          recursiveToDealWith(itemData = {}) {
            if (this.hasFoodList(itemData)) {
              let item = itemData.foodList
              this.toDealWithFoodList(item)
            }
            if (this.hasMListList(itemData)) {
              let item = itemData.mListList
              this.toDealWithMlist(item)
            }
            let ftyItemData = itemData.foodTypeList || []
            let mtyItemData = itemData.mTypeList || []
            if (ftyItemData.length != 0) {
              this.toDealWithFty(ftyItemData)
            }
            if (mtyItemData.length != 0) {
              this.toDealWithMty(mtyItemData)
            }
            if (ftyItemData.length != 0 || mtyItemData.length != 0) {
              Vue.set(itemData, "allTypeArry", [...ftyItemData, ...mtyItemData])
              delete itemData.foodTypeList
              delete itemData.mTypeList
            }

            this.toDealWithPack(itemData) // 递归处理打包盒子类型数据
            this.recursiveSort(itemData) // 递归排序单个foodlist里所有数据
            // 兼容allTypeList字段的递归
            if (Array.isArray(itemData.allTypeArry)) {
              this.recursiveMergeMenu(itemData.allTypeArry)
            }
          },
          toDealWithFoodList(item) {
            item.forEach(foodlsitItem => {
              this.combineMenuHelper(foodlsitItem, "foodMap")
              Vue.set(foodlsitItem, "listSelect", false)
              Vue.set(foodlsitItem, "localListSelect", "")
              this.setRequiredItemFlag(foodlsitItem)
              this.regularQty2Bound(foodlsitItem)
              let foodFList = foodlsitItem.foodList || [] //foodList=>每个对象的foodList
              let fPriceName = this.priceName("foodList")
              foodlsitItem.upa1 = foodlsitItem[fPriceName]
              if (foodFList.length != 0) {
                foodlsitItem.listSelect = true //把foodList的我的细项加入选项
                foodlsitItem.newOrderItemFoodList = [...this.onDisableItem(foodFList)]
                foodFList.forEach(i => {
                  this.combineMenuHelper(i, "foodMap")
                  this.setRequiredItemFlag(i)
                })
              }
              let foodMlist = foodlsitItem.mListList || [] //foodList=>每个对象的mlistList
              if (foodMlist.length != 0) {
                foodlsitItem.listSelect = true //把foodList的我的细项加入选项
                foodlsitItem.newOrderItemMListList = [...this.onDisableItem(foodMlist)]
                foodMlist.forEach(i => {
                  this.combineMenuHelper(i, "mListMap")
                  this.setRequiredItemFlag(i)
                })
              }
              let foodFty = foodlsitItem.foodTypeList || []
              if (foodFty.length != 0) {
                foodFty.forEach(myXi => {
                  this.combineMenuHelper(myXi, "foodTypeMap")
                  foodlsitItem.listSelect = true //把foodList的我的细项加入选项
                  myXi.foodList.forEach(i => {
                    this.combineMenuHelper(i, "foodMap")
                  })
                })
              }
              let foodMty = foodlsitItem.mTypeList || []
              if (foodMty.length != 0) {
                foodMty.forEach(myXi => {
                  this.combineMenuHelper(myXi, "mTypeMap")
                  foodlsitItem.listSelect = true //把foodList的我的细项加入选项
                  myXi.mListList.forEach(i => {
                    this.combineMenuHelper(i, "mListMap")
                  })
                })
              }
              this.recursiveToDealWith(foodlsitItem)
            })
          },

          toDealWithMlist(item) {
            item.forEach(mlsitItem => {
              this.combineMenuHelper(mlsitItem, "mListMap")
              // 我的细项
              this.regularQtyBound(mlsitItem)
              let mPriceName = this.priceName("mList")
              mlsitItem.price = mlsitItem[mPriceName]
              Vue.set(mlsitItem, "listSelect", false)
              Vue.set(mlsitItem, "localListSelect", "")
              this.setRequiredItemFlag(mlsitItem)
              let mMlist = mlsitItem.mListList || [] //foodList=>每个对象的mlistList
              if (mMlist.length != 0) {
                mlsitItem.listSelect = true
                mlsitItem.newOrderItemMListList = [...this.onDisableItem(mMlist)]
                mMlist.forEach(i => {
                  this.combineMenuHelper(i, "mListMap")
                  this.setRequiredItemFlag(i)
                })
              }
              let mMty = mlsitItem.mTypeList || [] //foodList=>每个对象的mtypeList
              if (mMty.length != 0) {
                mMty.forEach(myXi => {
                  this.combineMenuHelper(myXi, "mTypeMap")
                  mlsitItem.listSelect = true //把foodList的我的细项加入选项
                  myXi.mListList.forEach(i => {
                    this.combineMenuHelper(i, "mListMap")
                  })
                })
              }
              this.recursiveToDealWith(mlsitItem)
            })
          },
          toDealWithFty(item) {
            item.forEach(inItem => {
              this.combineMenuHelper(inItem, "foodTypeMap")
              inItem.typeName = "ftyItem" //挂上标识 混合排序用
              this.regularQtyBound(inItem)
              inItem.foodList.forEach(ftyItem => {
                this.combineMenuHelper(ftyItem, "foodMap")
                this.regularQty2Bound(ftyItem)
                let fPriceName = this.priceName("foodList")
                ftyItem.upa1 = ftyItem[fPriceName]
                Vue.set(ftyItem, "selected", false) //细项点击
                Vue.set(ftyItem, "takeUpQty", ftyItem.takeUpQty || 1)
                // 我的的细项之固定细项
                Vue.set(ftyItem, "listSelect", false)
                let ftyFlist = ftyItem.foodList || [] //foodTypeList=>foodList=>foodList
                if (ftyFlist.length != 0) {
                  ftyFlist.forEach(f => {
                    this.combineMenuHelper(f, "foodMap")
                    this.setRequiredItemFlag(f)
                  })
                  ftyItem.listSelect = true
                }
                let ftyMlist = ftyItem.mListList || [] //foodTypeList=>foodList=>mListList
                if (ftyMlist.length != 0) {
                  ftyMlist.forEach(m => {
                    this.combineMenuHelper(m, "mListMap")
                    this.setRequiredItemFlag(m)
                  })
                  ftyItem.listSelect = true
                }
                if (ftyItem.foodTypeList && ftyItem.foodTypeList.length != 0) {
                  ftyItem.foodTypeList.forEach(v => {
                    this.combineMenuHelper(v, "foodTypeMap")
                    v.typeName = "ftyItem" //挂上标识 混合排序用
                    if (v.foodList && v.foodList.length != 0) {
                      v.foodList.forEach(Inv => {
                        this.combineMenuHelper(Inv, "foodMap")
                        ftyItem.listSelect = true
                        Vue.set(Inv, "takeUpQty", Inv.takeUpQty || 1)
                      })
                    }
                  })
                }
                let ftyInfoInMty = ftyItem.mTypeList || [] //foodTypeList=>foodList=>mTypeList
                if (ftyInfoInMty.length != 0) {
                  ftyInfoInMty.forEach(myXi => {
                    this.combineMenuHelper(myXi, "mTypeMap")
                    myXi.typeName = "mtyItem"
                    ftyItem.listSelect = true //把foodList的我的细项加入选项
                    myXi.mListList.forEach(i => {
                      this.combineMenuHelper(i, "mListMap")
                      ftyItem.listSelect = true
                      Vue.set(i, "takeUpQty", i.takeUpQty || 1)
                    })
                  })
                }

                this.recursiveToDealWith(ftyItem)
              })
            })
          },
          toDealWithMty(item) {
            item.forEach(inItem => {
              this.combineMenuHelper(inItem, "mTypeMap")
              inItem.typeName = "mtyItem" //挂上标识 混合排序用
              this.regularQtyBound(inItem)
              inItem.mListList.forEach(mtyItem => {
                this.combineMenuHelper(mtyItem, "mListMap")
                let mPriceName = this.priceName("mList")
                mtyItem.price = mtyItem[mPriceName]
                Vue.set(mtyItem, "selected", false)
                Vue.set(mtyItem, "takeUpQty", mtyItem.takeUpQty || 1)
                // 我的细项
                Vue.set(mtyItem, "listSelect", false)
                let mtyMlist = mtyItem.mListList || []
                if (mtyMlist.length != 0) {
                  mtyItem.listSelect = true
                  mtyMlist.forEach(i => {
                    this.combineMenuHelper(i, "mListMap")
                    Vue.set(i, "takeUpQty", i.takeUpQty || 1)
                    this.setRequiredItemFlag(i)
                  })
                }
                //
                let mtyInMlisInMty = mtyItem.mTypeList || []
                if (mtyInMlisInMty.length != 0) {
                  mtyInMlisInMty.forEach(myXi => {
                    this.combineMenuHelper(myXi, "mTypeMap")
                    myXi.typeName = "mtyItem"
                    mtyItem.listSelect = true
                    myXi.mListList.forEach(i => {
                      this.combineMenuHelper(i, "mListMap")
                      Vue.set(i, "takeUpQty", i.takeUpQty || 1)
                    })
                  })
                }
                this.recursiveToDealWith(mtyItem)
              })
            })
          },

          // 处理打包盒子类型数据
          toDealWithPack(item) {
            let packingBoxMList = item.packingBoxMList || {}
            if (Object.keys(packingBoxMList).length !== 0) {
              let qty = this.regularQty(packingBoxMList.qty1) // 验证qty数字
              Vue.set(item.packingBoxMList, "qty1", qty)
              //处理对应价格
              let mPriceName = this.priceName("mList")
              packingBoxMList.price = packingBoxMList[mPriceName]
            }
          },

          // 第二层追加套餐点击细项
          secAddSMXiSelect(item, type, vItem) {
            if (item.itemCtrl) return // 判断点击细项是否售罄
            if (vItem.isExpired || item.isExpired) {
              this.showOutTimeTips()
              return
            }
            if (this.secVerifyQty2("max", type, item, vItem)) return
            if ((item.takeUpQty || 1) > vItem.maxQty) {
              return
            }
            if (this.checkLocalSIFCodes(item, 1, 3)) {
              let { limitByFcode, language } = this.openTable
              this.layerDia(limitByFcode[language])
              return
            }
            if (item.selected) {
              if (item.qty1 + item.takeUpQty > (item.inventory || Infinity)) {
                this.layerDia(
                  this.systemLanguage.insufficientInventory.replace("#qty", item.inventory)
                )
                // 库存不足
                return
              }
            }
            if (item.takeUpQty > (item.inventory || Infinity)) {
              this.layerDia(
                this.systemLanguage.insufficientInventory.replace("#qty", item.inventory)
              )
              // 库存不足
              return
            }
            if (item.selected) {
              if (vItem.lNoDup) {
                this.layerDia(this.systemLanguage.lNoDupTxt)
                return // lNoDup为pos设置type下的list不可多选,数量为1
              }
              this.secAddSMXiAddXiItem(item, type, vItem) // 增加数量
              if (this.itemFoldable) {
                const isMax = this.satisfyMaxQty(vItem)
                //满足最大Qty,折叠当前type
                if (isMax) this.foldType(vItem, 2)
              }
              return
            }

            let foodtypeInList = this.localSecAddSMXiFtyArry //细项foodTypeList.foodList
            let mtypeInList = this.localSecAddSMXiMtyArry //细项foodTypeList.foodList
            // if (this.secVerifyQty2("max", type, item, vItem)) return;
            switch (type) {
              case "foodtypeInList":
                this.radioXi(foodtypeInList, vItem, item.takeUpQty || 1, type)
                this.localSecAddSMXiFtyArry = this.mergeFtypeList(foodtypeInList, item, vItem)
                break
              case "mtypeInList":
                this.radioXi(mtypeInList, vItem, item.takeUpQty || 1, type)
                this.localSecAddSMXiMtyArry = this.mergeMtypeList(mtypeInList, item, vItem)
                break
            }
            Vue.set(item, "selected", true)
            Vue.set(item, "qty1", item.resetQty)
            if (this.itemFoldable) {
              const isMax = this.satisfyMaxQty(vItem)
              //满足最大Qty,折叠当前type
              if (isMax) this.foldType(vItem, 2)
            }
          },
          secDelXiItem(item, type, outCode) {
            let clickXiOutCode = outCode
            let clickXiItem = item
            let delItemArry = []
            if (type == "foodtypeInList") {
              let localftyList = this.localSecAddSMXiFtyArry
              localftyList.forEach(vItem => {
                if (vItem.code == clickXiOutCode) {
                  // 获取删除的item
                  delItemArry = vItem.newOrderItemFoodList.filter((e, i) => {
                    return e.fCode == clickXiItem.fCode
                  })
                  let foodListArry = vItem.newOrderItemFoodList.filter((e, i) => {
                    return e.fCode != clickXiItem.fCode
                  })
                  vItem.newOrderItemFoodList = [...foodListArry]
                }
              })
              // 过滤空数据
              this.localSecAddSMXiFtyArry = this.localSecAddSMXiFtyArry.filter(item => {
                return item.newOrderItemFoodList.length != 0
              })
            } else if (type == "mtypeInList") {
              let localmtyList = this.localSecAddSMXiMtyArry
              localmtyList.forEach(vItem => {
                if (vItem.code == clickXiOutCode) {
                  let mListArry = vItem.newOrderItemMListList.filter((e, i) => {
                    return e.code != clickXiItem.code
                  })
                  vItem.newOrderItemMListList = [...mListArry]
                }
              })
              this.localSecAddSMXiMtyArry = this.localSecAddSMXiMtyArry.filter(item => {
                return item.newOrderItemMListList.length != 0
              })
            }
            console.log(
              this.localSecAddSMXiFtyArry,
              this.localSecAddSMXiMtyArry,
              "删除细项第二层弹窗数据"
            )
            item.selected = false // 取消选中
            item.qty1 = item.resetQty // 重置数量
            // 计算删除后的本地总数量
            this.getDelLocalNum(item, delItemArry)
          },
          secAddSMXiAddXiItem(item, type, vItem) {
            let clickXiType = type
            let { code: clickXiOutCode } = vItem
            let codeTxt = type === "foodtypeInList" ? "fCode" : "code"
            let t = type === "foodtypeInList" ? "newOrderItemFoodList" : "newOrderItemMListList"
            let vItemList = type === "foodtypeInList" ? vItem.foodList : vItem.mListList
            let localList =
              type === "foodtypeInList" ? this.localSecAddSMXiFtyArry : this.localSecAddSMXiMtyArry
            let index = localList.findIndex(item => item.code === clickXiOutCode)
            let qtySum = localList[index][t].reduce((total, item) => {
              return total + (item.takeUpQty || 1)
            }, 0)
            let obj = {
              ...item,
              price: item.price,
              qty1: 1
            }
            let targetData = {
              localList,
              clickXiOutCode,
              vItem,
              item,
              clickXiType,
              qtySum,
              index,
              next: item.takeUpQty || 1,
              t,
              codeTxt,
              vItemList
            }
            if (this.exceedMaximumBySelected(targetData)) {
              return
            }
            localList[index][t].push(obj)
            item.qty1++
            console.log(
              this.localSecAddSMXiFtyArry,
              this.localSecAddSMXiMtyArry,
              "点击增加细项数量后"
            )
          },
          // 每次打开弹窗重置数量
          resetCurrentDiaNum(diaItem) {
            let allTypeArry = diaItem.allTypeArry || []
            allTypeArry.forEach(item => {
              if (item.typeName == "ftyItem") {
                if (item.foodList && item.foodList.length != 0) {
                  item.foodList.forEach(inItem => {
                    inItem.qty1 = inItem.resetQty
                  })
                }
              }
              if (item.typeName == "mtyItem") {
                if (item.mListList && item.mListList.length != 0) {
                  item.mListList.forEach(inItem => {
                    inItem.qty1 = inItem.resetQty
                  })
                }
              }
            })
          },
          //预览图片初始化
          previewFun() {
            var options = {
              navbar: false, // 布尔值/整型 true 显示缩略图导航
              toolbar: false, // 布尔值/整型 true 显示工具栏
              title: false, // 布尔值/整型 true 显示当前图片的标题（现实 alt 属性及图片尺寸）
              toggleOnDblclick: false,
              // container: document.querySelector('#app'),
              url: "data-original",
              show: () => {
                console.log("show")
              }
            }
            $(".food_info_img_Tag").viewer(options)
          },

          // 细项价钱显示
          showXiPrice(price) {
            const absPrice = Math.abs(price)
            const sign = price < 0 ? "-" : "+"
            return `${sign}${this.currencyWay}${retainSignificantDecimals(absPrice)}`
          },
          showOuterPrice(price) {
            const absPrice = Math.abs(price)
            const sign = price < 0 ? "-" : ""
            return `${sign}${this.currencyWay}${retainSignificantDecimals(absPrice)}`
          },
          // 单个大food的所有价格
          showCardPrice(item) {
            let allPrice = this.calculatedTotal(item)
            // console.log(allPrice, 'allPrice')
            return this.showOuterPrice(allPrice)
          },
          showPreOrderDia() {
            let { preOrderDiaTittle } = this.systemLanguage
            layer.open({
              skin: "preOrderDiaLayer",
              type: 1,
              shade: [0.1, "#fff"],
              title: preOrderDiaTittle, //不显示标题
              content: $(".preOrderDia"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
              success: (layero, index) => {
                // 打开前重置数据
                this.preOrderObj = {
                  tableNum: "",
                  tableKey: ""
                }
                this.preOrderDiaIndex = index
              },
              cancel: (index, layero) => {
                this.onPreOrderCancel()
              }
            })
          },
          onPreOrderCancel() {
            let { preOrderCancelTip } = this.systemLanguage
            layer.close(this.preOrderDiaIndex, () => {
              //do something
              layer.msg(preOrderCancelTip, {
                shade: [0],
                time: 0,
                // shadeClose: true,
                closeBtn: 2,
                anim: 6
              })
            })
            // layer.close(this.preOrderDiaIndex)
          },
          onPreOrderSub() {
            let { tableNum, tableKey } = this.preOrderObj
            let payType = this.openTable.payType || []
            if (tableNum == "" || tableKey == "") {
              let { preOrderRequiredTip } = this.systemLanguage
              layer.msg(preOrderRequiredTip, {
                shade: [0],
                time: 1000,
                anim: 6
              })
            } else {
              console.log(tableNum, tableKey, "值")
              layer.close(this.preOrderDiaIndex)
              let params = {
                tableNumber: tableNum,
                tableKey,
                isPreOrder: true
              }
              this.sendOrder(params)
            }
          },
          // 处理软键盘弹起顶起页面
          keepPageHeight() {
            const originalHeight =
              document.documentElement.clientHeight || document.body.clientHeight
            window.removeEventListener("resize", window.diy_resize, false)
            window.onresize = () => {
              return (() => {
                //键盘弹起与隐藏都会引起窗口的高度发生变化
                const resizeHeight =
                  document.documentElement.clientHeight || document.body.clientHeight
                // console.log("进入到判断页面高度=========")
                // console.log("页面初始高度=========" + originalHeight)
                // console.log("软键盘弹起高度=========" + resizeHeight)
                let cartWarpDom = document.querySelector(".cart_warp")
                if (!cartWarpDom) return
                if (resizeHeight - 0 < originalHeight - 0) {
                  //当软键盘弹起，在此处操作
                  // console.log("进入到软键盘弹起=========")
                  cartWarpDom.setAttribute("style", "height:" + originalHeight + "px;")
                  this.scrollerHeight = resizeHeight
                } else {
                  //当软键盘收起，在此处操作
                  // console.log("进入到软键盘收起=========")
                  cartWarpDom.setAttribute("style", "height:100%;")
                  this.scrollerHeight = "100%"
                }
              })()
            }
          },
          // 打开购物车把热门数据塞进数组
          addhotSaleData() {
            let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
            let cloneAllDataList = Object.freeze(this.allDataList) // 应避免使用深拷贝copy allDataList,若数据量大,很耗时
            let allHotSaleObj = {} // 所有类型的推荐食品
            let shopCartFoodName = []
            shopCartList.forEach(item => {
              shopCartFoodName.push(item.fCode)
              let foodHotData = item.hotSaleMap //购物车food自带的热门食品
              let ftyItem = cloneAllDataList.find(ftyItem => item.ftCode == ftyItem.code) || {}
              let ftyHotData = ftyItem.hotSaleFTCodes //购物车food父级fty自带的热门食品
              // let ftyHotData = "BA, AL,"
              if (foodHotData) {
                for (var i in foodHotData) {
                  //(i.g. BA03:BA)
                  let ftyCode = foodHotData[i]
                  if (allHotSaleObj[ftyCode]) {
                    //判断总热门数组是否已经存在fty的类别
                    if (!allHotSaleObj[ftyCode].includes(i)) {
                      //避免重复热门food
                      allHotSaleObj[ftyCode].push(i)
                    }
                  } else {
                    //不存在直接创建新fty类别
                    allHotSaleObj[ftyCode] = [i]
                  }
                }
              }
              if (ftyHotData) {
                // 去除空格根据,截取字符串
                let ftyHotDataList = ftyHotData.replace(/\s*/g, "").split(";")
                ftyHotDataList.forEach(ftyCode => {
                  let ftyItemData = cloneAllDataList.find(ftyItem => ftyItem.code == ftyCode)
                  let currentFoodCode = []
                  if (ftyItemData) {
                    ftyItemData.foodList.forEach(inItem => {
                      currentFoodCode.push(inItem.fCode)
                    })
                  }
                  if (allHotSaleObj[ftyCode]) {
                    // 遍历判断是否有foodCode
                    allHotSaleObj[ftyCode] = [
                      ...new Set(allHotSaleObj[ftyCode].concat(currentFoodCode))
                    ]
                  } else {
                    allHotSaleObj[ftyCode] = [...currentFoodCode]
                  }
                })
              }
            })

            let { hotSaleNum } = this.openTable
            let hotSaleData = []
            // 获取所有food携带的热门食品
            for (var i in allHotSaleObj) {
              let ftyCode = i
              let foodCodeList = allHotSaleObj[i]
              cloneAllDataList.forEach(ftyItem => {
                if (ftyItem.code == ftyCode) {
                  let foodList = ftyItem.foodList
                  foodList.forEach(foodItem => {
                    if (foodCodeList.includes(foodItem.fCode)) {
                      hotSaleData = [...hotSaleData, foodItem]
                    }
                  })
                }
              })
            }

            if (hotSaleData.length != 0) {
              // 过滤购物车已加购食品(过滤重复以及售罄数据)
              hotSaleData = hotSaleData.filter(
                item => !shopCartFoodName.includes(item.fCode) && !item.itemCtrl
              )
              // 随机抽取数量显示
              if (hotSaleNum > hotSaleData.length) {
                this.hotSaleData = hotSaleData
              } else {
                this.hotSaleData = this.getRandomArr(hotSaleData, +hotSaleNum)
              }
              // 随机排序
              const randomSort = () => {
                return Math.random() > 0.5 ? -1 : 1
              }
              this.hotSaleData.sort(() => randomSort())
            }
            // console.log(JSON.parse(JSON.stringify(hotSaleData)), '后面hotSaleData');

            // console.log(this.getRandomArr(hotSaleData, 6), 'this.hotSaleData')
          },
          onShowhotSaleItem(item) {
            // 自身数据定义超时fty
            if (item.hasOwnProperty("isExpired") && item.isExpired)
              return this.layerDia(this.systemLanguage.dataTimeOutTip)

            let cloneFoodItem = JSON.parse(JSON.stringify(item))
            this.onfoodInfo(cloneFoodItem)
          },
          getRandomArr(arr, count) {
            //随机抽取
            var shuffled = arr.slice(0),
              i = arr.length,
              min = i - count,
              temp,
              index
            while (i-- > min) {
              //打乱数组
              index = Math.floor((i + 1) * Math.random())
              temp = shuffled[index]
              shuffled[index] = shuffled[i]
              shuffled[i] = temp
            }
            return shuffled.slice(min)
          },
          // 提交订单动态提示
          showPostOrderPopup(resultObj, data) {
            let sponsored = !!this.openTable.sponsoredLink
            layer.open({
              skin: "postOrderDiaLayer",
              type: 1,
              shade: [0.1, "#fff"],
              btn: sponsored ? ["OK", "No,thanks"] : ["OK"],
              title: false, //不显示标题
              content: $(".postOrderPopup"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
              end: () => {
                this.orderSuccessEndLogic(resultObj, data)
              },
              success: () => {
                $(".layui-layer-btn1").css({
                  backgroundColor: "#ff5721",
                  color: "white",
                  borderColor: "#ff5721"
                })
              },
              yes: index => {
                if (sponsored) {
                  let url = this.openTable.sponsoredLink
                  if (url.indexOf("http") == -1) {
                    url = "http://" + url
                  }
                  let lan = this.openTable.language === "zh" ? "tc" : "en"
                  url += `?locale=${lan}&store=${this.openTable.storeNumber}`
                  window.open(url)
                  goMenuPage()
                } else {
                  layer.close(index)
                }
              }
            })
          },

          // 提交订单默认提示
          showDefSuccessMsg(defSuccessMsg, resultObj, data) {
            layer.msg(
              defSuccessMsg,
              {
                time: 1500 //2秒关闭（如果不配置，默认是3秒）
              },
              () => {
                this.orderSuccessEndLogic(resultObj, data)
              }
            )
          },
          // 提交订单成功后清理数据
          orderSuccessEndLogic(resultObj, data) {
            this.openTable = {
              ...this.openTable,
              tableKey: resultObj.tableKey,
              tableNumber: resultObj.tableNumber || data.tableNumber
            }
            sessionStorage.setItem("openTable", JSON.stringify(this.openTable))
            if (
              this.openTable.initialTableNum == "lbsMode" ||
              this.openTable.initialTableNum == "StaffMode"
            ) {
              // 公共函数返回首页
              localStorage.setItem("staffCode", this.openTable.staffCode)
              localStorage.setItem("staffPassword", this.openTable.staffPassword)
              goIndex()
            } else {
              if (this.openTable.goHistoryAfterOrder) {
                goHistoryAfterOrder(this.openTable.performType)
              } else {
                goMenuPage()
              }
            }
          },
          // 确认下单弹窗
          confirmOrderDia(payMethod) {
            let {
              btnTxtForConfirm,
              btnTxtForCancel,
              payAtCashierDiaContent,
              confirmOrderDiaContent
            } = this.systemLanguage
            let dialogContent =
              payMethod === "payAtCashier" && this.showSendOrderView
                ? payAtCashierDiaContent
                : confirmOrderDiaContent
            return new Promise((res, rej) => {
              layer.confirm(
                dialogContent,
                {
                  title: false,
                  closeBtn: false,
                  skin: "baseLayer payAtCashierLayer",
                  area: ["85%"],
                  btn: [btnTxtForCancel, btnTxtForConfirm] //按钮
                },
                (index, layero) => {
                  layer.close(index)
                  res(false)
                },
                () => {
                  res(true)
                }
              )
            })
          },

          // 切换免责声明的选中状态
          toggleAgreementStatus(event) {
            // 若pointerType<点击的是后面的文字> 不触发
            if (!event.pointerType) return
            this.sendOrderForm.agreement = !this.sendOrderForm.agreement
          },
          // 点击免责声明复选框
          onAgreementCheck() {
            this.showAgreement = true
            setTimeout(() => {
              this.$nextTick(() => {
                let dom = this.$refs["agreement-scroll-text"]
                let maxScroll = dom.scrollHeight - dom.offsetHeight
                if (!maxScroll || !dom.scrollHeight) {
                  this.useAgreementBtn = false
                }
              })
            })
          },
          // 关闭免责声明弹窗
          onCloseAreementDialog() {
            this.showAgreement = false
            this.sendOrderForm.agreement = true
            this.$nextTick(() => {
              this.$refs["agreement-scroll-text"].scrollTo(0, 0)
            })
          },
          // 免责声明内容滚动
          onAgreementContentScroll(e) {
            let maxScroll = e.target.scrollHeight - e.target.offsetHeight
            if (maxScroll - e.target.scrollTop <= 5) {
              this.useAgreementBtn = false
            }
          },
          // 显示付款号码
          showPayAtCashierNum() {
            layer.open({
              skin: "paidAtCashierLayer",
              type: 1,
              shade: [0.8, "#000"],
              title: false, //不显示标题
              closeBtn: 0,
              content: $(".paidAtCashierSucc"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
              success: (layero, index) => {
                //生成二维码
                new QRCode("paidAtCashierQRcode", {
                  text: this.payAtCashierNum,
                  width: 150,
                  height: 150,
                  colorDark: "#000000",
                  colorLight: "#ffffff",
                  correctLevel: QRCode.CorrectLevel.H
                })
                // 清除缓存
                sessionStorage.removeItem("shopCartList")
                // this.preOrderDiaIndex = index
              }
            })
          },
          // 计算出总价
          getAllFoodPrice(list, exclude) {
            let shopCartList = JSON.parse(sessionStorage.getItem("shopCartList")) || []
            let allPrice = 0
            // 加上餐具的价格
            let array =
              list ||
              shopCartList.concat(
                this.getSelectedAdditionalItemsData(),
                this.getSelectedAdditionalItemsData(false)
              )
            array.forEach(item => {
              let oneFoodPrice = this.calculatedTotal(item, exclude)
              allPrice = floatAdd(allPrice, oneFoodPrice)
            })
            // console.log(allPrice, "allPrice总计")
            return retainSignificantDecimals(allPrice)
          },
          // 计算是否费用大于0启用在线支付 前端显示 不加服务费的总价 getallfoodprice
          costsIncurred() {
            const allPrice = this.getAllFoodPrice()
            const { serviceCharges, keepDecimals } = this.openTable
            //num:价格,direction:舍入方向,decimalPlaces:保留小数位数,shouldPad:是否补零
            const computePrice = (num, direction, decimalPlaces, shouldPad) => {
              const factor = Math.pow(10, decimalPlaces)
              let result
              switch (direction) {
                // 向上
                case "ceiling":
                  result = Math.ceil(num * factor) / factor
                  break
                // 向下
                case "floor":
                  result = Math.floor(num * factor) / factor
                  break
                // 四拾伍入
                case "rounding":
                  result = Math.round(num * factor) / factor
                  break
                default:
                  result = num //'noChanage' 不改变价格
              }
              return shouldPad ? Number(result.toFixed(decimalPlaces)) : result
            }

            const calculateServiceCharge = (price, serviceCharges, keepDecimals) => {
              const totalCharge = floatAdd(floatMultiply(price, 0.1), price)
              return computePrice(
                totalCharge,
                serviceCharges.countType,
                keepDecimals ? keepDecimals.significantDigits : 0,
                keepDecimals ? keepDecimals.zeroPadding : false
              )
            }

            if (serviceCharges && keepDecimals) {
              if (serviceCharges.openType == "perItem") {
                //perItem逻辑暂不支持,以下先进行保留小数处理(后期需求再进行修改)
                return retainSignificantDecimals(allPrice)
              } else {
                return calculateServiceCharge(allPrice, serviceCharges, keepDecimals)
              }
            } else if (keepDecimals) {
              return retainSignificantDecimals(allPrice)
            } else if (serviceCharges) {
              return calculateServiceCharge(allPrice, serviceCharges, null)
            } else {
              return allPrice
            }
          },

          showPrivacy() {
            layer.open({
              type: 2,
              title: " ",
              shadeClose: true,
              shade: false,
              // closeBtn: 0,
              // maxmin: true, //开启最大化最小化按钮
              area: ["80%", "80%"],
              content: "../order/privacyPage.html"
            })
          },
          ValidationEmail() {
            let email = this.payEmail.replace(/\s+/g, "")
            let rus =
              /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
            return rus.test(email)
          },

          // 拼接商品图片地址
          jointImgUrl(item, type, width, height) {
            let imgUrl = "",
              resizeParams = ""
            switch (type) {
              case "food":
                imgUrl = this.baseUrl + "food/" + item.fCode
                break
              case "mlist":
                imgUrl = this.baseUrl + "mlist/" + item.code
                break
              case "fty":
                imgUrl = this.ftyBaseUrl + item.code
                break
              default:
                break
            }
            let suffix = this.setImgSuffix(item)
            // return `${imgUrl}.${suffix}?x-oss-process=image/resize,w_${width},h_${height},${item.photoTime}`
            if (width && height) {
              resizeParams = `,w_${width},h_${height}`
            } else if (!width && !height) {
              resizeParams = `,w_200,h_200`
            } else if (width) {
              resizeParams = `,w_${width}`
            } else if (height) {
              resizeParams = `,h_${height}`
            }
            return `${imgUrl}.${suffix}?x-oss-process=image/resize${resizeParams},${item.photoTime}`
          },
          setImgSuffix(item) {
            let suffix = ""
            if (item.photoSuffix) {
              suffix = item.photoSuffix
              // console.log('触发',item.name||item.fName)
            } else {
              suffix = "png"
            }
            return suffix
          },
          //拼接imgTagUrl
          imgTagUrl(item, extraPaths) {
            let { language } = this.openTable
            let imageTagName = extraPaths.substring(0, extraPaths.length - 1)
            // 判断对象是否存在个属性
            if (this.configureImgUrl.hasOwnProperty(imageTagName)) {
              let imgTagseries = this.configureImgUrl[imageTagName]
              return imgTagseries[language]
            } else {
              return undefined
            }
          },

          // 校验 折扣码 接口
          checkDiscountCode() {
            let { companyName, storeNumber, language, tableNumber, performType } = this.openTable
            let data = {
              companyName,
              storeNumber,
              tableNumber,
              performType,
              promotionDiscountCode: this.sendOrderForm.discountCode, //order页面请求 保存的字段不同
              newOrderItem_foodListJson: sessionStorage.getItem("shopCartList"),
              additionalItemsForTakeawayFixedList: JSON.stringify(
                this.getSelectedAdditionalItemsData()
              ),
              additionalItemsForTakeawayAutoList: JSON.stringify(
                this.getSelectedAdditionalItemsData(false)
              )
            }
            if (this.choosePayMethod === "payAtCashier") {
              data.promotionDiscountCode = ""
            }
            if (this.isFoodCourtMode) data.storeNumber = "*"
            if (this.sendPromotionDiscountTime) {
              data.promotionDiscountUpdateTime = this.openTable.promotionDiscountUpdateTime
            }

            return new Promise((res, rej) => {
              // let index = null
              $.ajax({
                type: "POST", //请求方式
                url: "../promotionDiscount/checkCode", //请求url地址
                dataType: "json",
                data,
                success: result => {
                  let codes = [2001, 2002, 200]
                  if (!codes.includes(result.statusCode)) {
                    const isAutoDiscount = sessionStorage.getItem("couponUseType") !== "custom"
                    this.discountErrorTip(result, data, isAutoDiscount) //错误状态码提示
                    rej({ ...result, autoDiscount: isAutoDiscount })
                  } else {
                    this.confirmDiscountCode = this.sendOrderForm.discountCode
                    this.discountCompulsory = result.compulsory
                    // result.totalPrice = 100000
                    res(result)
                  }
                },
                error: err => {
                  this.subOrderErrorTip({ statusCode: 500 })
                  rej(err)
                }
              })
            })
          },
          // 折扣错误提示
          discountErrorTip(result, requestParams, autoDiscount) {
            let {
              errorDiscount,
              errorMsg,
              discountPriceMismatch,
              PCLoginMemberTimeout,
              errorServiceTip
            } = this.systemLanguage
            switch (result.statusCode) {
              case 408: //包含了很多错误信息
                this.error408CallBack(result, autoDiscount)
                if (autoDiscount) this.checkDiscountAgain()
                break
              case 4013:
                layer.alert(discountPriceMismatch, {
                  title: false,
                  skin: "defaultLayer",
                  btn: [this.systemLanguage.confirmBtn],
                  yes: i => {
                    layer.close(i)
                  },
                  cancel: () => {}
                })
                break
              case 5001:
                layer.msg(PCLoginMemberTimeout, {
                  shade: [0.1, "#fff"], // 设置遮罩层透明度和颜色
                  time: 3000, // 显示时间，单位毫秒
                  end: this.showUserPopup // 消息关闭后的回调函数
                })
                break
              default:
                layer.msg(errorServiceTip)
            }
          },

          // 多次请求校验折扣码接口 处理返回的价格 防抖
          checkDiscountAgain() {
            let {
              promotionDiscount,
              serviceCharges: { displayInShoppingCartPage } = {},
              billTax
            } = this.openTable
            const shouldSkipDiscount = () => {
              // 无折扣且未登录且无税费且不在购物车页面时跳过
              const basicCondition =
                !promotionDiscount &&
                !this.LoginValid() &&
                !this.existTaxes &&
                !displayInShoppingCartPage &&
                !billTax

              // 购物车为空时跳过
              const emptyCart = !this.shopCartList.length

              // 关闭折扣且无税费且不在购物车页面时跳过
              const discountClosed =
                this.closeDiscount() && !this.existTaxes && !displayInShoppingCartPage

              return basicCondition || emptyCart || discountClosed
            }

            if (shouldSkipDiscount()) return false
            let discountType = sessionStorage.getItem("couponUseType")
            if (discountType === "custom" && this.whetherToCalculateDiscount) {
              // 用户输入折扣码>减满折扣>默认折扣
              this.discountDebounce(this.onRequestDiscount, 300)
            } else {
              const fillDiscount = this.checkDiscountExclude()
              if (fillDiscount && this.choosePayMethod !== "payAtCashier") {
                // 匹配到减满折扣
                this.sendOrderForm.discountCode = fillDiscount.code

                sessionStorage.setItem("couponUseType", "auto")
              } else {
                this.removeDiscount()
              }
              if (
                !this.LoginValid() &&
                !this.existTaxes &&
                !fillDiscount &&
                !displayInShoppingCartPage &&
                !billTax
              )
                return

              this.discountDebounce(this.onRequestDiscount, 300)
            }
          },
          discountDebounce(fn, delay = 200) {
            if (this.discountTimer) {
              clearTimeout(this.discountTimer)
            }
            this.discountTimer = setTimeout(() => {
              fn.apply(this, arguments) // 透传 this和参数
              this.discountTimer = 0
            }, delay)
          },
          // 显示搜索弹窗
          showSearchPopup() {
            // let { searchProductText } = this.systemLanguage
            layer.open({
              skin: "searchLayer",
              type: 1,
              title: false, //不显示标题
              shade: [0.1, "#fff"],
              shadeClose: true,
              content: $("#searchFoodDia"),
              success: (layero, index) => {
                this.searchFoodDiaIndex = index
                // .searchFoodDia-input清除数据并输入框聚焦
                setTimeout(() => {
                  $(".searchFoodDia-input").val("").focus()
                  // 保持页面font-size不变
                })
              },
              end: () => {
                this.machFoodNameList = []
              }
            })
          },

          // 模糊查询 + 高亮匹配
          getSearchList(searchContent) {
            return new Promise((resolve, reject) => {
              if (!searchContent) {
                this.machFoodNameList = []
                reject()
                return
              }
              // 将搜索关键字转义
              const key = searchContent.replace(/([\$\^\*\(\)\+\?\{\}\[\]\.\\])/g, "\\$1")
              const reg = new RegExp(`(${key})`, "igm")
              // 模糊查询
              let resultList = []
              this.allDataList.forEach(item => {
                let foodList = item.foodList || []
                foodList.forEach(food => {
                  let name = this.inListTitle(food)
                  if (reg.exec(name)) {
                    let someObj = {
                      foodType: { ...item },
                      foodList: { ...food }
                    }
                    resultList.push(someObj)
                  }
                })
              })
              //修改高亮文案
              let dataList = []
              resultList.forEach(item => {
                let ftyName = this.outListTitle(item.foodType)
                let foodListName = this.inListTitle(item.foodList).replace(
                  reg,
                  val => `<span class='searchList-highlightTxt'>${val}</span>`
                )
                let obj = {
                  // 不间断空格 \u00A0
                  ftyName,
                  ftyCode: item.foodType.code,
                  foodListName,
                  foodListData: item.foodList
                  // title: ftyName +" "+"/"+" "foodListName
                }
                dataList.push(obj)
              })
              // 把dataList相同的ftyName,ftyCode合并为一个对象,新增foodList数组包含foodListName和foodListData
              let mergeObj = {}
              dataList.forEach(item => {
                if (mergeObj[item.ftyCode]) {
                  mergeObj[item.ftyCode].foodList.push({
                    foodListName: item.foodListName,
                    foodListData: item.foodListData
                  })
                } else {
                  mergeObj[item.ftyCode] = {
                    ftyName: item.ftyName,
                    ftyCode: item.ftyCode,
                    foodList: [
                      {
                        foodListName: item.foodListName,
                        foodListData: item.foodListData
                      }
                    ]
                  }
                }
              })
              let mergeDataList = []
              for (let key in mergeObj) {
                mergeDataList.push(mergeObj[key])
              }
              console.log(
                "🚀 ~ file: menuPage.html:8412 ~ returnnewPromise ~ mergeDataList",
                mergeDataList
              )

              this.machFoodNameList = mergeDataList
              resolve()
            })
          },
          //防抖搜索
          debounceSearchList(e) {
            // 去除首尾空格
            e.target.value = e.target.value.replace(/(^\s*)|(\s*$)/g, "")
            if (e.target.value) {
              if (this.debounceTime != null) {
                clearTimeout(this.debounceTime)
              }
              this.searchListLoading = true
              this.debounceTime = setTimeout(() => {
                this.getSearchList(e.target.value).finally(() => {
                  this.searchListLoading = false
                })
              }, 1000)
            } else {
              this.machFoodNameList = []
              console.log("搜索内容为空")
            }
          },

          // 跳转点击的商品
          jumpToProduct(item) {
            let necessaryCondition1 = item.hasOwnProperty("isExpired") && item.isExpired
            let necessaryCondition2 = item.itemCtrl // 禁止售罄食品点击
            if (necessaryCondition1 || necessaryCondition2) return
            // 关闭searchLayer弹窗
            layer.close(this.searchFoodDiaIndex)
            this.onfoodInfo(item)
          },
          // 搜索框快速添加
          searchAddProduct(e, item) {
            if (this.checkMaxFoodNum(item)) return //限制主food数量下单数量
            // 判断最大food的数量限制
            let allshoplNumber = this.allshoplNumber
            if (allshoplNumber + item.qty1 > this.numberDishes && this.isShowNumberDishes) {
              let { promptText } = this.systemLanguage
              promptText = promptText.replace("#numberDishes", this.numberDishes)
              this.layerDia(promptText)
            } else {
              // +1动画
              console.log(this.checkCartSIFCodes(item, 1), "999")
              var n = 1
              var $i = $("<b/>").text("+" + n)
              var x = e.pageX,
                y = e.pageY
              $i.css({
                "z-index": 999999999,
                top: y - 20,
                left: x,
                position: "absolute",
                color: "var(--styleColor)",
                fontSize: "0.38rem"
              })
              $("body").append($i)
              $i.animate(
                {
                  top: y - 180,
                  opacity: 0
                },
                1500,
                function () {
                  $i.remove()
                }
              )
              e.stopPropagation()
              this.setAnimation(e, ["animate__animated", "animate__bounceIn"])
              this.additem(e, item, false)
            }
          },
          // i18n转化页面字体选项
          showFontSizeOption(type) {
            // 获取当前语言,根据对应语言return 文字
            let { language, pageFontSize = {} } = this.openTable
            let lanObj = {
              en: "firstLan",
              zh: "secondLan",
              thirdLan: "thirdLan"
            }
            let targetObj = pageFontSize[type] || {}
            return targetObj[lanObj[language]]
          },

          razerPay(result) {
            let form = document.createElement("form")
            form.action = result.url
            form.method = "post"
            for (let key in result.body) {
              let input = document.createElement("input")
              input.type = "hidden"
              input.name = key
              input.value = result.body[key]
              form.appendChild(input)
            }
            document.body.appendChild(form)
            form.submit()
          },
          // 验证res.list的排序后的第一条fty的img
          // 场景:当未启用备用oss,且存在时要验证一次
          async verifyOss(e = {}, backupOss) {
            let url =
              this.ftyBaseUrl +
              e.code +
              "." +
              this.setImgSuffix(e) +
              "?x-oss-process=image/resize,w_200,h_200," +
              e.photoTime
            let res = await checkImage(url, [this.defaultOss, backupOss]).catch(err => {})
            if (res && res.url.includes(backupOss)) {
              this.foodBaseUrl = this.foodBaseUrl.replace(this.defaultOss, backupOss)
              this.ftyBaseUrl = this.ftyBaseUrl.replace(this.defaultOss, backupOss)
              this.baseUrl = this.baseUrl.replace(this.defaultOss, backupOss)
            }
          },
          // 显示个人中心
          showUserPopup() {
            return new Promise((res, rej) => {
              if (!this.showCRM) res(true)
              if (!this.$refs.personalCenter || this.isUserLayerOpen) {
                res(true)
                return
              }
              this.isUserLayerOpen = true
              layer.open({
                skin: "userLayer layui-custom-style",
                type: 1,
                title: false, //不显示标题
                closeBtn: 0,
                // shade: [0.1, "#fff"],
                shadeClose: false,
                area: ["100%", "100%"],
                content: $("#personalCenterPopup"),
                success: (layero, index) => {
                  this.$refs.personalCenter.setUserPopIndex(index)
                  //判断cookie是否存在userInfo,存在则直接显示用户信息
                  this.$refs.personalCenter.setUserInfo()
                  // 获取label宽度
                  let personalCenter = app.$root.$refs.personalCenter
                  personalCenter.authFormGetLabelWidth()

                  // this.$refs.personalCenter.handleKeyboardShow() //添加监听
                },
                end: () => {
                  this.$refs.personalCenter.resetLoginForm()
                  // this.$refs.personalCenter.removeEventListener() //移除监听
                  this.isUserLayerOpen = false
                  res(true)
                }
              })
            })
          },
          //下单前校验是否已经登录
          verifyLoginCenter() {
            return new Promise((resolve, reject) => {
              const userInfo = this.LoginValid() || ""
              const personalCenter = this.$refs.personalCenter
                ? this.$refs.personalCenter.$el
                : undefined
              const noLogin = personalCenter ? personalCenter.getAttribute("data-login") : undefined

              if (!this.showCRM) {
                resolve(true)
                return
              }

              if (noLogin === "false" || userInfo) {
                resolve(true)
                return
              }

              const { yes, no, PCLoginConfirmationMessage } = this.systemLanguage
              layer.confirm(
                PCLoginConfirmationMessage,
                {
                  title: false,
                  closeBtn: false,
                  skin: "baseLayer payAtCashierLayer",
                  area: ["85%"],
                  btn: [no, yes]
                },
                (index, layero) => {
                  layer.close(index)
                  personalCenter.cancelLogin = true
                  personalCenter.setAttribute("data-login", "false")
                  resolve(true)
                },
                () => {
                  this.showUserPopup().then(() => {
                    resolve(true)
                  })
                }
              )
            })
          },
          /**
           * @description 是否显示会员商品
           * @param {Object} item foodItem/mlistItem
           * @return {boolean} true:显示
           * */
          displayMemberFood(item) {
            //未登录
            if (this.hasProperty("memberConfig", "pointsShowInLogin") && !this.LoginValid()) {
              // 判断自身
              if (item.points) {
                return false
              }
              // 判断固定细项
              if (this.hasFoodList(item) || this.hasMListList(item)) {
                let list = [...item.foodList, ...(item.mListList || [])]
                // 若全部固定细项都无积分,则显示
                return list.every(e => !e.points)
              }
            }
            return true
          },
          // 判断非会员时是否显示最外层type
          displayMemberFtype(typeItem) {
            //未登录
            if (this.hasProperty("memberConfig", "pointsShowInLogin") && !this.LoginValid()) {
              // 判断固定细项
              let { foodList = [], mListList = [] } = typeItem
              let list = [...foodList, ...mListList]
              // 若有一个细项没有积分,则显示该type
              if (list.length) {
                return list.some(e => !e.points)
              }
            }
            return true
          },

          // 滚动监听器
          onScroll(e) {
            const scrollItems = document.querySelectorAll(".foodCard")
            let newActiveIndex = null
            let { distanceNavHeight = 0 } = this.openTable.infiniteLoop //自定义距离顶部高度
            for (let i = scrollItems.length - 1; i >= 0; i--) {
              // 为了确保与content容器的相对位置，我们需要获取content容器的scrollTop值
              const contentScrollTop =
                this.$refs.contentScrollContainer.scrollTop + distanceNavHeight
              // 确定当前滚动项相对于content容器顶部的距离
              const itemTopRelativeToContainer =
                scrollItems[i].offsetTop - this.$refs.contentScrollContainer.offsetTop
              // 判断滚动条滚动距离是否大于当前滚动项相对于content容器顶部的距离
              const isItemScrolledPast = contentScrollTop >= itemTopRelativeToContainer
              if (isItemScrolledPast) {
                //获取scrollItems[i]的id,截取tab后面的数字
                let id = +scrollItems[i].id.replace("tab", "")
                let code = scrollItems[i].getAttribute("data-code")
                newActiveIndex = code
                // newActiveIndex = id
                break
              }
            }
            if (newActiveIndex !== null && this.tabIsActive !== newActiveIndex) {
              this.tabIsActive = newActiveIndex
            }
          },
          // 锚点导航-主要代码逻辑
          goAnchor(code) {
            let { verticalOrderLayout, infiniteLoop } = this.openTable
            if (verticalOrderLayout) {
              //优先判断是否开启了垂直导航(有可能infiniteLoop/verticalOrderLayout同时两个都开启了)
              this.$refs.verticalOrderLayout.intoScroll(code)
            } else if (infiniteLoop) {
              this.$refs.contentScrollContainer.removeEventListener(
                "scroll",
                this.infiniteLoopScroll
              )
              const targetElement = document.querySelector(`div[data-code="${code}"]`)
              if (targetElement) {
                // 立即滚动到目标元素
                targetElement.scrollIntoView({ block: "start" })
                // 设置一个延时，然后重新添加滚动监听器
                setTimeout(() => {
                  this.$refs.contentScrollContainer.addEventListener(
                    "scroll",
                    this.infiniteLoopScroll,
                    {
                      passive: true
                    }
                  )
                }, 100) // 100毫秒延时，根据需要调整
              }
            }
          },

          //锚点跳转
          // anchorJump(code) {
          //   //取消监听滚动解决锚点跳转动画问题
          //   this.$refs.contentScrollContainer.removeEventListener("scroll", this.infiniteLoopScroll)
          //   const targetElement = document.querySelector(`div[data-code="${code}"]`)
          //   if (targetElement instanceof HTMLElement) {
          //     targetElement.scrollIntoView({ block: "start" })
          //     setTimeout(() => {
          //       this.$refs.contentScrollContainer.addEventListener(
          //         "scroll",
          //         this.infiniteLoopScroll,
          //         {
          //           passive: true
          //         }
          //       )
          //     }, 100) // 100毫秒延时，根据需要调整
          //   }
          // },

          error4003TimeStr(ms) {
            // 使用 moment 将毫秒数转换为一个持续时间对象
            let { hoursMsg, minutesMsg, secondsMsg } = this.systemLanguage
            const duration = moment.duration(ms)
            // 获取小时、分钟和秒
            const hours = duration.hours()
            const minutes = duration.minutes()
            const seconds = duration.seconds()
            // 根据小时是否为 0 来决定返回的格式
            if (hours > 0) {
              return `${hours} ${hoursMsg} ${minutes} ${minutesMsg} ${seconds} ${secondsMsg}`
            } else {
              return `${minutes} ${minutesMsg} ${seconds} ${secondsMsg}`
            }
          },
          matchMtycode(code) {
            let len = this.openTable.mTypeCodeLength || 2
            return code.toString().slice(0, len)
          },
          error4016Str(foodName, maxNum) {
            let { maxFOrderTip } = this.systemLanguage
            return maxFOrderTip.replace("#foodName", foodName).replace("#maxNum", maxNum)
          },
          //判断主food是否达到最大数量禁止添加
          checkMaxFoodNum(
            item,
            preAddNum = 0, //预增加数量
            source = "normal", //来源
            hasShowTip = true, //是否显示弹窗提示
            allowFilter = true, //是否允许过滤自身数据(用于编辑购物车跳转详情页面过渡时,不因为数据变化导致按钮样式闪变样式)
            targetBtn = null //事件触发目标按钮
          ) {
            let { fCode, ftCode, maxSingleOrderCount, maxSingleQuantityToCart, qty1 } = item
            let { maxAddCartOrderTip } = this.systemLanguage

            if (this.checkItemCtrlQtyCount(item, preAddNum, source, hasShowTip)) return true

            if (source == "cart") {
              //为了保持数据一致性,更新缓存的maxSingleOrderCount
              let fty = this.allDataList.find(e => e.code == ftCode)
              if (fty) {
                let food = fty.foodList.find(e => e.fCode == fCode)
                if (food) {
                  maxSingleOrderCount = food.maxSingleOrderCount
                  maxSingleQuantityToCart = food.maxSingleQuantityToCart
                }
              }
            }

            //判断主food是否达到最大数量禁止添加
            if (typeof maxSingleOrderCount === "number" && !isNaN(maxSingleOrderCount)) {
              let name = this.inListTitle(item)
              //在购物车中添加则自身不加入基础数量
              let foodNum = source == "cart" ? preAddNum : qty1 + preAddNum
              let allCartData = this.shopCartList
              if (this.joinType == "edit" && allowFilter) {
                //编辑购物车状态下过滤掉自身数据再计算
                allCartData = this.shopCartList.filter((o, i) => i !== this.clickCartEditIndex)
              }
              allCartData.forEach(e => {
                if (e.fCode === fCode) {
                  foodNum += e.qty1
                }
              })
              if (foodNum > maxSingleOrderCount) {
                if (hasShowTip) this.layerDia(this.error4016Str(name, maxSingleOrderCount))
                return true
              }
            }

            //判断主food是否达到加入购物车最大数量禁止添加
            if (
              typeof maxSingleQuantityToCart === "number" &&
              !isNaN(maxSingleQuantityToCart) &&
              targetBtn != "joinCartBtn"
            ) {
              let curQty = qty1 + preAddNum
              if (curQty > maxSingleQuantityToCart) {
                if (hasShowTip) {
                  let name = this.inListTitle(item)
                  let strTip = maxAddCartOrderTip.replace("#foodName", name)
                  this.layerDia(strTip)
                }
                return true
              }
            }

            return false
          },
          languageLabel(label) {
            return this.systemLanguage[label] || label
          },
          walletLogined() {
            return new Promise(async resolve => {
              if (!this.LoginValid()) {
                await this.showUserPopup()
                if (!this.LoginValid()) {
                  resolve(false)
                }
              }
              resolve(true)
            })
          },
          //禁用钱包支付
          checkDisabledWallet() {
            return new Promise((resolve, reject) => {
              let memberInfo = Cookies.get("memberInfo")
              if (!memberInfo) return false
              let { balance = 0 } = JSON.parse(memberInfo)
              this.isDisabledWallet = balance < this.finalPrice
              // console.log(balance, this.finalPrice, 999)
              if (this.choosePayMethod == "wallet" && this.isDisabledWallet) {
                this.chooseOtherPayMethod()
              }
              resolve(true)
            })
          },
          getPaymentGatewaySuffix(payMethod) {
            let { walletBalanceNotEnough } = this.systemLanguage
            const suffixMap = {
              wallet: this.isDisabledWallet ? walletBalanceNotEnough : ""
            }

            return suffixMap[payMethod] || ""
          },
          //初始设置支付方式
          initSetPayMethod() {
            let firstPayMethod = this.getAllPayMethod[0]
            this.choosePayMethod = firstPayMethod //默认第一个支付方式
          },
          //禁用当前支付方式,顺延向上
          chooseOtherPayMethod() {
            let index = this.getAllPayMethod.findIndex(e => e == this.choosePayMethod)
            let prev = this.getAllPayMethod[index - 1]
            let next = this.getAllPayMethod[index + 1]
            if (next) {
              this.choosePayMethod = next
            } else if (prev) {
              this.choosePayMethod = prev
            }
          },
          //处理最大下单限制数量
          setMaxOrderLimit(pax) {
            pax = pax || 1
            if ("numberDishes" in this.openTable && this.openTable.numberDishes["number"]) {
              let { multiplyByPax = false, number } = this.openTable.numberDishes
              this.numberDishes = multiplyByPax ? number * pax : number
            } else {
              this.isShowNumberDishes = false
            }
          },
          mountedAboutTimePicker() {
            let {
              language,
              pickupTime = {},
              tableNumber,
              performType,
              storeData,
              isEnableTimeSimulation
            } = this.openTable

            let isTakeAwayInit =
              this.isTakeAway &&
              performType == 2 &&
              storeData &&
              pickupTime.pickupTimeInterval &&
              pickupTime.pickupDayRange

            let isTestInit = isEnableTimeSimulation && this.isTestTable

            if (isTestInit && !storeData) {
              this.$set(this.openTable, "storeData", {
                pickupTime: "",
                hourStrAndMinute: "",
                checkDate: {}
              })
            } //手动补全storeData跟随外卖时间组件逻辑

            if (isTakeAwayInit || isTestInit) {
              this.initTimePicker(language, pickupTime) // 初始化时间选择器
            }
          }
        }
      })
    </script>
  </body>
</html>
