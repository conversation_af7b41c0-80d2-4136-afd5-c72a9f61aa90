<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <script src="../../static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <script src="../../static/vue/vue.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../static/elementUI/index.css" />
    <!-- 引入组件库 -->
    <script src="../../static/elementUI/index.js"></script>
    <script src="../../static/elementUI/locale/en.js"></script>

    <link rel="stylesheet" href="../../static/cmsUtils/style/CMSCommonStyle.css" />
    <!-- <script src="../../static/plugins/tinymce/tinymce.min.js"></script> -->
    <script src="../../static/js/page/uiConfigRulesMixin.js"></script>
    <script>
      ELEMENT.locale(ELEMENT.lang.en)
    </script>
    <style>
      .dialog_footer {
        text-align: right;
        padding-top: 15px;
      }

      .el-form {
        max-height: 60vh;
        overflow: auto;
        overflow-x: hidden;
      }
      .el-dialog .el-dialog__body {
        max-height: 75vh;
      }
      .el-popover {
        max-width: 500px;
        overflow-y: auto;
      }

      .countTypeRadio {
        padding: 6px 0;
        /* width: 50%; */
        margin-right: 30px;
      }
      .select-option-prefix {
        display: inline-block;
        background-color: #cccccc;
        border-radius: 20px;
        width: 75px;
        font-size: 14px;
        color: #f4f4f4;
        text-align: center;
        line-height: initial;
      }
      .fontSize-checkBox-group {
        display: flex;
        /* justify-content: end; */
        flex-wrap: wrap;
      }
      .fontSize-checkBox {
        display: flex;
        align-items: center;
        margin-right: 0;
        justify-content: center;
        margin-bottom: 20px;
      }
      .fontSizeForm {
        display: flex;
      }
      .fontSizeForm .el-form-item {
        margin-right: 20px;
      }
      .fontSizeForm .el-form-item:last-child {
        margin-right: 0;
      }
      .size-header {
        display: flex;
        /* margin-left: -145px; 抵消el-form-item默认的margin-left:200px */
        /* justify-content: end; */
      }

      .size-header > div {
        text-align: center;
        margin-left: 20px;
        margin-bottom: 10px;
        flex-shrink: 0;
      }

      .size-header > div:nth-child(1) {
        width: 100px;
      }

      .size-header > div:nth-child(2) {
        width: 140px;
      }

      .size-header > div:nth-child(3) {
        width: 140px;
      }

      .size-header > div:nth-child(4) {
        width: 140px;
      }

      /* .fontSize-checkBox-group的兄弟元素 .el-form-item__error */
      .fontSize-checkBox-group ~ .el-form-item__error {
        right: 480px;
        left: unset;
        white-space: nowrap;
      }

      /* .size-select */
      .size-select > .el-input > .el-input__suffix span.el-input__suffix-inner {
        pointer-events: none !important;
      }

      .fontSize-checkBox .pageFontSize-elForm-item .el-form-item__content {
        margin-left: 0 !important;
      }

      /* .tox-statusbar {
        display: none !important;
      } */
      .no-margin-left > .el-form-item__content {
        margin-left: unset !important;
      }
      .memberExpRadio span,
      .pointExpRadio span,
      .smtpRadio span {
        display: inline-block;
      }
      .form-alert-dom > .el-form-item__content {
        line-height: unset !important;
      }

      .form-alert-dom .el-alert__icon {
        font-size: 25px !important;
        width: 25px !important;
      }
      .form-alert-dom-alert .el-alert__content .el-alert__title {
        font-size: 15px !important;
        line-height: unset !important;
        word-break: break-word !important;
      }

      /* pos价格符号组件 */
      .posPrice-container .el-form {
        padding-bottom: 10px;
        max-height: unset;
      }
      .posPrice-form-item {
        display: flex;
        align-items: center;
        margin-bottom: 22px;
        position: relative;
      }

      .posPrice-form-content {
        flex: 1;
        display: flex;
        gap: 15px;
        line-height: 0px !important;
      }
      .posPrice-form-item .el-form-item {
        margin-bottom: 0;
        flex: 1;
      }
      .posPrice-delete-btn {
        margin-left: 10px;
      }

      .posPrice-add-btn {
        margin-left: 10px;
      }

      @media (max-width: 1600px) {
        .el-dialog {
          width: 83%;
        }
      }
    </style>
  </head>

  <body>
    <div id="app" ref="tableBox">
      <template>
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          :header-cell-style="{fontSize: '15px'}"
          empty-text="No Data"
          :max-height="tableHeight"
        >
          <el-table-column prop="storeNumber" label="Store Number" align="center" width="180">
            <template slot-scope="scope">{{scope.row.storeNumber}}</template>
          </el-table-column>
          <el-table-column
            prop="type"
            label="Type"
            align="center"
            width="250"
            show-overflow-tooltip
          >
            <template slot-scope="scope">{{scope.row.type|showUpperCaseType}}</template>
          </el-table-column>

          <el-table-column label="Switch/Currency" align="center">
            <template v-slot="scope">
              <!-- 货币符号 -->
              <div v-if="scope.row.type=='currency'">{{translCurrency[scope.row.value]}}</div>
              <div v-else-if="strConfig.includes(scope.row.type)">{{scope.row.value}}</div>
              <div
                v-else-if="objConfig.includes(scope.row.type)"
                v-html="showParseObj(scope.row.value)"
              ></div>
              <div v-else-if="scope.row.type=='page'">
                {{scope.row.value==1?'Old Page':'New Page'}}
              </div>

              <div v-else-if="scope.row.type=='keepDecimals'">
                {{ scope.row.value | showKeepDecimals }}
                <el-switch
                  style="padding: 0 0 2px 5px"
                  v-model="parseKeepDecimals(scope.row.value).zeroPadding"
                  :active-value="true"
                  :inactive-value="false"
                  @change="onEditSwitch($event, scope.row,true)"
                ></el-switch>
              </div>
              <div v-else-if="scope.row.type=='pickupTime'">
                <span>
                  Pickup Day Range : {{scope.row.value |showPickupTime('pickupDayRange')}} days
                </span>
                <br />
                <span>
                  Pickup Time Interval : {{scope.row.value |showPickupTime('pickupTimeInterval')}}
                  minute
                </span>
                <br />
                <span>
                  Advance Order Minutes : {{scope.row.value |showPickupTime('advanceOrderMinutes')}}
                  minute
                </span>
                <br />
                <span>
                  AdvancePickup :
                  <el-switch
                    v-model="scope.row.switchVal"
                    @change="onEditSwitch($event, scope.row)"
                  ></el-switch>
                </span>
              </div>

              <div v-else-if="scope.row.type=='pageFontSize'">
                <div>Default : {{scope.row.value['default']||"normal"}}</div>
                <div v-for="(typeVal,o) in scope.row.value.fontSizeType" :key="o">
                  {{ scope.row.value | showPageFontSize(typeVal)}}
                </div>
              </div>
              <!-- 开关 -->
              <div v-else>
                <!-- 特殊类型数据(开关/三语言等等显示) -->
                <div v-if="irregularityConfig.includes(scope.row.type)">
                  <el-popover placement="top-start" width="600" trigger="hover">
                    <div v-html="showIrregularityConfig(scope.row)"></div>
                    <!-- 特殊类型-开关 -->
                    <template v-for="(val,key) in showIrregularityConfig(scope.row,'boolean')">
                      <div>
                        <span>{{key}}:</span>
                        <el-switch
                          v-model="scope.row.value[key]"
                          :active-value="true"
                          :inactive-value="false"
                          @change="onEditSwitch($event, scope.row)"
                        ></el-switch>
                      </div>
                    </template>
                    <div slot="reference">display on hover</div>
                  </el-popover>
                </div>
                <el-switch
                  v-if="!unShowSwitch(scope.row.type)"
                  v-model="scope.row.switchVal"
                  :active-value="true"
                  :inactive-value="false"
                  @change="onEditSwitch($event, scope.row)"
                ></el-switch>
              </div>
            </template>
          </el-table-column>
          <!--  -->
          <el-table-column prop label="Operation" align="center">
            <el-table-column width="110" align="center">
              <template slot="header">
                <el-button
                  type="text"
                  size="mini"
                  align="center"
                  style="font-size: 15px"
                  @click="addDialogVisible=true"
                >
                  <i class="el-icon-plus"></i>
                  Add
                </el-button>
              </template>
              <template slot-scope="scope">
                <!-- 除按钮类型外显示编辑按钮 -->
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  v-if="!switchValType.includes(scope.row.type)"
                  @click="onEdit(scope.$index, scope.row)"
                ></el-button>
                <el-button
                  size="small"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="onDel(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </template>
      <!-- 類別Add弹窗 -->
      <template>
        <el-dialog
          title="Add Dialog"
          :visible.sync="addDialogVisible"
          @close="onDiaCloseDialog(addForm,'add')"
        >
          <el-form :model="addForm" ref="addForm" label-width="auto" :rules="rules">
            <el-form-item label="Store Number" prop="storeNumber">
              <el-input
                v-model="addForm.storeNumber"
                placeholder="Please enter the store number"
                :disabled="fixStoreNumber.includes(addForm.type)"
              ></el-input>
            </el-form-item>
            <el-form-item label="Type" prop="type">
              <el-select
                v-model="addForm.type"
                placeholder="Please select type"
                @change="clearVerify"
                style="width: 100%"
                filterable
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="item in typeOption"
                  :key="item.value"
                >
                  <span
                    class="select-option-prefix"
                    v-if="item.prefix"
                    :style="[{backgroundColor:prefixOption[item.prefix]}]"
                    style="margin-right: 5px"
                  >
                    {{item.prefix}}
                  </span>
                  {{item.label}}
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              :label="switchLabel[addForm.type]||'Enable'"
              v-if="switchValType.includes(addForm.type)"
            >
              <el-switch v-model="addForm.switchVal"></el-switch>
            </el-form-item>
            <template v-for="(type,y) in typeOption" :key="type.value">
              <div v-if="type.value==addForm.type">
                <template v-for="(element,i) in type.formElement" :key="i">
                  <!-- 输入框 -->
                  <el-form-item
                    v-if="element.elementType=='input'&&comShowCondition(element.showConditions)"
                    :label="element.label"
                    :prop="!app[element.disable]&&element.prop||'empty'"
                  >
                    <!-- 嵌套数据 -->
                    <el-input
                      v-if="checkNestingData(type.value)"
                      v-model="addForm[type.value][element.model]"
                      :placeholder="element.placeholder"
                      :oninput="element.oninput"
                      @blur="onBlur('addForm',type.value,element.model,element.valueType)"
                      clearable
                      :disabled="app[element.disable]"
                    >
                      <template v-if="element.slotEnd" slot="append">{{element.slotEnd}}</template>
                    </el-input>
                    <!-- 单层数据 -->
                    <el-input
                      v-else
                      v-model="getSingleLayerData"
                      :placeholder="element.placeholder"
                      :oninput="element.oninput"
                      @blur="onBlur('addForm',type.value,null,element.valueType)"
                      clearable
                      :disabled="app[element.disable]"
                    >
                      <template v-if="element.slotEnd" slot="append">{{element.slotEnd}}</template>
                    </el-input>
                  </el-form-item>
                  <!-- 文本框 -->
                  <el-form-item
                    v-if="element.elementType=='textarea'&&comShowCondition(element.showConditions)"
                    :label="element.label"
                    :prop="element.prop"
                  >
                    <el-input
                      type="textarea"
                      :rows="3"
                      v-if="element.level==2"
                      v-model="addForm[type.value][element.model1][element.model2]"
                      :placeholder="element.placeholder"
                    ></el-input>
                    <el-input
                      type="textarea"
                      :rows="3"
                      v-model="addForm[type.value][element.model]"
                      v-else-if="checkNestingData(type.value)"
                      :placeholder="element.placeholder"
                    ></el-input>
                    <el-input
                      type="textarea"
                      :rows="3"
                      v-else
                      v-model="getSingleLayerData"
                      :placeholder="element.placeholder"
                    ></el-input>
                  </el-form-item>
                  <!--el-tooltip 包裹的 input  -->
                  <el-tooltip
                    v-if="element.elementType=='tooltip'"
                    :value="isShowTooltip(element.inputModel)"
                    :content="element.content"
                    :placement="element.placement"
                    :manual="element.manual"
                    :hide-after="element.hideAfter"
                    :effect="element.effect"
                  >
                    <el-form-item :label="element.label">
                      <el-input
                        v-model="addForm[type.value][element.inputModel]"
                        :placeholder="element.placeholder"
                        clearable
                      ></el-input>
                    </el-form-item>
                  </el-tooltip>

                  <!-- 计数器 -->
                  <el-form-item
                    v-if="element.elementType=='inputNumber'"
                    :label="element.label"
                    :prop="element.prop"
                  >
                    <!-- 双层数据计数器 -->
                    <el-input-number
                      v-if="checkNestingData(type.value)"
                      :min="element.min"
                      :max="element.max"
                      v-model="addForm[type.value][element.model]"
                      :precision="element.precision"
                      clearable
                    ></el-input-number>
                    <!-- 单层数据计数器 -->
                    <el-input-number
                      v-else
                      :min="element.min"
                      :max="element.max"
                      v-model="getSingleLayerData"
                      :precision="element.precision"
                      clearable
                    ></el-input-number>
                    <span v-if="element.suffixName">{{element.suffixName}}</span>
                  </el-form-item>
                  <!-- 单选单层model结构 -->
                  <el-form-item
                    v-if="element.elementType==='radio'&&type.nest=='monolayer'"
                    :label="element.label"
                    :prop="element.prop"
                  >
                    <el-radio-group
                      v-model="addForm[element.model]"
                      @input="(e)=>handleRadioChange(e,null,element.model)"
                    >
                      <el-radio
                        :class="element.className"
                        :label="radioItem.value"
                        :key="radioItem.value"
                        v-for="radioItem in element.radioOptions"
                      >
                        {{radioItem.label}}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <!-- 单选多层model结构 -->
                  <el-form-item
                    v-if="element.elementType=='radio'&&type.nest!='monolayer'"
                    :label="element.label"
                    :prop="element.prop"
                  >
                    <el-radio-group
                      v-model="addForm[type.value][element.model]"
                      @input="(e)=>handleRadioChange(e,type.value,element.model)"
                    >
                      <el-radio
                        :class="element.className"
                        :label="radioItem.value"
                        :key="radioItem.value"
                        v-for="radioItem in element.radioOptions"
                      >
                        {{radioItem.label}}
                        <el-form-item
                          v-if="radioItem.radioElement=='datePicker'"
                          :prop="radioItem.prop"
                        >
                          <el-date-picker
                            v-model="addForm[type.value][radioItem.model]"
                            type="date"
                            @focus="e=>handleRadioDatePickerFocus(e,type.value,element.model,radioItem.value)"
                            @change="handleRadioFormItemChange(type.value,element.model)"
                            value-format="yyyy-MM-dd"
                            placeholder="Select date"
                          ></el-date-picker>
                        </el-form-item>
                        <el-form-item v-if="radioItem.radioElement=='input'" :prop="radioItem.prop">
                          <el-input
                            @focus="e=>handleRadioInputFocus(e,type.value,element.model,radioItem.value)"
                            @blur="e=>handleRadioInputBlur(e,type.value,element.model,radioItem.value)"
                            @input="handleRadioFormItemChange(type.value,element.model)"
                            v-model="addForm[type.value][radioItem.model]"
                            :placeholder="radioItem.placeholder"
                          >
                            <template v-if="radioItem.slotEnd" slot="append">
                              {{radioItem.slotEnd}}
                            </template>
                          </el-input>
                        </el-form-item>
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <!-- 开关 -->
                  <el-form-item
                    v-if="element.elementType=='switch'&&comShowCondition(element.showConditions)"
                    :label="element.label"
                  >
                    <el-switch v-model="addForm[type.value][element.model]"></el-switch>
                  </el-form-item>
                  <!-- 主开关 -->
                  <el-form-item v-if="element.elementType=='mainSwitch'" :label="element.label">
                    <el-switch v-model="addForm[element.model]"></el-switch>
                  </el-form-item>
                  <!-- 下拉选择单层 -->
                  <el-form-item
                    v-if="element.elementType=='select'"
                    :label="getLabelOrModel(type, element)"
                    :prop="getPropOrValue(type, element)"
                  >
                    <el-select
                      :value="getFormValue(type, element)"
                      @input="setFormValue(type, element, $event)"
                      :placeholder="element.placeholder"
                      style="width: 100%"
                      filterable
                    >
                      <el-option
                        :label="item.label"
                        :value="item.value"
                        v-for="item in generateSelectList(element.elementOption)"
                        :key="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <!-- 级联选择器 -->
                  <el-form-item
                    v-if="element.elementType=='cascader'"
                    :label="element.label"
                    :prop="element.prop"
                  >
                    <el-cascader
                      v-model="addForm[type.value][element.model]"
                      :options="element.options"
                      :props="element.props"
                      :style="{width: element.width||'100%'}"
                      @change="(value) => cascaderHandleChange(element.change, value)"
                      :placeholder="element.placeholder"
                    ></el-cascader>
                  </el-form-item>

                  <!-- 多选框 -->
                  <el-form-item
                    v-if="element.elementType=='checkbox'"
                    :label="element.label"
                    :prop="element.prop"
                    class="pageFontSize-elForm-item"
                  >
                    <template v-if="addForm.type=='pageFontSize'">
                      <div class="size-header">
                        <div>Font Size</div>
                        <div>First Lan</div>
                        <div>Second Lan</div>
                        <div>Third Lan</div>
                      </div>
                      <el-checkbox-group
                        v-model="addForm[type.value][element.model]"
                        :class="element.elementGroupClass"
                      >
                        <el-checkbox
                          :label="chkItem.value"
                          :key="chkItem.value"
                          v-for="chkItem in element.elementOption"
                          @change="handleCheckboxChange(chkItem.value,'addForm',type.value)"
                          :class="element.elementChkClass"
                        >
                          <!-- 单独写pageFontSize布局 -->
                          <div class="fontSizeForm" @click="(event)=>{event.preventDefault()}">
                            <!-- label="Font Size" -->
                            <el-form-item>
                              <el-select
                                v-model="addForm.pageFontSize[chkItem.value].sizeType"
                                disabled
                                style="width: 100px"
                                class="size-select"
                              >
                                <el-option
                                  v-for="(size, sizeIndex) in element.elementOption"
                                  :key="size.value"
                                  :label="size.label"
                                  :value="size.value"
                                ></el-option>
                              </el-select>
                            </el-form-item>
                            <!-- label="First Lang" -->
                            <el-form-item
                              :prop="'pageFontSize.'+chkItem.value+'.firstLan'"
                              :rules="[{ required: addForm.pageFontSize.fontSizeType.includes(chkItem.value), message: 'Insert first lang', trigger:'blur' }]"
                            >
                              <el-input
                                v-model="addForm.pageFontSize[chkItem.value].firstLan"
                                placeholder="Insert first lang"
                                :disabled="!addForm.pageFontSize.fontSizeType.includes(chkItem.value)"
                                style="width: 140px"
                              ></el-input>
                            </el-form-item>
                            <!-- label="Second Lang" -->
                            <el-form-item
                              :prop="'pageFontSize.'+chkItem.value+'.secondLan'"
                              :rules="[{required: addForm.pageFontSize.fontSizeType.includes(chkItem.value),message: 'Insert second lang',trigger: 'blur'}]"
                            >
                              <el-input
                                v-model="addForm.pageFontSize[chkItem.value].secondLan"
                                placeholder="Insert second lang"
                                :disabled="!addForm.pageFontSize.fontSizeType.includes(chkItem.value)"
                                style="width: 140px"
                              ></el-input>
                            </el-form-item>
                            <!-- label="Third Lang" -->
                            <el-form-item
                              :prop="'pageFontSize.'+chkItem.value+'.thirdLan'"
                              :rules="[{required: addForm.pageFontSize.fontSizeType.includes(chkItem.value),message: 'Insert third lang',trigger: 'blur'}]"
                            >
                              <el-input
                                v-model="addForm.pageFontSize[chkItem.value].thirdLan"
                                placeholder="Insert third lang"
                                :disabled="!addForm.pageFontSize.fontSizeType.includes(chkItem.value)"
                                style="width: 140px"
                              ></el-input>
                            </el-form-item>
                          </div>
                        </el-checkbox>
                      </el-checkbox-group>
                    </template>
                    <template v-else>
                      <el-checkbox-group
                        v-model="addForm[type.value][element.model]"
                        :class="element.elementGroupClass"
                      >
                        <el-checkbox
                          :label="chkItem.value"
                          :key="chkItem.value"
                          v-for="chkItem in element.elementOption"
                          @change="handleCheckboxChange(chkItem.value,'addForm',type.value)"
                          :class="element.elementChkClass"
                          :disabled="chkItem.disabled"
                        >
                          {{chkItem.label}}
                        </el-checkbox>
                      </el-checkbox-group>
                    </template>
                  </el-form-item>
                  <!-- 富文本 -->
                  <!-- <el-form-item
                    v-if="element.elementType=='richText'"
                    class="dia-form-item-prompt"
                    :label="element.label"
                    :prop="element.prop"
                  >
                    <textarea :id="element.richTextId+'-addForm'"></textarea>
                  </el-form-item> -->
                  <!-- 警告框 -->
                  <el-form-item
                    v-if="element.elementType==='alertTip'"
                    label=""
                    class="form-alert-dom"
                  >
                    <el-alert
                      class="form-alert-dom-alert"
                      :type="element.type"
                      :effect="element.effect"
                      :title="element.title"
                      :show-icon="element.showIcon"
                      :closable="element.closable"
                    ></el-alert>
                  </el-form-item>
                  <el-form-item
                    v-if="element.elementType==='component'"
                    :class="{'no-margin-left': element.fullRow}"
                  >
                    <component
                      :ref="element.name"
                      :is="element.name"
                      v-bind="element.addProps"
                    ></component>
                  </el-form-item>
                </template>
              </div>
            </template>
          </el-form>
          <!-- 底部确认/取消按钮 -->
          <div class="dialog_footer">
            <el-button type="primary" @click="addDialogVisible=false">Cancel</el-button>
            <el-button @click="onAdd('addForm')">Add</el-button>
          </div>
        </el-dialog>
      </template>

      <!--  -->
      <!-- 单元格编辑弹窗 -->
      <template>
        <el-dialog
          title="Edit Dialog"
          :visible.sync="editDialogVisible"
          @close="onDiaCloseDialog(editForm,'edit')"
        >
          <el-form :model="editForm" ref="editForm" label-width="auto" :rules="rules">
            <el-form-item label="Store Number" prop="storeNumber">
              <el-input
                v-model="editForm.storeNumber"
                placeholder="Please enter the store number"
                :disabled="fixStoreNumber.includes(editForm.type)"
              ></el-input>
            </el-form-item>
            <el-form-item label="Type">
              <el-select disabled v-model="editForm.type" style="width: 100%" filterable>
                <el-option
                  v-for="item in typeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              :label="switchLabel[editForm.type]||'Enable'"
              v-if="switchValType.includes(editForm.type)"
            >
              <el-switch v-model="editForm.switchVal"></el-switch>
            </el-form-item>
            <template v-for="(type,y) in typeOption" :key="type.value">
              <div v-if="type.value==editForm.type">
                <template v-for="(element,i) in type.formElement" :key="i">
                  <!-- 输入框 -->
                  <el-form-item
                    v-if="element.elementType=='input'&&comShowCondition(element.showConditions)"
                    :label="element.label"
                    :prop="!app[element.disable]&&element.prop||'empty'"
                  >
                    <!-- 嵌套数据 -->
                    <el-input
                      v-if="checkNestingData(type.value)"
                      v-model="editForm[type.value][element.model]"
                      :placeholder="element.placeholder"
                      :oninput="element.oninput"
                      @blur="onBlur('editForm',type.value,element.model,element.valueType)"
                      clearable
                      :disabled="app[element.disable]"
                    >
                      <template v-if="element.slotEnd" slot="append">{{element.slotEnd}}</template>
                    </el-input>
                    <!-- 单层数据 -->
                    <el-input
                      v-else
                      v-model="getSingleLayerData"
                      :placeholder="element.placeholder"
                      :oninput="element.oninput"
                      @blur="onBlur('editForm',type.value,null,element.valueType)"
                      clearable
                      :disabled="app[element.disable]"
                    >
                      <template v-if="element.slotEnd" slot="append">{{element.slotEnd}}</template>
                    </el-input>
                  </el-form-item>
                  <!-- 文本框 -->
                  <el-form-item
                    v-if="element.elementType=='textarea'&&comShowCondition(element.showConditions)"
                    :prop="element.prop"
                    :label="element.label"
                  >
                    <el-input
                      type="textarea"
                      :rows="3"
                      v-if="element.level==2"
                      v-model="editForm[type.value][element.model1][element.model2]"
                      :placeholder="element.placeholder"
                    ></el-input>
                    <el-input
                      type="textarea"
                      :rows="3"
                      v-else-if="checkNestingData(type.value)"
                      v-model="editForm[type.value][element.model]"
                      :placeholder="element.placeholder"
                    ></el-input>
                    <el-input
                      type="textarea"
                      :rows="3"
                      v-else
                      v-model="getSingleLayerData"
                      :placeholder="element.placeholder"
                    ></el-input>
                  </el-form-item>
                  <!--el-tooltip 包裹的 input  -->
                  <el-tooltip
                    v-if="element.elementType=='tooltip'"
                    :value="isShowTooltip(element.inputModel)"
                    :content="element.content"
                    :placement="element.placement"
                    :manual="element.manual"
                    :hide-after="element.hideAfter"
                    :effect="element.effect"
                  >
                    <el-form-item :label="element.label">
                      <el-input
                        v-model="editForm[type.value][element.inputModel]"
                        :placeholder="element.placeholder"
                        clearable
                      ></el-input>
                    </el-form-item>
                  </el-tooltip>
                  <!-- 计数器 -->
                  <el-form-item
                    v-if="element.elementType=='inputNumber'"
                    :label="element.label"
                    :prop="element.prop"
                  >
                    <!-- 双层数据计数器 -->
                    <el-input-number
                      v-if="checkNestingData(type.value)"
                      :min="element.min"
                      :max="element.max"
                      v-model="editForm[type.value][element.model]"
                      :precision="element.precision"
                      clearable
                    ></el-input-number>
                    <!-- 单层数据计数器 -->
                    <el-input-number
                      v-else
                      :min="element.min"
                      :max="element.max"
                      v-model="getSingleLayerData"
                      :precision="element.precision"
                      clearable
                    ></el-input-number>
                    <span v-if="element.suffixName">{{element.suffixName}}</span>
                  </el-form-item>
                  <!-- 单选单层model结构 -->
                  <el-form-item
                    v-if="element.elementType==='radio'&&type.nest=='monolayer'"
                    :label="element.label"
                    :prop="element.prop"
                  >
                    <el-radio-group
                      v-model="editForm[element.model]"
                      @input="(e)=>handleRadioChange(e,null,element.model)"
                    >
                      <el-radio
                        :class="element.className"
                        :label="radioItem.value"
                        :key="radioItem.value"
                        v-for="radioItem in element.radioOptions"
                      >
                        {{radioItem.label}}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <!-- 单选多层model结构 -->
                  <el-form-item
                    v-if="element.elementType==='radio'&&type.nest!='monolayer'"
                    :label="element.label"
                    :prop="element.prop"
                  >
                    <el-radio-group
                      v-model="editForm[type.value][element.model]"
                      @input="(e)=>handleRadioChange(e,type.value,element.model)"
                    >
                      <el-radio
                        :class="element.className"
                        :label="radioItem.value"
                        :key="radioItem.value"
                        v-for="radioItem in element.radioOptions"
                      >
                        {{radioItem.label}}
                        <el-form-item
                          v-if="radioItem.radioElement=='datePicker'"
                          :prop="radioItem.prop"
                        >
                          <el-date-picker
                            v-model="editForm[type.value][radioItem.model]"
                            type="date"
                            @focus="e=>handleRadioDatePickerFocus(e,type.value,element.model,radioItem.value)"
                            @change="handleRadioFormItemChange(type.value,element.model)"
                            value-format="yyyy-MM-dd"
                            placeholder="Select date"
                          ></el-date-picker>
                        </el-form-item>
                        <el-form-item v-if="radioItem.radioElement=='input'" :prop="radioItem.prop">
                          <el-input
                            @focus="e=>handleRadioInputFocus(e,type.value,element.model,radioItem.value)"
                            @blur="e=>handleRadioInputBlur(e,type.value,element.model,radioItem.value)"
                            @input="handleRadioFormItemChange(type.value,element.model)"
                            v-model="editForm[type.value][radioItem.model]"
                            :placeholder="radioItem.placeholder"
                          >
                            <template v-if="radioItem.slotEnd" slot="append">
                              {{radioItem.slotEnd}}
                            </template>
                          </el-input>
                        </el-form-item>
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <!-- 开关 -->
                  <el-form-item
                    v-if="element.elementType=='switch'&&comShowCondition(element.showConditions)"
                    :label="element.label"
                  >
                    <el-switch v-model="editForm[type.value][element.model]"></el-switch>
                  </el-form-item>
                  <!-- 主开关 -->
                  <el-form-item v-if="element.elementType=='mainSwitch'" :label="element.label">
                    <el-switch v-model="editForm[element.model]"></el-switch>
                  </el-form-item>
                  <!-- 下拉选择 -->
                  <el-form-item
                    v-if="element.elementType=='select'"
                    :label="getLabelOrModel(type, element)"
                    :prop="getPropOrValue(type, element)"
                  >
                    <el-select
                      :value="getFormValue(type, element)"
                      @input="setFormValue(type, element, $event)"
                      :placeholder="element.placeholder"
                      style="width: 100%"
                      filterable
                    >
                      <el-option
                        :label="item.label"
                        :value="item.value"
                        v-for="item in generateSelectList(element.elementOption)"
                        :key="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <!-- 级联选择器 -->
                  <el-form-item
                    v-if="element.elementType=='cascader'"
                    :label="element.label"
                    :prop="element.prop"
                  >
                    <el-cascader
                      v-model="editForm[type.value][element.model]"
                      :options="element.options"
                      :props="element.props"
                      :style="{width: element.width||'100%'}"
                      @change="(value) => cascaderHandleChange(element.change, value)"
                    ></el-cascader>
                  </el-form-item>
                  <!-- 多选框 -->
                  <el-form-item
                    v-if="element.elementType=='checkbox'"
                    :label="element.label"
                    :prop="element.prop"
                    class="pageFontSize-elForm-item"
                  >
                    <template v-if="editForm.type=='pageFontSize'">
                      <div class="size-header">
                        <div>Font Size</div>
                        <div>First Lan</div>
                        <div>Second Lan</div>
                        <div>Third Lan</div>
                      </div>
                      <el-checkbox-group
                        v-model="editForm[type.value][element.model]"
                        :class="element.elementGroupClass"
                      >
                        <el-checkbox
                          :label="chkItem.value"
                          :key="chkItem.value"
                          v-for="chkItem in element.elementOption"
                          @change="handleCheckboxChange(chkItem.value,'editForm',type.value)"
                          :class="element.elementChkClass"
                        >
                          <!-- 单独写pageFontSize布局 -->
                          <div
                            class="fontSizeForm"
                            v-if="editForm.type=='pageFontSize'"
                            @click.prevent="(event)=>{event.preventDefault()}"
                          >
                            <!-- label="Font Size" -->
                            <el-form-item>
                              <el-select
                                v-model="editForm.pageFontSize[chkItem.value].sizeType"
                                disabled
                                style="width: 100px"
                              >
                                <el-option
                                  v-for="(size, sizeIndex) in element.elementOption"
                                  :key="size.value"
                                  :label="size.label"
                                  :value="size.value"
                                ></el-option>
                              </el-select>
                            </el-form-item>
                            <!-- label="First Lang" -->
                            <el-form-item
                              :prop="'pageFontSize.'+chkItem.value+'.firstLan'"
                              :rules="[{ required: editForm.pageFontSize.fontSizeType.includes(chkItem.value), message: 'Insert first lang', trigger:'blur' }]"
                            >
                              <el-input
                                v-model="editForm.pageFontSize[chkItem.value].firstLan"
                                placeholder="Insert first lang"
                                :disabled="!editForm.pageFontSize.fontSizeType.includes(chkItem.value)"
                                style="width: 140px"
                              ></el-input>
                            </el-form-item>
                            <!-- label="Second Lang" -->
                            <el-form-item
                              :prop="'pageFontSize.'+chkItem.value+'.secondLan'"
                              :rules="[{required: editForm.pageFontSize.fontSizeType.includes(chkItem.value),message: 'Insert second lang',trigger: 'blur'}]"
                            >
                              <el-input
                                v-model="editForm.pageFontSize[chkItem.value].secondLan"
                                placeholder="Insert second lang"
                                :disabled="!editForm.pageFontSize.fontSizeType.includes(chkItem.value)"
                                style="width: 140px"
                              ></el-input>
                            </el-form-item>
                            <!-- label="Third Lang" -->
                            <el-form-item
                              :prop="'pageFontSize.'+chkItem.value+'.thirdLan'"
                              :rules="[{required: editForm.pageFontSize.fontSizeType.includes(chkItem.value),message: 'Insert third lang',trigger: 'blur'}]"
                            >
                              <el-input
                                v-model="editForm.pageFontSize[chkItem.value].thirdLan"
                                placeholder="Insert third lang"
                                :disabled="!editForm.pageFontSize.fontSizeType.includes(chkItem.value)"
                                style="width: 140px"
                              ></el-input>
                            </el-form-item>
                          </div>
                        </el-checkbox>
                      </el-checkbox-group>
                    </template>
                    <template v-else>
                      <el-checkbox-group
                        v-model="editForm[type.value][element.model]"
                        :class="element.elementGroupClass"
                      >
                        <el-checkbox
                          :label="chkItem.value"
                          :key="chkItem.value"
                          v-for="chkItem in element.elementOption"
                          @change="handleCheckboxChange(chkItem.value,'editForm',type.value)"
                          :class="element.elementChkClass"
                          :disabled="chkItem.disabled"
                        >
                          {{chkItem.label}}
                        </el-checkbox>
                      </el-checkbox-group>
                    </template>
                  </el-form-item>
                  <!-- 警告框 -->
                  <el-form-item
                    v-if="element.elementType==='alertTip'"
                    label=""
                    class="form-alert-dom"
                  >
                    <el-alert
                      class="form-alert-dom-alert"
                      :type="element.type"
                      :effect="element.effect"
                      :title="element.title"
                      :show-icon="element.showIcon"
                      :closable="element.closable"
                    ></el-alert>
                  </el-form-item>
                  <el-form-item
                    v-if="element.elementType==='component'"
                    :class="{'no-margin-left': element.fullRow}"
                  >
                    <component
                      :ref="element.name"
                      :is="element.name"
                      v-bind="element.editProps"
                    ></component>
                  </el-form-item>
                </template>
              </div>
            </template>
          </el-form>

          <div class="dialog_footer">
            <el-button type="primary" @click="editDialogVisible=false">Cancel</el-button>
            <el-button @click="subEdit">Submit</el-button>
          </div>
        </el-dialog>
      </template>
    </div>
    <script src="../../static/cmsUtils/OrderTimeForm.js"></script>
    <script src="../../static/cmsUtils/SalesControlForm.js"></script>
    <script src="../../static/cmsUtils/SelectPosPrice.js"></script>
    <script>
      const validateReqSmtpDiy = (rule, value, callback) => {
        let form = app.addDialogVisible ? app.addForm : app.editForm
        let { smtpRadio, smtpDiy, smtpHost } = form.emailSenderManagement
        if (smtpRadio === "smtpDiy" && (!value || value.trim() === "")) {
          callback(new Error("Please enter the smtp host"))
          return
        }
        callback()
      }
      const validateReqHasCodeReq = (rule, value, callback) => {
        if (value.includes("#code")) {
          return callback()
        } else {
          return callback(
            new Error("Error: The input must contain '#code' for verification code replacement.")
          )
        }
      }
      const validateReqHasCode = (rule, value, callback) => {
        if (!value) {
          return callback()
        } else {
          if (value.includes("#code")) {
            return callback()
          } else {
            return callback(
              new Error("Error: The input must contain '#code' for verification code replacement.")
            )
          }
        }
      }
      const OrderNumber = (rule, value, callback) => {
        let reg = /^[0-9]+$/
        if (!reg.test(value.number)) {
          return callback(new Error("Please enter limit number of dishes per order"))
        } else {
          return callback()
        }
      }
      const validateSmsSenderId = (rule, value, callback) => {
        const pattern = /^(?=.*[a-zA-Z])(?=.{1,11}$)[a-zA-Z0-9]+$|^(?=.*\d)(?=.{1,15}$)\d+$/
        if (!pattern.test(value)) {
          return callback(
            new Error(
              "Only numbers and letters are allowed, with a maximum of 11 digits for alphabetic identifiers and 15 digits for numeric-only identifiers"
            )
          )
        } else {
          return callback()
        }
      }
      const validateSmsMessage = (rule, value, callback) => {
        const pattern = /#code/
        if (!pattern.test(value)) {
          return callback(
            new Error("Error: The input must contain '#code' for verification code replacement.")
          )
        } else {
          return callback()
        }
      }
      const getSMSRegionList = async () => {
        let res = await $.get("../../static/SMSRegionList/index.json")
        console.log("🚀 ~ SMSRegionList ~ res:", res)
        //找到app.typeOption 下aliyunSMS下的regionObj赋值options
        // let aliyunSMS = app.typeOption.find(item => item.value == "aliyunSMS")
        // aliyunSMS.options = res
      }
      var app = new Vue({
        el: "#app",
        mixins: [uiConfigRulesMixin],
        filters: {
          showKeepDecimals(e) {
            if (!e) return
            return e.significantDigits + "  |  "
          },
          showUpperCaseType(val) {
            for (let i = 0; i < app.typeOption.length; i++) {
              const item = app.typeOption[i]
              if (item.value == val) {
                return item.label
              }
            }
          },
          showPickupTime(val, type) {
            return val[type]
          },
          showPageFontSize(val, type) {
            // val.fontSizeType
            // 遍历val.fontSizeType用"|"映射val[type],拼接对象里面的字段,拼接完一个对象换行拼接下一个对象
            let str = ""
            for (const key in val[type]) {
              if (key == "sizeType") {
                // 首字母大写拼接
                str += val[type][key].charAt(0).toUpperCase() + val[type][key].slice(1) + " | "
              } else {
                str += val[type][key] + " | "
              }
            }
            str = str.substring(0, str.lastIndexOf("|"))
            // console.log("🚀 ~ file: ConfigPage.html:829 ~ showPageFontSize ~ str:", val, type, str)
            return str
          }
        },
        computed: {
          disabledOrderKPImmediately() {
            const form = this.addDialogVisible ? "addForm" : "editForm"
            return !!app[form].pickupTime?.advanceOrderMinutes
          },
          disabledAdvanceOrderMinutes() {
            const form = this.addDialogVisible ? "addForm" : "editForm"
            // const keys = ["printsKPImmediately", "todayKPName", "todayKPName2"]
            // return keys.some(it => !!app[form]["pickupTime"]?.[it])
            return !!app[form].pickupTime?.printsKPImmediately
          },
          disabledKPName() {
            const form = this.addDialogVisible ? "addForm" : "editForm"
            return !app[form].pickupTime?.printsKPImmediately
          },
          //e:要解析的参数,
          //f:true解析为Array,false解析为字符串

          getSingleLayerData: {
            // 处理单层嵌套数据
            get() {
              let form = this.addDialogVisible ? this.addForm : this.editForm
              let { type } = form
              return form[type]
            },
            set(val) {
              let form = this.addDialogVisible ? this.addForm : this.editForm
              let { type } = form
              form[type] = val
            }
          },
          /**
           * @description 组件显示判断函数
           * @param {string} val 每个组件中的showConditions判断条件
           * @return {boolean} true:显示 false:隐藏
           *  */
          comShowCondition() {
            return val => {
              if (val) {
                let form = this.addDialogVisible ? this.addForm : this.editForm
                let condition = new Function("form", "return form." + val)(form)
                return condition
              } else {
                return true
              }
            }
          },
          isShowTooltip() {
            return val => {
              let form = this.addDialogVisible ? this.addForm : this.editForm
              let { type } = form
              return form[type]?.[val] == ""
            }
          },
          modelOrLabel: {
            get() {
              return this.checkNestingData(this.type.value) ? this.element.model : this.type.label
            },
            set(newValue) {
              if (this.checkNestingData(this.type.value)) {
                this.element.model = newValue
              } else {
                this.type.label = newValue
              }
            }
          }
        },
        data: {
          dialogWidth: "45%",
          tableHeight: 0,
          showSwitch: false,
          // page 和 currency属于strConfig,但未加入,需要另外处理
          strConfig: [
            "color",
            "pcBackgroundColor",
            "currencyWay",
            "thirdLan",
            "preOrderPINLength",
            "hotSaleNum",
            "loginByStaff", //ipad模式员工填写密码
            "sponsoredLink", //送单后的推广link
            "redirectAfterLogin",
            "saveVersionNumber",
            "orderFoodSorting",
            "mTypeCodeLength",
            "ohqTimeout",
            "forceSalesMode",
            "distanceAlert",
            "delayRefreshCacheTime",
            "ossBackupUrl",
            "diningStyle",
            "dineInStoreNumber",
            "keepPayLogUserInfo"
          ],
          //keepDecimals属于objConfig但未加入,需要另外处理
          objConfig: [
            "address",
            "limitByFcode",
            "shopTipText",
            "shopMapSearchScope",
            "distanceAlert",
            "additionalItemsForTakeaway",
            "combinedNavigation",
            "foodCourt"
          ],
          switchValType: [
            "allowSwitchLanguage",
            "sideDish",
            "ftyPage",
            "price",
            "thumbnail",
            "showTimeOutItems",
            "mergeFood",
            "foodRepeatVerify",
            "mergeFoodFollowsFirst",
            "postOrderPopup",
            "hqModule",
            "promotionOverlay",
            "takeAwayPackaging",
            "promotionDiscount",
            "showTotalPrice",
            "showArrow",
            "nameMandatory",
            "emailMandatory",
            "confirmationPopup",
            "showOutsideAddCartBtn",
            "modifiersAlwaysDispMods",
            "closeTakeawayShopPhoto",
            "clearCartButton",
            "lModPhoto",
            "rollingFoodPicture",
            "rollingFoodBigPicture",
            "groupedFoodsByTypeCode",
            "isEnableTimeSimulation",
            "categoryFoldable",
            "splitMainFoodOnOrder",
            "goHistoryAfterOrder",
            "hideOrdersWithoutOrderNumberInHistoryPage",
            "allowPaxInputInFBTrans",
            "displayCartOrderRecord"
          ],
          irregularityConfig: [
            "StaffMode",
            "logCustomerPhone",
            "fixQRAutoOpenTable",
            "pickupTime",
            "animation",
            "errorEmailConfig",
            "assistMode",
            "foodCourt",
            "checkFBTransBeforePayment",
            "menuPopup",
            "displayCRM",
            "memberConfig",
            "browserRestrictions",
            "infiniteLoop",
            "verticalOrderLayout",
            "orderTimeLimit",
            "salesControl",
            "mapConfig",
            "billTax",
            "aliyunSMS",
            "aliyunSMSGlobe",
            "memberAccountManagement",
            "emailSenderManagement",
            "serviceCharges",
            "sseConnect",
            "priceWay",
            "disableItemCtrlLog"
          ], // 不规则类型(开关/文字都具备)
          // 开关类型的别名(label)
          switchLabel: {
            promotionOverlay: "isOverlay"
          },

          domain: sessionStorage.getItem("domain"),
          addDialogVisible: false,
          editDialogVisible: false,
          addForm: {
            storeNumber: "",
            // type: "pageFontSize",
            type: "",
            switchVal: true,
            currency: "",
            currencyWay: "",
            color: "",
            pcBackgroundColor: "",
            page: "",
            priceWay: {},
            numberDishes: {
              number: "",
              multiplyByPax: false
            },
            address: {
              en: "",
              thirdLan: "",
              zh: ""
            },
            shopTipText: {
              tipFontSize: "normal",
              en: "",
              thirdLan: "",
              zh: ""
            },

            pickupTime: {
              pickupDayRange: "",
              pickupTimeInterval: "",
              advanceOrderMinutes: "",
              printsKPImmediately: "", //立即打印KP(下单塞一个foodCode进)
              todayKPName: "Today",
              todayKPName2: "当日"
            },
            // SHOW WITH FCODE limit数量限制
            limitByFcode: {
              en: "",
              zh: "",
              thirdLan: ""
            },
            // 第三语言
            thirdLan: "",
            preOrderPINLength: "",
            hotSaleNum: 0,
            postOrderPopup: "",
            assistMode: {
              password: "",
              pax: 0,
              exitUsingPassword: true
            },
            loginByStaff: "",
            serviceCharges: {
              openType: "perBill",
              countType: "noChange",
              displayInShoppingCartPage: false
            },

            keepDecimals: {
              significantDigits: 1,
              zeroPadding: true
            },
            ohqTimeout: "",
            hqModule: "",
            shopMapSearchScope: {
              scope: 1
            },
            promotionOverlay: "",
            StaffMode: {
              addFoodCode: "",
              pax: "",
              showInHistory: false,
              password: "", //采用CMS设置的密码(未设置则采用pos设置的密码)
              loginModel: "1" //登录界面1(账号密码)/2(密码)
            },
            logCustomerPhone: {
              phoneRequired: false
            },
            sponsoredLink: "",
            fixQRAutoOpenTable: {
              pax: 0
            },
            redirectAfterLogin: "",
            aliyunSMS: {
              accessKeyEncrypt: "",
              hierarchicalList: [],
              region: "",
              endpointOverride: "",
              signName: "",
              templateCode: "",
              excludeNumber: "",
              validTime: 300,
              daySendMax: 0, //0代表不限制
              countdown: 60 //倒计时
            },
            aliyunSMSGlobe: {
              accessKeyEncrypt: "",
              hierarchicalList: [],
              region: "",
              endpointOverride: "",
              senderId: "",
              message: "",
              excludeNumber: "",
              validTime: 300,
              daySendMax: 0, //0代表不限制
              countdown: 60
            },
            saveVersionNumber: 0,
            animation: {
              page_transition: true,
              page_breathe: true,
              page_zoom: true,
              page_shaking: true
            },
            pageFontSize: {
              fontSizeType: [], //BYOD页面控制字体大小复选框选中的值(i.g. normal/ large/ xLarger)
              default: "normal", //预设字体大小
              normal: {
                sizeType: "normal",
                firstLan: "",
                secondLan: "",
                thirdLan: ""
              },
              large: {
                sizeType: "large",
                firstLan: "",
                secondLan: "",
                thirdLan: ""
              },
              xLarger: {
                sizeType: "xLarger",
                firstLan: "",
                secondLan: "",
                thirdLan: ""
              }
            },
            errorEmailConfig: {
              host: "",
              email: "",
              senderName: "",
              to: "",
              authorizationCode: ""
            },
            orderFoodSorting: "",
            mTypeCodeLength: "2",
            additionalItemsForTakeaway: {
              fixed: "",
              auto: ""
            },
            forceSalesMode: "MD",
            distanceAlert: "",
            delayRefreshCacheTime: "",
            ossBackupUrl: "",
            foodCourt: {
              transactionCode: "",
              column: 1,
              customerCode: "",
              firstBillNumber: "080-000001",
              resetByHourOfDay: 1,
              sort: "",
              timeZone: ""
            },
            checkFBTransBeforePayment: {
              timeOut: 3
            },
            menuPopup: {
              carouselInterval: 3000,
              timeout: 180,
              mode: "Random"
            },
            closeTakeawayShopPhoto: false,
            diningStyle: "",
            showOutsideAddCartBtn: false,
            modifiersAlwaysDispMods: false,
            infiniteLoop: {
              distanceNavHeight: 200
            },
            verticalOrderLayout: {
              distanceNavHeight: 200
            },
            displayCRM: {
              showInfo: [],
              requiredMemtype2: "",
              inviteMemberToLogin: true,
              birthdayReminder: true,
              birthdayPromptEN: "",
              birthdayPromptZH: "",
              birthdayPromptThirdLan: "",
              dineInMemberLoginSwitch: true,
              takeawayMemberLoginSwitch: true
            },
            browserRestrictions: {
              category: []
            },
            memberConfig: {
              en: "",
              zh: "",
              thirdLan: "",
              discType: null,
              pointsShowInLogin: false,
              discountSwitch: true, //控制是否开启堂食折扣优惠,关闭则不计算折扣优惠
              takeawayDiscountSwitch: true //控制是否开启会员折扣优惠
            },
            emailSenderManagement: {
              email: "",
              senderName: "",
              smtpRadio: "smtp.gmail.com",
              smtpDiy: "",
              smtpHost: "", //最终smtpHost
              authorizationCode: "",
              excludeNumber: "",
              validTime: 300 //验证码过期时间
            },
            memberAccountManagement: {
              memtype2: "", //注册的会员账号类型
              verifyContact: ["Email"],
              personalDetails: ["Name", "Gender", "Birthday"],
              registerTitle: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              registerMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              resetPasswordTitle: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              resetPasswordMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              aliyun_registerMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              aliyunGlobe_registerMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              aliyun_resetPasswordMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              aliyunGlobe_resetPasswordMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              // 会员过期日期
              memberExpRadio: "memberExpDay",
              pointExpRadio: "pointExpDay",
              memberExpDay: null,
              memberExpDate: null,
              pointExpDay: null,
              pointExpDate: null
            },

            dineInStoreNumber: "",
            combinedNavigation: {
              tab1: "AllFoodType",
              tab2: "AllFoodType",
              color: null
            },
            rollingFoodPicture: true,
            rollingFoodBigPicture: true,
            groupedFoodsByTypeCode: false,
            orderTimeLimit: [],
            salesControl: {
              countScope: 0, //后端统计订单时间间隔
              maxCount: 0,
              includeFCode: "",
              refreshTime: 0,
              estimatedTime: 0,
              rule: []
            },
            mapConfig: {
              timeout: 5,
              disableMap: false
            },
            billTax: {
              billTaxRate: null, //基础税率
              billTaxIncludeDiscount: false, //是否包含折扣
              billTaxIncludeServiceCharge: false //是否包含服务费
            },
            keepPayLogUserInfo: 0,
            sseConnect: {
              // SSE 长链接
              inactiveTime: 0,
              itemCtrl: true,
              shopCartSync: true
            },
            disableItemCtrlLog: {
              onlyLogSoldOutItemCtrl: false
            }
          },

          retain: {},
          editForm: {
            storeNumber: "",
            type: "",
            switchVal: true,
            currency: "",
            currencyWay: "",
            color: "",
            pcBackgroundColor: "",
            page: "",
            priceWay: {},
            numberDishes: {
              number: "",
              multiplyByPax: false
            },
            address: {
              en: "",
              thirdLan: "",
              zh: ""
            },
            shopTipText: {
              tipFontSize: "normal",
              en: "",
              thirdLan: "",
              zh: ""
            },

            pickupTime: {
              pickupDayRange: "",
              pickupTimeInterval: "",
              advanceOrderMinutes: "",
              printsKPImmediately: "",
              todayKPName: "",
              todayKPName2: ""
            },
            limitByFcode: {
              en: "",
              zh: "",
              thirdLan: ""
            },
            // 第三语言
            thirdLan: "",
            preOrderPINLength: "",
            hotSaleNum: "",
            postOrderPopup: "",
            assistMode: {
              password: "",
              pax: 0,
              exitUsingPassword: true
            },
            loginByStaff: "",
            serviceCharges: {
              openType: "",
              countType: "",
              displayInShoppingCartPage: false
            },

            keepDecimals: {
              significantDigits: 1,
              zeroPadding: true
            },
            hqModule: "",
            shopMapSearchScope: {
              scope: 1
            },
            promotionOverlay: "",
            StaffMode: {
              addFoodCode: "",
              pax: "",
              showInHistory: false,
              password: "", //采用CMS设置的密码(未设置则采用pos设置的密码)
              loginModel: "1" //登录界面1(账号密码)/2(密码)
            },
            logCustomerPhone: {
              phoneRequired: false
            },
            sponsoredLink: "",
            fixQRAutoOpenTable: {
              pax: ""
            },
            ohqTimeout: "",
            redirectAfterLogin: "",
            aliyunSMS: {
              accessKeyEncrypt: "",
              hierarchicalList: [],
              region: "",
              endpointOverride: "",
              signName: "",
              templateCode: "",
              excludeNumber: "",
              validTime: "",
              daySendMax: "",
              countdown: ""
            },
            aliyunSMSGlobe: {
              accessKeyEncrypt: "",
              hierarchicalList: [],
              region: "",
              endpointOverride: "",
              senderId: "",
              message: "",
              excludeNumber: "",
              validTime: "",
              daySendMax: "",
              countdown: ""
            },
            saveVersionNumber: 0,
            animation: {
              page_transition: false,
              page_breathe: false,
              page_zoom: false,
              page_shaking: false
            },
            errorEmailConfig: {
              host: "",
              email: "",
              senderName: "",
              to: "",
              authorizationCode: ""
            },
            pageFontSize: {
              fontSizeType: [], //BYOD页面控制字体大小复选框选中的值(i.g. normal/ large/ xLarger)
              default: "normal", //预设字体大小
              normal: {
                sizeType: "normal",
                firstLan: "",
                secondLan: "",
                thirdLan: ""
              },
              large: {
                sizeType: "large",
                firstLan: "",
                secondLan: "",
                thirdLan: ""
              },
              xLarger: {
                sizeType: "xLarger",
                firstLan: "",
                secondLan: "",
                thirdLan: ""
              }
            },
            orderFoodSorting: "",
            mTypeCodeLength: "",
            additionalItemsForTakeaway: {
              fixed: "",
              auto: ""
            },
            forceSalesMode: "",
            distanceAlert: "",
            delayRefreshCacheTime: "",
            ossBackupUrl: "",
            foodCourt: {
              transactionCode: "",
              column: 1,
              customerCode: "",
              firstBillNumber: "080-000001",
              resetByHourOfDay: 1,
              sort: "",
              timeZone: ""
            },
            checkFBTransBeforePayment: {
              timeOut: null
            },
            menuPopup: {
              carouselInterval: "",
              timeout: "",
              mode: ""
            },
            showOutsideAddCartBtn: false,
            modifiersAlwaysDispMods: false,
            closeTakeawayShopPhoto: false,
            infiniteLoop: {
              distanceNavHeight: ""
            },
            verticalOrderLayout: {
              distanceNavHeight: ""
            },
            diningStyle: "",
            displayCRM: {
              showInfo: [],
              requiredMemtype2: "",
              inviteMemberToLogin: true,
              birthdayReminder: true,
              birthdayPromptEN: "",
              birthdayPromptZH: "",
              birthdayPromptThirdLan: "",
              dineInMemberLoginSwitch: true,
              takeawayMemberLoginSwitch: true
            },
            browserRestrictions: {
              category: []
            },
            memberConfig: {
              en: "",
              zh: "",
              thirdLan: "",
              discType: null,
              pointsShowInLogin: false,
              discountSwitch: true, //控制是否开启堂食折扣优惠,关闭则不计算折扣优惠
              takeawayDiscountSwitch: true //控制是否开启会员折扣优惠
            },
            emailSenderManagement: {
              email: "",
              senderName: "",
              smtpRadio: "smtp.gmail.com",
              smtpDiy: "",
              smtpHost: "", //最终smtpHost
              authorizationCode: "",
              excludeNumber: "",
              validTime: 300
            },
            memberAccountManagement: {
              memtype2: "", //注册的会员账号类型
              verifyContact: ["Email"],
              personalDetails: ["Name", "Gender", "Birthday"],
              registerTitle: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              registerMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              resetPasswordTitle: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              resetPasswordMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              aliyun_registerMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              aliyunGlobe_registerMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              aliyun_resetPasswordMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              aliyunGlobe_resetPasswordMsg: {
                en: "",
                zh: "",
                thirdLan: ""
              },
              memberExpRadio: "memberExpDay",
              pointExpRadio: "pointExpDay",
              memberExpDay: null,
              memberExpDate: null,
              pointExpDay: null,
              pointExpDate: null
            },

            dineInStoreNumber: "",
            combinedNavigation: {
              tab1: "AllFoodType",
              tab2: "AllFoodType",
              color: null
            },
            rollingFoodPicture: false,
            rollingFoodBigPicture: false,
            groupedFoodsByTypeCode: false,
            orderTimeLimit: [],
            salesControl: {
              countScope: 0,
              maxCount: 0,
              includeFCode: "",
              refreshTime: 0,
              estimatedTime: 0,
              rule: []
            },
            mapConfig: {
              timeout: 5,
              disableMap: false
            },
            billTax: {
              billTaxRate: null, //基础税率
              billTaxIncludeDiscount: false, //是否包含折扣
              billTaxIncludeServiceCharge: false //是否包含服务费
            },
            keepPayLogUserInfo: 0,
            sseConnect: {
              inactiveTime: 0,
              itemCtrl: true,
              shopCartSync: true
            },
            disableItemCtrlLog: {
              onlyLogSoldOutItemCtrl: false
            }
          },
          prefixOption: {
            Display: "#6F9167",
            Order: "#035553",
            System: "#0084B4",
            Others: "#70B5D4",
            "MO&P": "#769EB0",
            CRM: "#4d648d"
          },

          typeOption: [
            {
              label: "Clear Cart Button", // 清空购物车按钮
              value: "clearCartButton",
              prefix: "Display"
            },
            {
              label: "Display food categories", //fty页开启/关闭
              value: "ftyPage",
              prefix: "Display"
            },
            {
              label: "Display item price", //购物车页是否显示每项价格
              value: "price",
              prefix: "Display"
            },

            {
              label: "Display total price in cart", //是否显示购物车价格合计
              value: "showTotalPrice",
              prefix: "Display"
            },
            {
              label: "Display modifiers", //是否显示购物车细项
              value: "sideDish",
              prefix: "Display"
            },
            {
              label: "Display out-of-hour items", //是否显示过期数据
              value: "showTimeOutItems",
              prefix: "Display"
            },
            {
              label: "Always display modifiers", //是否根据food内有无必选项显示添加+至购物车按钮
              value: "showOutsideAddCartBtn",
              prefix: "Display"
            },
            {
              label: "Modifiers always display modifiers", //点击细项是否强制弹窗显示里面细项(即使细项内全是可不选的内容)
              value: "modifiersAlwaysDispMods",
              prefix: "Display"
            },

            {
              label: "Display Big Picture (Food)", // FOOD 详情页 大图
              value: "rollingFoodBigPicture",
              prefix: "Display"
            },
            {
              label: "Display order records in cart(MD)",
              value: "displayCartOrderRecord",
              prefix: "Display"
            },
            {
              label: "Group same fcode in different food type", // 开启则加入购物车合并不同fty下相同fcode的food
              value: "groupedFoodsByTypeCode",
              prefix: "Display"
            },
            {
              label: "Roll away food picture", // food 详情页滚动覆盖图片
              value: "rollingFoodPicture",
              prefix: "Display"
            },

            {
              label: "Shopping cart Food thumbnail", //是否显示购物车缩略图
              prefix: "Display",
              value: "thumbnail"
            },
            {
              label: "Show arrow on horizontal navigation", //是否显示点餐区左右箭头
              prefix: "Display",
              value: "showArrow"
            },

            {
              label: "Large Modifier Photo", //放大food详情页一栏配置的图片
              value: "lModPhoto",
              prefix: "Display"
            },

            {
              label: "Display CRM", //控制个人中心显示信息
              value: "displayCRM",
              prefix: "CRM",
              formElement: [
                {
                  elementType: "checkbox",
                  label: "Show Info",
                  // elementGroupClass: "fontSize-checkBox-group",
                  // elementChkClass: "fontSize-checkBox",
                  prop: "displayCRM.showInfo",
                  model: "showInfo",
                  elementOption: [
                    {
                      label: "Name", //名称
                      value: "Name"
                    },
                    {
                      label: "Email", //邮箱
                      value: "Email"
                    },
                    {
                      label: "Telephone", //电话号码
                      value: "Telephone"
                    },
                    {
                      label: "Balance", //账号余额
                      value: "Balance"
                    },
                    {
                      label: "Points", //积分
                      value: "Points"
                    },
                    {
                      label: "Code", //编号
                      value: "Code"
                    },
                    {
                      label: "Expdate", //到期日
                      value: "Expdate"
                    },
                    {
                      label: "Points_due", //点数到期日
                      value: "Points_due"
                    },
                    {
                      label: "Coupon", // 优惠券
                      value: "Coupon"
                    },
                    {
                      label: "Recharge", // 充值
                      value: "Recharge"
                    }
                  ]
                },

                {
                  elementType: "input",
                  label: "Required Memtype2",
                  model: "requiredMemtype2",
                  prop: "displayCRM.requiredMemtype2",
                  placeholder: "use ; to separate multiple"
                },
                // {
                //   elementType: "richText",
                //   label: "Birthday message(en)",
                //   prop: "displayCRM.birthdayPromptEN",
                //   model: "birthdayPromptEN",
                //   richTextId: "birthdayPromptEN"
                // }
                {
                  elementType: "textarea",
                  label: "Birthday Prompt(en)",
                  prop: "displayCRM.birthdayPromptEN",
                  model: "birthdayPromptEN",
                  placeholder: "Please enter birthday prompt (en) ",
                  showConditions: "displayCRM.birthdayReminder == true"
                },
                {
                  elementType: "textarea",
                  label: "Birthday Prompt(zh-hk)",
                  prop: "displayCRM.birthdayPromptZH",
                  model: "birthdayPromptZH",
                  placeholder: "Please enter birthday prompt(zh-hk)",
                  showConditions: "displayCRM.birthdayReminder == true"
                },
                {
                  elementType: "textarea",
                  label: "Birthday Prompt(thirdLan)",
                  prop: "displayCRM.birthdayPromptThirdLan",
                  model: "birthdayPromptThirdLan",
                  placeholder: "Please enter birthday prompt(thirdLan)",
                  showConditions: "displayCRM.birthdayReminder == true"
                },
                {
                  elementType: "switch",
                  label: "Birthday Reminder",
                  model: "birthdayReminder"
                },
                {
                  elementType: "switch",
                  label: "Invite Member to login",
                  model: "inviteMemberToLogin"
                },
                {
                  elementType: "switch",
                  label: "DineIn Member Login",
                  model: "dineInMemberLoginSwitch"
                },
                {
                  elementType: "switch",
                  label: "Takeaway Member Login",
                  model: "takeawayMemberLoginSwitch"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },

            {
              label: "Member Config",
              value: "memberConfig",
              prefix: "CRM",
              formElement: [
                {
                  elementType: "input",
                  label: "Discount Desc(en)",
                  model: "en"
                },
                {
                  elementType: "input",
                  label: "Discount Desc(zh)",
                  model: "zh"
                },
                {
                  elementType: "input",
                  label: "Discount Desc(thirdLan)",
                  model: "thirdLan"
                },
                {
                  elementType: "inputNumber",
                  label: "Discount Code",
                  model: "discType",
                  min: 0
                },
                {
                  elementType: "switch",
                  label: "Points Show In Login",
                  model: "pointsShowInLogin"
                },
                {
                  elementType: "switch",
                  // 原label:Member Discount Switch,字段未改
                  label: "DineIn Discount Switch",
                  model: "discountSwitch"
                },
                {
                  elementType: "switch",
                  label: "Takeaway Discount Switch",
                  model: "takeawayDiscountSwitch"
                }
              ]
            },
            {
              label: "Member Register",
              value: "memberAccountManagement",
              prefix: "CRM",
              formElement: [
                {
                  elementType: "input",
                  label: "Member Type",
                  model: "memtype2",
                  prop: "memberAccountManagement.memtype2",
                  placeholder: "Please enter the member type"
                },
                {
                  elementType: "checkbox",
                  label: "Register Info",
                  prop: "memberAccountManagement.verifyContact",
                  model: "verifyContact",
                  elementOption: [
                    {
                      label: "Email", // 邮箱
                      value: "Email",
                      disabled: true
                    },
                    {
                      label: "Telephone", // 手机号
                      value: "Telephone"
                    }
                  ]
                },
                {
                  elementType: "checkbox",
                  label: "Register Info",
                  prop: "memberAccountManagement.personalDetails",
                  model: "personalDetails",
                  elementOption: [
                    {
                      label: "Name", // 名字
                      value: "Name"
                    },
                    {
                      label: "Gender", // 性别
                      value: "Gender"
                    },
                    {
                      label: "Birthday", // 生日
                      value: "Birthday"
                    }
                  ]
                },
                {
                  elementType: "radio",
                  label: "Member Exp Date",
                  model: "memberExpRadio",
                  prop: "memberAccountManagement.memberExpRadio",
                  className: "memberExpRadio",
                  radioOptions: [
                    {
                      label: "",
                      radioElement: "input",
                      value: "memberExpDay",
                      model: "memberExpDay",
                      placeholder: "Please enter a number",
                      slotEnd: "Day"
                    },
                    {
                      label: "",
                      radioElement: "datePicker",
                      value: "memberExpDate",
                      model: "memberExpDate"
                    }
                  ]
                },
                {
                  elementType: "radio",
                  label: "Point Exp Date",
                  model: "pointExpRadio",
                  prop: "memberAccountManagement.pointExpRadio",
                  className: "pointExpRadio",
                  radioOptions: [
                    {
                      label: "",
                      radioElement: "input",
                      value: "pointExpDay",
                      model: "pointExpDay",
                      placeholder: "Please enter a number",
                      slotEnd: "Day"
                    },
                    {
                      label: "",
                      radioElement: "datePicker",
                      value: "pointExpDate",
                      model: "pointExpDate"
                    }
                  ]
                },
                {
                  elementType: "alertTip",
                  type: "warning",
                  effect: "dark",
                  showIcon: true,
                  closable: false,
                  title: `The following configurations containing "Msg" & "Title" need to be used with Type=Email Sender Management configuration`
                },

                {
                  elementType: "textarea",
                  label: "Email Register Title (en)",
                  prop: "memberAccountManagement.registerTitle.en",
                  model1: "registerTitle",
                  model2: "en",
                  level: 2,
                  placeholder: "Please enter the title in the first language"
                },
                {
                  elementType: "textarea",
                  label: "Email Register Title (zh-hk)",
                  prop: "memberAccountManagement.registerTitle.zh",
                  model1: "registerTitle",
                  model2: "zh",
                  level: 2,
                  placeholder: "Please enter the title in the second language"
                },
                {
                  elementType: "textarea",
                  label: "Email Register Title (thirdLan)",
                  prop: "memberAccountManagement.registerTitle.thirdLan",
                  model1: "registerTitle",
                  model2: "thirdLan",
                  level: 2,
                  placeholder: "Please enter the title in the third language"
                },
                {
                  elementType: "textarea",
                  label: "Email Register Message (en)",
                  prop: "memberAccountManagement.registerMsg.en",
                  model1: "registerMsg",
                  model2: "en",
                  level: 2,
                  placeholder: "Please enter the message in the first language"
                },
                {
                  elementType: "textarea",
                  label: "Email Register Message (zh-hk)",
                  prop: "memberAccountManagement.registerMsg.zh",
                  model1: "registerMsg",
                  model2: "zh",
                  level: 2,
                  placeholder: "Please enter the message in the second language"
                },
                {
                  elementType: "textarea",
                  label: "Email Register Message (thirdLan)",
                  prop: "memberAccountManagement.registerMsg.thirdLan",
                  model1: "registerMsg",
                  model2: "thirdLan",
                  level: 2,
                  placeholder: "Please enter the message in the third language"
                },
                {
                  elementType: "textarea",
                  label: "Email Reset Password Title (en)",
                  prop: "memberAccountManagement.resetPasswordTitle.en",
                  model1: "resetPasswordTitle",
                  model2: "en",
                  level: 2,
                  placeholder: "Please enter the title in the first language"
                },
                {
                  elementType: "textarea",
                  label: "Email Reset Password Title (zh-hk)",
                  prop: "memberAccountManagement.resetPasswordTitle.zh",
                  model1: "resetPasswordTitle",
                  model2: "zh",
                  level: 2,
                  placeholder: "Please enter the title in the second language"
                },
                {
                  elementType: "textarea",
                  label: "Email Reset Password Title (thirdLan)",
                  prop: "memberAccountManagement.resetPasswordTitle.thirdLan",
                  model1: "resetPasswordTitle",
                  model2: "thirdLan",
                  level: 2,
                  placeholder: "Please enter the title in the third language"
                },
                {
                  elementType: "textarea",
                  label: "Email Reset Password Message (en)",
                  prop: "memberAccountManagement.resetPasswordMsg.en",
                  model1: "resetPasswordMsg",
                  model2: "en",
                  level: 2,
                  placeholder: "Please enter the message in the first language"
                },

                {
                  elementType: "textarea",
                  label: "Email Reset Password Message (zh-hk)",
                  prop: "memberAccountManagement.resetPasswordMsg.zh",
                  model1: "resetPasswordMsg",
                  model2: "zh",
                  level: 2,
                  placeholder: "Please enter the message in the second language"
                },

                {
                  elementType: "textarea",
                  label: "Email Reset Password Message (thirdLan)",
                  prop: "memberAccountManagement.resetPasswordMsg.thirdLan",
                  model1: "resetPasswordMsg",
                  model2: "thirdLan",
                  level: 2,
                  placeholder: "Please enter the message in the third language"
                },
                {
                  elementType: "alertTip",
                  type: "warning",
                  effect: "dark",
                  showIcon: true,
                  closable: false,
                  title: `The following configurations containing "SMS" need to be used with Type=Aliyun SMS (Chinese Mainland) / Type=Aliyun SMS (Globe) configuration`
                },
                {
                  elementType: "textarea",
                  label: "SMS Register Template Code(en)",
                  prop: "memberAccountManagement.aliyun_registerMsg.en",
                  model1: "aliyun_registerMsg",
                  model2: "en",
                  level: 2,
                  placeholder: "Please enter the message in the first language"
                },
                {
                  elementType: "textarea",
                  label: "SMS Register Template Code(zh-hk)",
                  prop: "memberAccountManagement.aliyun_registerMsg.zh",
                  model1: "aliyun_registerMsg",
                  model2: "zh",
                  level: 2,
                  placeholder: "Please enter the message in the second language"
                },
                {
                  elementType: "textarea",
                  label: "SMS Register Template Code(thirdLan)",
                  prop: "memberAccountManagement.aliyun_registerMsg.thirdLan",
                  model1: "aliyun_registerMsg",
                  model2: "thirdLan",
                  level: 2,
                  placeholder: "Please enter the message in the third language"
                },
                {
                  elementType: "textarea",
                  label: "SMS-Globe Register Message (en)",
                  prop: "memberAccountManagement.aliyunGlobe_registerMsg.en",
                  model1: "aliyunGlobe_registerMsg",
                  model2: "en",
                  level: 2,
                  placeholder: "Please enter the message in the first language"
                },
                {
                  elementType: "textarea",
                  label: "SMS-Globe Register Message (zh-hk)",
                  prop: "memberAccountManagement.aliyunGlobe_registerMsg.zh",
                  model1: "aliyunGlobe_registerMsg",
                  model2: "zh",
                  level: 2,
                  placeholder: "Please enter the message in the second language"
                },
                {
                  elementType: "textarea",
                  label: "SMS-Globe Register Message (thirdLan)",
                  prop: "memberAccountManagement.aliyunGlobe_registerMsg.thirdLan",
                  model1: "aliyunGlobe_registerMsg",
                  model2: "thirdLan",
                  level: 2,
                  placeholder: "Please enter the message in the third language"
                },
                {
                  elementType: "textarea",
                  label: "SMS Reset Password Template Code(en)",
                  prop: "memberAccountManagement.aliyun_resetPasswordMsg.en",
                  model1: "aliyun_resetPasswordMsg",
                  model2: "en",
                  level: 2,
                  placeholder: "Please enter the message in the first language"
                },
                {
                  elementType: "textarea",
                  label: "SMS Reset Password Template Code(zh-hk)",
                  prop: "memberAccountManagement.aliyun_resetPasswordMsg.zh",
                  model1: "aliyun_resetPasswordMsg",
                  model2: "zh",
                  level: 2,
                  placeholder: "Please enter the message in the second language"
                },
                {
                  elementType: "textarea",
                  label: "SMS Reset Password Template Code(thirdLan)",
                  prop: "memberAccountManagement.aliyun_resetPasswordMsg.thirdLan",
                  model1: "aliyun_resetPasswordMsg",
                  model2: "thirdLan",
                  level: 2,
                  placeholder: "Please enter the message in the third language"
                },
                {
                  elementType: "textarea",
                  label: "SMS-Globe Reset Password Message (en)",
                  prop: "memberAccountManagement.aliyunGlobe_resetPasswordMsg.en",
                  model1: "aliyunGlobe_resetPasswordMsg",
                  model2: "en",
                  level: 2,
                  placeholder: "Please enter the message in the first language"
                },
                {
                  elementType: "textarea",
                  label: "SMS-Globe Reset Password Message (zh-hk)",
                  prop: "memberAccountManagement.aliyunGlobe_resetPasswordMsg.zh",
                  model1: "aliyunGlobe_resetPasswordMsg",
                  model2: "zh",
                  level: 2,
                  placeholder: "Please enter the message in the second language"
                },
                {
                  elementType: "textarea",
                  label: "SMS-Globe Reset Password Message (thirdLan)",
                  prop: "memberAccountManagement.aliyunGlobe_resetPasswordMsg.thirdLan",
                  model1: "aliyunGlobe_resetPasswordMsg",
                  model2: "thirdLan",
                  level: 2,
                  placeholder: "Please enter the message in the third language"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "BillTax", //订单税率
              value: "billTax",
              prefix: "Order",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Bill Tax Rate",
                  prop: "billTax.billTaxRate",
                  min: 0,
                  suffixName: "%",
                  model: "billTaxRate"
                },
                {
                  elementType: "switch",
                  label: "Bill Tax Include Discount",
                  model: "billTaxIncludeDiscount"
                },
                {
                  elementType: "switch",
                  label: "Bill Tax Include Service Charge",
                  model: "billTaxIncludeServiceCharge"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Display message after order", //下单成功定制化提示
              value: "postOrderPopup",
              prefix: "Order"
            },
            {
              label: "Text message before sending order", //购物车底部服务费提示语
              value: "shopTipText",
              prefix: "Order",
              formElement: [
                {
                  elementType: "radio",
                  label: "Font Size",
                  prop: "shopTipText.tipFontSize",
                  model: "tipFontSize",
                  radioOptions: [
                    {
                      label: "Normal",
                      value: "normal"
                    },
                    {
                      label: "Enlarge",
                      value: "enlarge"
                    }
                  ]
                },
                {
                  elementType: "textarea",
                  label: "Message (en)",
                  prop: "shopTipText.en",
                  model: "en",
                  placeholder: "Please enter the message before sending the order (en)"
                },
                {
                  elementType: "textarea",
                  label: "Message (zh-hk)",
                  prop: "shopTipText.zh",
                  model: "zh",
                  placeholder: "Please enter the message before sending the order (zh)"
                },
                {
                  elementType: "textarea",
                  label: "Message (thirdLan)",
                  prop: "shopTipText.thirdLan",
                  model: "thirdLan",
                  placeholder: "Please enter the message before sending the order (thirdLan)"
                }
              ]
            },
            {
              label: "Display Order Confirmation Popup", //正常模式下单前确认的弹窗
              value: "confirmationPopup",
              prefix: "Order"
            },
            {
              label: "Limit number of dishes per order", //菜品最大可选数量
              value: "numberDishes",
              prefix: "Order",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Limit number",
                  prop: "numberDishes.number",
                  model: "number",
                  min: 0,
                  max: 1000
                },
                {
                  elementType: "switch",
                  label: "Multiply By Pax",
                  model: "multiplyByPax"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Order Time Limit", // 最长下单时间; 仅在此时间之内可下单,超过前有倒计时
              value: "orderTimeLimit",
              prefix: "Order",
              formElement: [
                {
                  elementType: "component",
                  name: "order-timer-form",
                  fullRow: true,
                  addProps: {
                    list() {
                      return []
                    }
                  },
                  editProps: {
                    list() {
                      return this.editForm.orderTimeLimit
                    }
                  }
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Promotional Items", // 赠品(实质指的是赠品规则叠加功能是否开启)
              value: "promotionOverlay",
              prefix: "Order"
            },
            {
              label: "Sales Control", // 显示送单排队,
              value: "salesControl",
              prefix: "Order",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Time Scope",
                  suffixName: "minute",
                  model: "countScope",
                  prop: "salesControl.countScope",
                  min: 0
                },
                {
                  elementType: "inputNumber",
                  label: "Max Order (Count)",
                  model: "maxCount",
                  prop: "salesControl.maxCount",
                  min: 0
                },
                {
                  elementType: "inputNumber",
                  label: "Max Order (Estimated Time)",
                  model: "estimatedTime",
                  prop: "salesControl.estimatedTime",
                  min: 0,
                  suffixName: "minute"
                },
                {
                  elementType: "inputNumber",
                  label: "Max Order (Refresh Time)",
                  model: "refreshTime",
                  prop: "salesControl.refreshTime",
                  min: 0,
                  suffixName: "minute"
                },
                {
                  elementType: "input",
                  label: "Food Codes",
                  model: "includeFCode",
                  prop: "salesControl.includeFCode",
                  placeholder: "use ; to separate multiple"
                },
                {
                  elementType: "component",
                  name: "sales-control-form",
                  fullRow: false, // 组件width 100%
                  // props都必须以function回,component内再处理
                  addProps: {
                    list() {
                      return []
                    }
                  },
                  editProps: {
                    list() {
                      return this.editForm.salesControl.rule
                    }
                  }
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Suggestive selling", // 热门推荐数量
              value: "hotSaleNum",
              prefix: "Order",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Suggestive selling",
                  prop: "hotSaleNum",
                  model: "hotSaleNum",
                  min: 0,
                  max: 100
                }
              ]
            },
            {
              label: "Disable Item Ctrl Log",
              value: "disableItemCtrlLog",
              prefix: "Others",
              formElement: [
                {
                  elementType: "switch",
                  label: "Only Log Sold Out Item Ctrl", //只生成售罄变化日志（不包含只剩6个变成只剩5个这种itemCtrl编号）
                  model: "onlyLogSoldOutItemCtrl"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable", //开关开了后，itemCtrl更新时不生成相关的orderReport文件
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Test Time Simulation", //控制测试台是否开启模拟时间功能
              value: "isEnableTimeSimulation",
              prefix: "Others"
            },
            {
              label: "Send email configuration after error", //出错后发送邮件给管理员的配置
              value: "errorEmailConfig",
              prefix: "Others",
              formElement: [
                {
                  elementType: "input",
                  label: "Host", //发送者邮箱的主机
                  prop: "errorEmailConfig.host",
                  model: "host",
                  placeholder: "Example: smtp.gmail.com"
                },
                {
                  elementType: "input",
                  label: "Sender Email", //发送者邮箱
                  prop: "errorEmailConfig.email",
                  model: "email",
                  placeholder: "Please enter the email"
                },
                {
                  elementType: "input",
                  label: "Sender Name", //发送者名
                  prop: "errorEmailConfig.senderName",
                  model: "senderName",
                  placeholder: "Please enter the sender name"
                },
                {
                  elementType: "input",
                  label: "Target Email", //接收者邮箱，分号;隔开
                  prop: "errorEmailConfig.to",
                  model: "to",
                  placeholder: "Please enter the target email address, multiples are separated by ;"
                },
                {
                  elementType: "input",
                  label: "Authorization Code", //授权码
                  prop: "errorEmailConfig.authorizationCode",
                  model: "authorizationCode",
                  placeholder: "Please enter the authorization code"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Merge food", //后端合并菜品
              value: "mergeFood",
              prefix: "Others"
            },
            {
              label: "Merge food sequence",
              value: "mergeFoodFollowsFirst",
              prefix: "Others"
            },
            {
              label: "SSE Connect",
              value: "sseConnect",
              prefix: "System",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Close when inactive",
                  prop: "sseConnect.inactiveTime",
                  model: "inactiveTime",
                  min: 0,
                  suffixName: "minutes"
                },
                {
                  elementType: "switch",
                  label: "Enable itemCtrl",
                  model: "itemCtrl"
                },
                {
                  elementType: "switch",
                  label: "Enable shopCart sync",
                  model: "shopCartSync"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Keep Pay Log Userinfo", // 后端用,日志保留天数
              value: "keepPayLogUserInfo",
              prefix: "System",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Retention days",
                  min: 0,
                  suffixName: "days"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Food Court", //美食广场显示店铺栏目布局配置(1栏/2栏/3栏)
              value: "foodCourt",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "Transaction Code",
                  prop: "foodCourt.transactionCode",
                  model: "transactionCode",
                  placeholder: "Please enter the transaction Code"
                },
                {
                  elementType: "input",
                  label: "Customer Code",
                  prop: "foodCourt.customerCode",
                  model: "customerCode",
                  placeholder: "Please enter the customer code"
                },
                {
                  elementType: "select",
                  label: "Column",
                  prop: "foodCourt.column",
                  model: "column",
                  placeholder: "Please select the Column",
                  style: "width: 100%",
                  elementOption: [
                    {
                      label: 1,
                      value: 1
                    },
                    {
                      label: 2,
                      value: 2
                    },
                    {
                      label: 3,
                      value: 3
                    }
                  ]
                },
                {
                  elementType: "input",
                  label: "First billNumber",
                  prop: "foodCourt.firstBillNumber",
                  model: "firstBillNumber",
                  placeholder: "Please enter the first bill number,format:080-000001"
                },
                {
                  elementType: "inputNumber",
                  min: 0,
                  label: "resetByHourOfDay",
                  prop: "foodCourt.resetByHourOfDay",
                  model: "resetByHourOfDay",
                  placeholder: "Please enter the bill number interval days"
                },
                {
                  elementType: "input",
                  label: "Store sorting",
                  model: "sort",
                  placeholder: "Please enter the store number sequence, use ';' combination"
                },
                {
                  elementType: "select",
                  label: "Time Zone",
                  prop: "foodCourt.timeZone",
                  model: "timeZone",
                  placeholder: "Please select the time zone",
                  style: "width: 100%",
                  elementOption: "timeZoneList"
                }
              ]
            },
            {
              label: "Bilingual display", //是否显示语言切换
              value: "allowSwitchLanguage",
              prefix: "System"
            },

            {
              label: "Continuous menu scroll", // 点餐页food容器无限滚动显示所有food
              value: "infiniteLoop",
              prefix: "System",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Distance Nav Height",
                  prop: "infiniteLoop.distanceNavHeight",
                  model: "distanceNavHeight",
                  min: 0,
                  max: 400,
                  placeholder: "Please enter height",
                  suffixName: "px"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Vertical Order Layout", //是否显示语言切换
              value: "verticalOrderLayout",
              prefix: "System",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Distance Nav Height",
                  prop: "verticalOrderLayout.distanceNavHeight",
                  model: "distanceNavHeight",
                  min: 0,
                  max: 400,
                  placeholder: "Please enter height",
                  suffixName: "px"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Combined Food Type Navigation", // 组合导航栏
              value: "combinedNavigation",
              prefix: "System",
              formElement: [
                {
                  elementType: "radio",
                  label: "Food Type Navigation(1)",
                  model: "tab1",
                  radioOptions: [
                    {
                      label: "All FoodType",
                      value: "AllFoodType"
                    },
                    {
                      label: "Box+Other",
                      value: "Box+Other"
                    }
                  ]
                },
                {
                  elementType: "radio",
                  label: "Food Type Navigation(2)",
                  model: "tab2",
                  radioOptions: [
                    {
                      label: "All FoodType",
                      value: "AllFoodType"
                    },
                    {
                      label: "Selected FoodType",
                      value: "SelectedFoodType"
                    }
                  ]
                },
                {
                  elementType: "input",
                  label: "Tab Selected Color",
                  prop: "combinedNavigation.color",
                  model: "color",
                  placeholder: "Default: Theme Color"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },

            {
              label: "External UAT preview", //预览历史版本鉴权开关
              value: "externalUatPreview",
              prefix: "System",
              formElement: [
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Animation", //byod 动画效果
              value: "animation",
              prefix: "System",
              formElement: [
                {
                  elementType: "switch",
                  label: "Page transition", //页面切换动画
                  model: "page_transition"
                },
                {
                  elementType: "switch",
                  label: "Page Button breathe", // 页面按钮呼吸动画
                  model: "page_breathe"
                },
                {
                  elementType: "switch",
                  label: "Page Button zoom", // 页面按钮缩放动画
                  model: "page_zoom"
                },
                {
                  elementType: "switch",
                  label: "Page button shaking", // 页面按钮抖动动画
                  model: "page_shaking"
                }
              ]
            },
            {
              label: "Assist/EnhAssist Mode Login Method", // AssistMode员工点餐模式密码/默认人数
              value: "assistMode",
              prefix: "System",
              formElement: [
                {
                  elementType: "tooltip",
                  content: "You can set your password to null value",
                  placement: "bottom-end",
                  manual: "true",
                  hideAfter: "1000",
                  effect: "light",
                  label: "Password",
                  inputModel: "password",
                  placeholder: "Please enter the password"
                },
                {
                  elementType: "inputNumber",
                  label: "Default Pax",
                  prop: "assistMode.pax", //员工模式默认人数自动开台
                  model: "pax",
                  min: 0,
                  placeholder: "Please enter default pax"
                },
                {
                  elementType: "switch",
                  label: "Exit using password", //页面切换动画
                  model: "exitUsingPassword"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Currency symbol", //动态货币符号
              value: "currencyWay",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "Currency symbol",
                  prop: "currencyWay",
                  model: "currencyWay",
                  placeholder: "Please enter Currency symbol"
                }
              ]
            },
            {
              label: "Decimals",
              value: "keepDecimals", //保留小数位数
              prefix: "System",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "SignificantDigits",
                  prop: "keepDecimals.significantDigits",
                  model: "significantDigits",
                  min: 1,
                  max: 3,
                  precision: 0
                },
                {
                  elementType: "switch",
                  label: "ZeroPadding",
                  model: "zeroPadding"
                }
              ]
            },
            {
              label: "Delay Refresh Cache Time", // 延迟刷新缓存时间(后端使用)
              value: "delayRefreshCacheTime",
              prefix: "System",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Delay Time (Second)",
                  prop: "delayRefreshCacheTime",
                  model: "delayRefreshCacheTime",
                  min: 0,
                  precision: 0
                }
              ]
            },
            {
              label: "Food Details Category Foldable", // food type 可折叠
              value: "categoryFoldable",
              prefix: "System"
            },
            {
              label: "Split Main Food On Order", // 下单时拆分主food(后端使用)
              value: "splitMainFoodOnOrder",
              prefix: "System"
            },
            {
              label: "Redirect to order history after order confirmation", // 下单后跳转至订单历史
              value: "goHistoryAfterOrder",
              prefix: "System"
            },
            {
              label: "hide orders without order number in history page", // 历史订单页面隐藏待支付订单
              value: "hideOrdersWithoutOrderNumberInHistoryPage",
              prefix: "System"
            },
            {
              label: "Allow pax input in FB Trans", //FB下index页面弹窗选择人数(埋点统计)
              value: "allowPaxInputInFBTrans",
              prefix: "System"
            },
            {
              label: "HQ Mode",
              value: "hqModule",
              prefix: "System"
            },
            {
              label: "MType Code Length", // Type Code的长度
              value: "mTypeCodeLength",
              prefix: "System",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Length",
                  prop: "mTypeCodeLength",
                  model: "mTypeCodeLength",
                  placeholder: "Please enter mType code length",
                  min: 0,
                  max: 5
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Order Food Sorting",
              value: "orderFoodSorting",
              prefix: "System",
              formElement: [
                {
                  elementType: "textarea",
                  label: "Sorting rule",
                  placeholder: "Please enter the sorting rule,use ; to separate multiple",
                  prop: "orderFoodSorting",
                  model: "orderFoodSorting"
                }
              ]
            },
            {
              label: "OSS Backup URL", // 图片服务器备份地址
              value: "ossBackupUrl",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "Backup URL",
                  model: "ossBackupUrl",
                  placeholder: "Please enter OSS Backup URL"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Post order external link", // 下单成功后弹窗模式提示用户是否跳转赞助链接
              value: "sponsoredLink",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "link",
                  prop: "sponsoredLink",
                  model: "sponsoredLink",
                  placeholder: "Please enter sponsored link"
                }
              ]
            },
            {
              label: "Pre-order mode PIN length",
              value: "preOrderPINLength",
              prefix: "System",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Length",
                  prop: "preOrderPINLength",
                  model: "preOrderPINLength",
                  min: 4
                }
              ]
            },
            {
              label: "Save Version Number", // 保留历史版本数量
              value: "saveVersionNumber",
              prefix: "System",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Save Version Number",
                  prop: "saveVersionNumber",
                  model: "saveVersionNumber",
                  min: 0,
                  max: 50
                }
              ]
            },
            {
              label: "Select POS unit price 1-8", //动态价格字段
              value: "priceWay",
              prefix: "System",
              formElement: [
                {
                  elementType: "component",
                  name: "select-pos-price",
                  fullRow: true,
                  addProps: {
                    priceWay() {
                      return {}
                    }
                  },
                  editProps: {
                    priceWay() {
                      return this.editForm.priceWay
                    }
                  }
                }
              ]
            },
            {
              label: "Service charge settings", //服务费计算方式配置(四拾伍入等)
              value: "serviceCharges",
              prefix: "System",
              formElement: [
                {
                  elementType: "radio",
                  label: "Service charge settings",
                  prop: "serviceCharges.openType",
                  model: "openType",
                  radioOptions: [
                    {
                      label: "Enable(bill)",
                      value: "perBill"
                    },
                    {
                      label: "Enable(per item)",
                      value: "perItem"
                    }
                  ]
                },
                {
                  elementType: "radio",
                  label: "Rounding Method",
                  prop: "serviceCharges.countType",
                  model: "countType",
                  className: "countTypeRadio",
                  radioOptions: [
                    {
                      label: "No change",
                      value: "noChange"
                    },
                    {
                      label: "Ceiling (Up)",
                      value: "ceiling"
                    },
                    {
                      label: "Floor (Down)",
                      value: "floor"
                    },
                    {
                      label: "Rounding",
                      value: "rounding"
                    }
                  ]
                },
                {
                  elementType: "switch",
                  label: "Display in shopping cart page",
                  prop: "serviceCharges.displayInShoppingCartPage", // 在购物车页面显示服务费
                  model: "displayInShoppingCartPage"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Shop address", //首页地址(三语言)
              value: "address",
              prefix: "System",
              formElement: [
                {
                  elementType: "textarea",
                  label: "Address(en)",
                  prop: "address.en",
                  model: "en",
                  placeholder: "Please enter address(en)"
                },
                {
                  elementType: "textarea",
                  label: "Address(zh-hk)",
                  prop: "address.zh",
                  model: "zh",
                  placeholder: "Please enter address(zh-hk)"
                },
                {
                  elementType: "textarea",
                  label: "Address(thirdLan)",
                  prop: "address.thirdLan",
                  model: "thirdLan",
                  placeholder: "Please enter address(thirdLan)"
                }
              ]
            },
            {
              label: "Static QR code", // 自动开台人数
              value: "fixQRAutoOpenTable",
              prefix: "System",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Default Pax",
                  prop: "fixQRAutoOpenTable.pax",
                  model: "pax",
                  min: 0,
                  placeholder: "Please enter default pax"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },

            {
              label: "Theme color", //主题颜色
              value: "color",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "Color ('#' start)",
                  prop: "color",
                  model: "color",
                  placeholder: "Please enter Color"
                }
              ]
            },
            {
              label: "PC version background(color)", //PC版背景主题颜色
              value: "pcBackgroundColor",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "Color ('#' start)",
                  prop: "pcBackgroundColor",
                  model: "pcBackgroundColor",
                  placeholder: "Please enter PC version background color"
                }
              ]
            },
            {
              label: "Third language support", //第三语言字段
              value: "thirdLan",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "Label",
                  prop: "thirdLan",
                  model: "thirdLan",
                  placeholder: "Please enter a third language"
                }
              ]
            },
            {
              label: "Toggle menu layout", //新旧版menuPage(远古版本使用)
              value: "page",
              prefix: "System",
              nest: "monolayer", //声明单层结构
              formElement: [
                {
                  elementType: "select",
                  placeholder: "Please select the interface",
                  style: "width: 100%",
                  elementOption: [
                    {
                      label: "Old Page",
                      value: 1
                    },
                    {
                      label: "New Page",
                      value: 2
                    }
                  ]
                }
              ]
            },
            {
              label: "Update Item Ctrl", // 更新售罄food的数量
              value: "updateItemCtrl",
              prefix: "System",
              formElement: [
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Select Font Size", //控制页面字体大小
              value: "pageFontSize",
              prefix: "System",
              formElement: [
                {
                  elementType: "select",
                  style: "width: 100%",
                  label: "Default",
                  prop: "pageFontSize.default",
                  model: "default",
                  elementOption: [
                    {
                      label: "Normal",
                      value: "normal"
                    },
                    {
                      label: "Large",
                      value: "large"
                    },
                    {
                      label: "XLarger",
                      value: "xLarger"
                    }
                  ]
                },
                {
                  elementType: "checkbox",
                  elementGroupClass: "fontSize-checkBox-group",
                  elementChkClass: "fontSize-checkBox",
                  placeholder: "Please select the interface",
                  prop: "pageFontSize.fontSizeType",
                  model: "fontSizeType",
                  elementOption: [
                    {
                      label: "Normal",
                      value: "normal"
                    },
                    {
                      label: "Large",
                      value: "large"
                    },
                    {
                      label: "XLarger",
                      value: "xLarger"
                    }
                  ]
                }
              ]
            },

            {
              label: "Staff Mode Login Method", // 员工帮忙点餐模式
              value: "StaffMode",
              prefix: "System",
              formElement: [
                {
                  elementType: "radio",
                  label: "Login Model",
                  prop: "StaffMode.loginModel",
                  model: "loginModel",
                  radioOptions: [
                    {
                      label: "Account/Password",
                      value: "1"
                    },
                    {
                      label: "Password",
                      value: "2"
                    }
                  ]
                },
                {
                  elementType: "inputNumber",
                  label: "Default Pax",
                  prop: "StaffMode.pax", //员工模式默认人数自动开台
                  model: "pax",
                  min: 0,
                  placeholder: "Please enter default pax"
                },

                {
                  elementType: "input",
                  label: "Food Code", //用于后端加某个数据鉴别员工点餐
                  prop: "StaffMode.addFoodCode",
                  model: "addFoodCode",
                  placeholder: "Please enter food code",
                  showConditions: "StaffMode.loginModel == '1'"
                },
                {
                  elementType: "input",
                  label: "Password", //用于员工登录密码
                  prop: "StaffMode.password",
                  model: "password",
                  placeholder: "Please enter password",
                  showConditions: "StaffMode.loginModel == '2'"
                },
                {
                  elementType: "switch",
                  label: "Show In History", //后端使用,开启则历史订单显示员工名字
                  prop: "StaffMode.showInHistory",
                  model: "showInHistory",
                  showConditions: "StaffMode.loginModel == '1'"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Display Menu Popup", //闲置一段时间后显示宣传图弹窗
              value: "menuPopup",
              prefix: "System",
              formElement: [
                {
                  elementType: "radio",
                  label: "Mode",
                  model: "mode",
                  radioOptions: [
                    {
                      label: "Random",
                      value: "Random"
                    },
                    {
                      label: "Carousel",
                      value: "Carousel"
                    }
                  ]
                },
                {
                  elementType: "input",
                  label: "Timeout",
                  prop: "menuPopup.timeout",
                  model: "timeout",
                  valueType: "number", //规定输出数据类型为number
                  placeholder: "Please enter idle time",
                  oninput: "value=value.replace(/^0|[^0-9]/g, '')",
                  slotEnd: "second"
                },
                {
                  elementType: "input",
                  label: "Carousel Interval",
                  prop: "menuPopup.carouselInterval",
                  model: "carouselInterval",
                  valueType: "number", //规定输出数据类型为number
                  placeholder: "Please enter a carousel interval",
                  showConditions: "menuPopup.mode == 'Carousel'",
                  oninput: "value=value.replace(/^0|[^0-9]/g, '')",
                  slotEnd: "second"
                },

                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Re-direct to food cat on startup", // 配置Code进入fty页或者点餐页滚动到对应fty位置
              value: "redirectAfterLogin",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "Code",
                  prop: "redirectAfterLogin",
                  model: "redirectAfterLogin",
                  placeholder: "Please enter code"
                }
              ]
            },
            {
              label: "Aliyun SMS (Chinese Mainland)", // 配置SMS短信服务
              value: "aliyunSMS",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "Access Key Encrypt",
                  prop: "aliyunSMS.accessKeyEncrypt",
                  model: "accessKeyEncrypt",
                  placeholder: "Please enter access key encrypt"
                },
                {
                  elementType: "cascader",
                  label: "Region",
                  prop: "aliyunSMS.hierarchicalList",
                  model: "hierarchicalList",
                  placeholder: "Please enter region option",
                  options: [], // 初始为空数组，稍后会被填充
                  change: "smsRegionChange"
                },
                {
                  elementType: "input",
                  label: "Sign Name",
                  prop: "aliyunSMS.signName",
                  model: "signName",
                  placeholder: "Please enter sign name"
                },
                {
                  elementType: "input",
                  label: "Template Code",
                  prop: "aliyunSMS.templateCode",
                  model: "templateCode",
                  placeholder: "Please enter template code"
                },
                {
                  elementType: "inputNumber",
                  label: "Valid Time",
                  prop: "aliyunSMS.validTime",
                  model: "validTime",
                  min: 1,
                  suffixName: "second",
                  placeholder: "default 300s"
                },
                {
                  elementType: "inputNumber",
                  label: "Countdown",
                  prop: "aliyunSMS.countdown",
                  model: "countdown",
                  suffixName: "second",
                  min: 60,
                  placeholder: "default 60s"
                },
                {
                  elementType: "inputNumber",
                  label: "Daily Maximum Send (per number)",
                  prop: "aliyunSMS.daySendMax",
                  model: "daySendMax",
                  suffixName: "times",
                  min: 0,
                  placeholder: "Please enter day send max"
                },

                {
                  elementType: "input",
                  label: "Exclude Number",
                  prop: "aliyunSMS.excludeNumber",
                  model: "excludeNumber",
                  placeholder: "Please enter exclude number (Use ; to separate multiple)"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Aliyun SMS (Globe)", // 配置SMS短信服务
              value: "aliyunSMSGlobe",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "Access Key Encrypt",
                  prop: "aliyunSMSGlobe.accessKeyEncrypt",
                  model: "accessKeyEncrypt",
                  placeholder: "Please enter access key encrypt"
                },
                {
                  elementType: "cascader",
                  label: "Region",
                  prop: "aliyunSMSGlobe.hierarchicalList",
                  model: "hierarchicalList",
                  placeholder: "Please enter region option",
                  options: [], // 初始为空数组，稍后会被填充
                  change: "smsRegionChange"
                },
                {
                  elementType: "input",
                  label: "Sender ID",
                  prop: "aliyunSMSGlobe.senderId",
                  model: "senderId",
                  placeholder: "Please enter sender id"
                },
                {
                  elementType: "input",
                  label: "Exclude Number",
                  prop: "aliyunSMSGlobe.excludeNumber",
                  model: "excludeNumber",
                  placeholder: "Please enter exclude number (Use ; to separate multiple)"
                },
                {
                  elementType: "inputNumber",
                  label: "Valid Time",
                  prop: "aliyunSMSGlobe.validTime",
                  model: "validTime",
                  min: 1,
                  suffixName: "second",
                  placeholder: "default 300s"
                },
                {
                  elementType: "inputNumber",
                  label: "Countdown",
                  prop: "aliyunSMSGlobe.countdown",
                  model: "countdown",
                  suffixName: "second",
                  min: 1,
                  placeholder: "default 60s"
                },
                {
                  elementType: "inputNumber",
                  label: "Daily Maximum Send (per number)",
                  prop: "aliyunSMSGlobe.daySendMax",
                  model: "daySendMax",
                  suffixName: "times",
                  min: 0,
                  placeholder: "Please enter day send max"
                },

                {
                  elementType: "textarea",
                  label: "Message",
                  prop: "aliyunSMSGlobe.message",
                  model: "message",
                  placeholder: 'Please enter content including "#code"'
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Verify repeated order",
              value: "foodRepeatVerify",
              prefix: "System"
            },
            {
              label: "Email Sender Management",
              value: "emailSenderManagement",
              prefix: "System",
              formElement: [
                {
                  elementType: "input",
                  label: "Email",
                  model: "email",
                  prop: "emailSenderManagement.email",
                  placeholder: "Please enter the email"
                },
                {
                  elementType: "input",
                  label: "Sender Name",
                  model: "senderName",
                  prop: "emailSenderManagement.senderName",
                  placeholder: "Please enter the sender name"
                },
                {
                  elementType: "radio",
                  label: "Smtp Host",
                  model: "smtpRadio",
                  prop: "emailSenderManagement.smtpRadio",
                  className: "smtpRadio",
                  radioOptions: [
                    {
                      label: "GMAIL",
                      value: "smtp.gmail.com"
                    },
                    {
                      label: "DIRECT MAIL",
                      value: "smtpdm-ap-southeast-1.aliyun.com"
                    },
                    {
                      label: "",
                      radioElement: "input",
                      value: "smtpDiy",
                      model: "smtpDiy",
                      prop: "emailSenderManagement.smtpDiy",
                      placeholder: "Please enter smtp host"
                    }
                  ]
                },
                {
                  elementType: "inputNumber",
                  label: "Captcha expiration time",
                  model: "validTime",
                  prop: "validTime",
                  min: 1,
                  suffixName: "second",
                  placeholder: "Please enter the member type"
                },
                {
                  elementType: "input",
                  label: "Authorization code",
                  model: "authorizationCode",
                  prop: "emailSenderManagement.authorizationCode",
                  placeholder: "Please enter code"
                },
                {
                  elementType: "input",
                  label: "Exclude Number",
                  prop: "emailSenderManagement.excludeNumber",
                  model: "excludeNumber",
                  placeholder: "Please enter exclude number (Use ; to separate multiple)"
                }
              ]
            },

            {
              label: "Order History Query Timeout", // 历史订单查询时间范围
              value: "ohqTimeout",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "input",
                  label: "Query Timeout",
                  prop: "ohqTimeout",
                  model: "ohqTimeout",
                  valueType: "number", //规定输出数据类型为number
                  placeholder: "Please enter default time horizon",
                  oninput: "value=value.replace(/^0|[^0-9]/g, '')",
                  slotEnd: "minute"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Force Sales Mode", //固定performType模式
              value: "forceSalesMode",
              nest: "monolayer", //声明单层结构
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "radio",
                  label: "Force Sales Mode",
                  model: "forceSalesMode",
                  radioOptions: [
                    {
                      label: "MD",
                      value: "MD"
                    },
                    {
                      label: "FB",
                      value: "FB"
                    }
                  ]
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Distance Alert", //固定performType模式
              value: "distanceAlert",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "input",
                  label: "Distance Alert",
                  prop: "distanceAlert",
                  valueType: "number", //规定输出数据类型为number
                  placeholder: "Please enter a distance",
                  oninput: "value=value.replace(/^0|[^0-9]/g, '')",
                  slotEnd: "meter"
                }
              ]
            },
            {
              label: "Additional items for takeaway", // 是否需要餐具
              value: "additionalItemsForTakeaway",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "input",
                  label: "Codes(checkbox)",
                  model: "auto",
                  placeholder: "Please enter the cutlery codes, Use ; to separate multiple "
                },
                {
                  elementType: "input",
                  label: "Codes(quantity)",
                  placeholder: "Please enter the cutlery codes, Use ; to separate multiple ",
                  model: "fixed"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Pickup order time intervals", // 自提时间组件配置(日/时/分)
              value: "pickupTime",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "input",
                  label: "Pickup Day Range",
                  prop: "pickupTime.pickupDayRange",
                  model: "pickupDayRange",
                  valueType: "number", //规定输出数据类型为number
                  placeholder: "Please enter the number of dates displayed",
                  oninput: "value=value.replace(/^0|[^0-9]/g, '')",
                  slotEnd: "days"
                },
                {
                  elementType: "input",
                  label: "Pickup Time Interval",
                  prop: "pickupTime.pickupTimeInterval",
                  model: "pickupTimeInterval",
                  valueType: "number", //规定输出数据类型为number
                  placeholder: "Please enter a time interval",
                  oninput: "value=value.replace(/^0|[^0-9]/g, '')",
                  slotEnd: "minute"
                },
                {
                  elementType: "input",
                  label: "Advance Order Minutes",
                  model: "advanceOrderMinutes",
                  prop: "pickupTime.advanceOrderMinutes",
                  valueType: "number", //规定输出数据类型为number
                  placeholder: "Please enter the time to place your order in advance",
                  oninput: "value=value.replace(/^0|[^0-9]/g, '')",
                  slotEnd: "minute",
                  disable: "disabledAdvanceOrderMinutes" // disable现在只适配了el-input
                },
                {
                  elementType: "input",
                  label: "Advanced Orders prints KP immediately",
                  prop: "pickupTime.printsKPImmediately",
                  model: "printsKPImmediately",
                  placeholder: "Please enter a Food Code, multiple codes are prohibited.",
                  oninput: "value=value.replace(/[;,@!%^&*()+=]/g, '')",
                  disable: "disabledOrderKPImmediately"
                },
                {
                  elementType: "input",
                  label: "Today KPName",
                  model: "todayKPName",
                  placeholder: "today KPName En",
                  slotEnd: "+ HH:mm-HH:mm",
                  disable: "disabledKPName"
                },
                {
                  elementType: "input",
                  label: "Today KPName2",
                  model: "todayKPName2",
                  placeholder: "today KPName2 Zh",
                  slotEnd: "+ HH:mm-HH:mm",
                  disable: "disabledKPName"
                },
                {
                  elementType: "mainSwitch",
                  label: "Advance Pickup",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Promotion codes", // 优惠券配置(开启/关闭优惠券功能)
              value: "promotionDiscount",
              prefix: "MO&P"
            },
            {
              label: "Take away map config", //google map 配置
              value: "mapConfig",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Map load timeout",
                  model: "timeout",
                  min: 1,
                  placeholder: "default 5s",
                  suffixName: "second"
                },
                {
                  elementType: "switch",
                  label: "Disable google map",
                  model: "disableMap"
                }
              ]
            },
            {
              label: "Take away map search scope", // 外卖定位距离定位
              value: "shopMapSearchScope",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "Distance",
                  prop: "shopMapSearchScope.scope",
                  model: "scope",
                  min: 1,
                  precision: 0,
                  suffixName: "km"
                }
              ]
            },
            {
              label: "Take away packing", // 打包盒
              value: "takeAwayPackaging",
              prefix: "MO&P"
            },
            {
              label: "Turn off Takeaway shop photo", //  是否显示隐藏takeaway确认订单时的店铺照片, true:关闭
              value: "closeTakeawayShopPhoto",
              prefix: "MO&P"
            },
            {
              label: "Must input name (MO&P)", // 下单必填填写姓名
              value: "nameMandatory",
              prefix: "MO&P"
            },
            {
              label: "Must input phone number(MO&P)", // 下单填写手机联系方式
              value: "logCustomerPhone",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "switch",
                  label: "Mobile number(required)",
                  model: "phoneRequired"
                },
                {
                  elementType: "mainSwitch",
                  label: "Mobile number(display)",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Must input email(MO&P)", // 下单必填填写邮箱
              value: "emailMandatory",
              prefix: "MO&P"
            },
            {
              label: "Dual mode MO&P", // 选择堂食/外卖用餐方式
              value: "diningStyle",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "input",
                  label: "Trigger Table Number",
                  model: "diningStyle",
                  placeholder: "Please enter table number,Use ; to separate multiple"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Set Table Number for Dine-in", //设置堂食的tableNumber/若包含则顶部导航栏不显示台号显示"堂食/外卖"文字
              value: "dineInStoreNumber",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "input",
                  label: "Table Number",
                  model: "dineInStoreNumber",
                  placeholder: "Please enter table number,Use ; to separate multiple"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  model: "switchVal"
                }
              ]
            },

            {
              label: "AliPay / WeChat browser restrictions", //限制部分浏览器是否放行(配置的浏览器扫码则不能打开)
              value: "browserRestrictions",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "checkbox",
                  label: "Browser category",
                  prop: "browserRestrictions.category",
                  model: "category",
                  elementOption: [
                    {
                      label: "AliPay",
                      value: "AliPay"
                    },
                    {
                      label: "WeChat",
                      value: "WeChat"
                    }
                  ]
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Check FBTrans Before Payment", //FB下单时检测FB是否开启再让客服进入支付
              value: "checkFBTransBeforePayment",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "inputNumber",
                  label: "TimeOut(s)",
                  prop: "checkFBTransBeforePayment.timeOut",
                  model: "timeOut",
                  min: 0,
                  suffixName: "second"
                },
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            },
            {
              label: "Table number after FBTrans Ref Num", //柜台支付付款编号后面加上桌号
              value: "tableNumberAfterFBTransRefNum",
              prefix: "MO&P",
              formElement: [
                {
                  elementType: "mainSwitch",
                  label: "Enable",
                  prop: "switchVal",
                  model: "switchVal"
                }
              ]
            }
            // {
            //   label: "LoginByStaff",
            //   value: "loginByStaff"
            // },

            // {
            //   label: "LimitByFcode", // 全局限制food数量超出提示语(三语言)
            //   value: "limitByFcode"
            // },
          ],

          translCurrency: {
            usd: "USD",
            gbp: "GBP",
            cny: "CNY",
            hkd: "HKD",
            eur: "EUR",
            jpy: "JPY",
            chf: "CHF",
            cad: "CAD",
            myr: "MYR",
            twd: "TWD",
            nzd: "NZD"
          },

          delsubForm: {
            sign: "",
            restrictions2: "",
            store_number: ""
          },
          tableData: [],
          oldEditForm: {
            oldNumber: "",
            oldtype: ""
          },
          fixStoreNumber: [
            "saveVersionNumber",
            "errorEmailConfig",
            "mTypeCodeLength",
            "delayRefreshCacheTime",
            "mapConfig",
            "foodCourt"
          ], //固定"*"的storeNumber
          timeZoneList: [],
          SMSRegionList: [],
          unShowSwitchList: ["animation", "memberConfig", "priceWay"] // 表格上不显示的switch类型
          // initTinymceTypeArr: ["displayCRM"]
        },
        created() {
          this.getTimeZoneList()
          this.getData()
          this.loadSMSRegionList()
          let windowHeight = document.documentElement.clientHeight || document.bodyclientHeight
          this.tableHeight = windowHeight - 60 //数值"140"根据需要调整\
          this.retain = JSON.parse(JSON.stringify(this.addForm))
        },
        mounted() {
          //  监听屏幕变化,当宽度小于常规笔记本页面宽度时,给el-dialog的宽度赋值
          this.initDialogWidth()
        },
        watch: {
          "addForm.type"(newVal, oldVal) {
            //解决固定的storeNumber切换别的type的问题
            if (this.fixStoreNumber.includes(oldVal)) {
              this.addForm.storeNumber = ""
            }
            // if (this.initTinymceTypeArr.includes(newVal)) {
            //   this.tinymceInit(newVal)
            // }
          },
          "addForm.pickupTime": {
            deep: true,
            handler() {
              this.$nextTick(() => {
                const list = ["Advance Order Minutes", "Advanced Orders prints KP immediately"]
                this.$refs["addForm"].$children
                  .filter(it => list.includes(it.label))
                  .forEach(it => it.clearValidate())
              })
            }
          },
          "editForm.pickupTime": {
            deep: true,
            handler() {
              this.$nextTick(() => {
                const list = ["Advance Order Minutes", "Advanced Orders prints KP immediately"]
                this.$refs["editForm"].$children
                  .filter(it => list.includes(it.label))
                  .forEach(it => it.clearValidate())
              })
            }
          }
        },
        methods: {
          unShowSwitch(type) {
            return this.unShowSwitchList.includes(type)
          },
          generateSelectList(item) {
            if (Array.isArray(item)) {
              return item
            }
            if (typeof item === "string") {
              return this[item]
            }
          },
          getTimeZoneList() {
            $.getJSON("../../static/js/cms/timezone.json", res => {
              this.timeZoneList = Object.freeze(res)
            })
          },
          showParseObj(obj) {
            if (!obj) return
            let str = ""
            const { en, zh, thirdLan, ...rest } = obj
            if (Object.keys(rest).length > 0) {
              const values = Object.values(rest)
              str += `${values.join(" | ")}`
            }
            //判断str不为空加上br
            if (str) str += "<br>"
            if (en && zh && thirdLan) {
              str += `${en} | ${zh} | ${thirdLan}`
            }
            return str
          },
          parseKeepDecimals(e) {
            if (!e) return
            this.keepDecimals = e
            return e
          },

          getData() {
            let data = {
              domain: this.domain
            }
            $.get({
              url: "../../manager_UI_config/getAll",
              data,
              success: res => {
                if (res.RESULT_CODE != 0) {
                  this.$message.error("Query data error！")
                } else {
                  res.list.sort((a, b) => {
                    return a["storeNumber"].localeCompare(b["storeNumber"])
                  })
                  // 处理value成对象数据
                  let array = res.list.map(item => {
                    if (item.value) {
                      try {
                        item.value = JSON.parse(item.value)
                      } catch (e) {
                        item.value = item.value
                      }
                    }
                    return item
                  })
                  this.tableData = array
                }
              },
              error: function (error) {
                console.log(error)
                // this.$message.error('Fail to load！');
              }
            })
          },
          async checkSpecialFrom(type, form) {
            const dfPms = () => {
              return new Promise(res => res())
            }
            //获取当前表单的实例,调用validate方法
            switch (type) {
              case "orderTimeLimit": {
                // 获取组件内的ref实例调用
                let outRef = this.$refs["order-timer-form"][0]
                let ref = outRef.$refs["order-time-rule"]
                if (ref && Array.from(ref).length) {
                  let list = []
                  for (const refElement of ref) {
                    list.push(refElement.validate())
                  }
                  this[form][type] = outRef.ruleForms
                  return Promise.all(list)
                }
                return dfPms()
              }
              case "salesControl": {
                let outRef = this.$refs["sales-control-form"][0]
                let rule = outRef.ruleForms
                this[form][type] = {
                  ...this[form][type],
                  rule
                }
                return dfPms()
              }
              case "emailSenderManagement": {
                let form = this.addDialogVisible ? "addForm" : "editForm"
                let { smtpRadio, smtpDiy, smtpHost } = this[form].emailSenderManagement
                if (this.isOtherSmtpHost(form)) {
                  this[form].emailSenderManagement.smtpHost = smtpRadio
                } else {
                  this[form].emailSenderManagement.smtpHost = smtpDiy
                }
                return dfPms()
              }
              case "memberAccountManagement": {
                let form = this.addDialogVisible ? "addForm" : "editForm"
                this.$refs[form].clearValidate("memberAccountManagement.memberExpRadio")
                this.$refs[form].clearValidate("memberAccountManagement.pointExpRadio")
                return dfPms()
              }
              case "priceWay": {
                let data = this.$refs["select-pos-price"][0].submitPosPriceForm()
                if (data) {
                  this[form][type] = data
                  return dfPms()
                } else {
                  return new Promise((resolve, reject) => reject("priceWay校验不通过"))
                }
              }
              default:
                return dfPms()
            }
          },
          async onAdd(addForm) {
            await this.checkSpecialFrom(this.addForm.type, addForm)
            let config = this.addForm
            let data = {
              storeNumber: config.storeNumber,
              switchVal: config.switchVal,
              type: config.type,
              value:
                config[config.type] instanceof Object
                  ? JSON.stringify(config[config.type])
                  : config[config.type],
              domain: this.domain
            }
            if (this.switchValType.includes(config.type)) {
              delete data.value
            } else {
              // delete data.switchVal
            }
            let res = this.unique(data.storeNumber, data.type)
            if (res) return
            this.$refs[addForm].validate(valid => {
              if (valid) {
                $.post({
                  url: "../../manager_UI_config/addOne",
                  data,
                  dataType: "json",
                  success: res => {
                    if (res.RESULT_CODE !== 0) {
                      this.$message.error("Fail to add！")
                    } else {
                      console.log(this.addForm, "addForm")
                      this.getData()
                      this.$message.success("Successfully added！")
                      this.addDialogVisible = false
                    }
                  },
                  error: error => {
                    this.$message.error("Fail to add！")
                  }
                })
              } else {
                this.$message.error("Fail to add！")
              }
            })
          },
          onEdit(index, row) {
            row = JSON.parse(JSON.stringify(row)) //脱离响应
            let initForm = JSON.parse(JSON.stringify(this.retain))
            let { id, storeNumber, type, switchVal, value } = row
            // console.log(row, value, "编辑数据")
            //解决新增字段但数据返回的数据缺失字段补充完整value数据
            let completeValue =
              typeof value === "object" ? { ...initForm[row.type], ...value } : value
            if (type === "memberConfig") {
              // 若没有takeawayDiscountSwitch字段,则跟discountSwitch的值一致
              if (!("takeawayDiscountSwitch" in value)) {
                completeValue.takeawayDiscountSwitch = completeValue.discountSwitch ?? true
              }
            }
            if (type === "emailSenderManagement") {
              if (completeValue.smtpDiy) {
                completeValue.smtpRadio = "smtpDiy"
              }
            }
            this.editForm = {
              id,
              storeNumber,
              type,
              // 增加判断是否符合多类型数据
              switchVal,
              [row.type]: completeValue
            }
            console.log(this.editForm, "editForm")

            this.oldEditForm = {
              oldNumber: row.storeNumber,
              oldtype: row.type
            }

            this.editDialogVisible = true
          },
          async subEdit() {
            await this.checkSpecialFrom(this.editForm.type, "editForm")
            let config = this.editForm
            let data = {
              id: config.id,
              storeNumber: config.storeNumber,
              switchVal: config.switchVal,
              type: config.type,
              value:
                config[config.type] instanceof Object
                  ? JSON.stringify(config[config.type])
                  : config[config.type],
              domain: this.domain
            }
            if (this.switchValType.includes(config.type)) {
              delete data.value
            } else {
              // delete data.switchVal
            }
            if (this.editUnique(data.storeNumber, data.type)) return
            // console.log(data, 'data')
            this.$refs.editForm.validate(valid => {
              if (valid) {
                $.post({
                  url: "../../manager_UI_config/updateOne",
                  data,
                  dataType: "json",
                  success: res => {
                    if (res.RESULT_CODE != 0) {
                      this.$message.error("Edit failure！")
                    } else {
                      console.log("修改", res)
                      this.getData()
                      this.$message.success("Edit success！")
                      this.editDialogVisible = false
                    }
                  },
                  error: res => {
                    // console.log(res);
                    this.$message.error("Edit failure！")
                  }
                })
              } else {
                this.$message.error("Form validation failed !")
              }
            })
          },
          onDel(index, row) {
            let { id, storeNumber, type, switchVal, domain } = row
            let data = {
              storeNumber,
              domain,
              id: row.id,
              type: row.type
            }
            $.post({
              url: "../../manager_UI_config/deleteOne",
              data,
              dataType: "json",
              success: res => {
                if (res.RESULT_CODE != 0) {
                  this.$message.error("Fail to delete!")
                } else {
                  console.log("删除", res)
                  this.getData()
                  this.$message.success("Successfully delete!")
                }
                // console.log(res);
              },
              error: res => {
                // console.log(res);
                this.$message.error("Fail to delete!")
              }
            })
          },
          // 开关触发
          onEditSwitch($event, row, tip) {
            let { id, storeNumber, type, switchVal, value } = row
            // 针对某些type类型数据处理(不规则类型数据需要全部传过去,包括开关及内容)
            let conversionValue = ""
            if (tip) {
              conversionValue = JSON.stringify(this.keepDecimals)
            } else if (this.irregularityConfig.includes(type)) {
              conversionValue = JSON.stringify(value)
            }
            let data = {
              id: id,
              storeNumber: storeNumber,
              type: type,
              value: conversionValue || value,
              switchVal: switchVal,
              domain: this.domain
            }
            console.log(data, "data")
            if (this.switchValType.includes(type)) {
              delete data.value
            } else {
              // delete data.switchVal
            }
            let that = this
            $.post({
              url: "../../manager_UI_config/updateOne",
              data,
              dataType: "json",
              success: function (res) {
                if (res.RESULT_CODE != 0) {
                } else {
                  console.log("开关修改", res)
                  that.getData()
                  that.$message.success("Edit success！")
                }
              },
              error: function (res) {
                // console.log(res);
                that.$message.error("Edit failure！")
              }
            })
          },

          // 添加对话框关闭��件
          onDiaCloseDialog(e, str) {
            // 关闭弹窗不再重置表单,直接替换数据清除校验;resetFields()出现某些数据错误

            let strForm = `${str}Form`
            this[strForm] = JSON.parse(JSON.stringify(this.retain))
            if (str == "add") {
              this.$nextTick(() => {
                this.$refs["addForm"].clearValidate()
              })
            }

            this.rules.StaffMode.addFoodCode[0].required = true //恢复必填
          },

          unique(storeNumber, type) {
            let tableData = this.tableData
            let res = false
            if (tableData.length != 0) {
              for (let i = 0; i < tableData.length; i++) {
                const item = tableData[i]
                if (item.storeNumber == storeNumber && item.type == type) {
                  this.$message.error("Do not duplicate configuration！")
                  res = true
                  break
                }
              }
            }
            return res
          },
          editUnique(newNumber, Newtype) {
            let tableData = this.tableData
            let { oldNumber, oldtype } = this.oldEditForm
            let res = false
            if (tableData.length != 0) {
              // storeNumber或者type有改动情况下判断
              if (newNumber != oldNumber || Newtype != oldtype) {
                for (let i = 0; i < tableData.length; i++) {
                  const item = tableData[i]
                  if (item.storeNumber == newNumber && item.type == Newtype) {
                    this.$message.error("Do not duplicate configuration！")
                    res = true
                    break
                  }
                }
              }
            }
            return res
          },
          // 初步根据需求封装不规则类别显示信息,后期再完善
          showIrregularityConfig(row, type = "txt") {
            if (!row.value) return
            let str = ""
            let lanTxt = {
              en: "FirstLan",
              zh: "SecondLan",
              thirdLan: "ThirdLan"
            }
            let val = this.filterComplexRow(row) //过滤不需要显示的数据
            for (const key in val) {
              let itemName = lanTxt[key] || key
              // 默认获取的是文字/数字类型
              if (type == "txt" && typeof val[key] !== "boolean") {
                str += itemName + `:` + JSON.stringify(val[key]) + `<br /><br />`
              } else if (type == "boolean" && typeof val[key] === "boolean") {
                // 返回一个对象提供遍历用
                typeof str === "string" ? (str = {}) : ""
                str[itemName] = val[key]
              }
            }
            return str
          },
          // 移除校验结果
          clearVerify() {
            this.$refs["addForm"].clearValidate()
            // 判断选中type是否需要固定storeNumber
            let type = this.addForm.type
            if (this.fixStoreNumber.includes(type)) {
              this.addForm.storeNumber = "*"
            }
            //  else {
            //   this.addForm.storeNumber = ""
            // }
          },
          // 检查是否是是嵌套数据
          checkNestingData(type) {
            let form = this.addDialogVisible ? this.addForm : this.editForm
            let data = form[type]
            // console.log(data, Object.prototype.toString.call(data) === "[object Object]", 555)
            return Object.prototype.toString.call(data) === "[object Object]"
          },
          /**
           * @description: 此方法用于input框检查数据类型是否符合,不符合则转化
           * @param {String} form 表单名称
           * @param {String} typeValue 表单中的数据名称
           * @param {String} elModel 表单中的数据名称(用于区分嵌套数据)
           * @param {String} type formElement配置的数据类型
           * @return {*}
           */
          // 检查数据类型是否符合,不符合则转化
          onBlur(form, typeValue, elModel, type = "txt") {
            if (type === "txt") return
            let value = elModel ? this[form][typeValue][elModel] : this[form][typeValue]
            if (type === "number" && value !== "" && typeof value !== "number") {
              value = Number(value)
              if (elModel) {
                this[form][typeValue][elModel] = value
              } else {
                this[form][typeValue] = value
              }
            }
          },
          handleCheckboxChange(value, form, type) {
            switch (type) {
              case "pageFontSize":
                this.pageFontSizeCheckBoxChange(value, form)
                break
              default:
                break
            }
          },
          pageFontSizeCheckBoxChange(value, form) {
            // 封装
            let type = this[form].pageFontSize.fontSizeType
            if (!type.includes(value)) {
              this[form]["pageFontSize"][value] = {
                sizeType: value,
                firstLan: "",
                secondLan: "",
                thirdLan: ""
              }
              // 清空校验结果
              this.$refs[form].clearValidate()
            }
            const arr = ["normal", "large", "xLarger"]
            // 将对应form中的fontSizeType数据按arr顺序排列
            this[form]["pageFontSize"]["fontSizeType"] = arr.filter(item => {
              return type.includes(item)
            })
          },
          // 处理弹窗宽度
          initDialogWidth() {
            if (document.body.clientWidth < 1300) {
              this.dialogWidth = "60%"
            }
            window.onresize = () => {
              return (() => {
                if (document.body.clientWidth < 1300) {
                  this.dialogWidth = "60%"
                } else {
                  this.dialogWidth = "45%"
                }
              })()
            }
          },
          //添加/编辑弹窗中的单选框组件触发事件
          handleRadioChange(e, val, model) {
            let type = val ? `${val}.${model}` : model
            let form = this.addDialogVisible ? "addForm" : "editForm"
            switch (type) {
              case "StaffMode.loginModel":
                this.changeSMFcodeRequired(e)
                break
              case "emailSenderManagement.smtpRadio":
                this[form].emailSenderManagement.smtpHost = e
                this.removeCustomEmailData(e)
                break

              default:
                break
            }
          },
          //清除自定义邮件字段数据
          removeCustomEmailData(e) {
            let form = this.addDialogVisible ? "addForm" : "editForm"
            if (e !== "smtpDiy") {
              this.$nextTick(() => {
                this.$refs[form].clearValidate("emailSenderManagement.smtpDiy")
                this[form].emailSenderManagement.smtpDiy = ""
              })
            }
          },

          changeSMFcodeRequired(e) {
            //重置校验状态
            let form = this.addDialogVisible ? "addForm" : "editForm"
            this.$refs[form].clearValidate()
            let { addFoodCode } = this.rules.StaffMode
            addFoodCode[0].required = e == 1
          },

          //过滤要隐藏的复杂形数据(某些dom需要根据条件显示)
          filterComplexRow(row) {
            switch (row.type) {
              case "StaffMode":
                if (!row.value.hasOwnProperty("loginModel")) {
                  return row.value //兼容旧数据
                } else if (row.value.loginModel == 1) {
                  let { password, ...data } = row.value
                  return data
                } else {
                  let { addFoodCode, showInHistory, ...data } = row.value
                  return data
                }
              case "menuPopup":
                if (row.value.mode == "Carousel") {
                  let { switchVal, ...data } = row.value
                  return data
                } else {
                  let { switchVal, carouselInterval, ...data } = row.value
                  return data
                }
              default:
                return row.value
            }
          },
          getLabelOrModel(type, element) {
            return this.checkNestingData(type.value) ? element.label : type.label
          },
          getPropOrValue(type, element) {
            return this.checkNestingData(type.value) ? element.prop : type.value
          },
          getFormValue(type, element) {
            let form = this.addDialogVisible ? this.addForm : this.editForm
            return this.checkNestingData(type.value)
              ? form[type.value][element.model]
              : form[type.value]
          },
          setFormValue(type, element, newValue) {
            let form = this.addDialogVisible ? this.addForm : this.editForm
            if (this.checkNestingData(type.value)) {
              this.$set(form[type.value], element.model, newValue)
            } else {
              this.$set(form, type.value, newValue)
            }
          },
          loadSMSRegionList() {
            $.getJSON("../../static/SMSRegionList/index.json", data => {
              this.SMSRegionList = data
              // 更新 aliyunSMS 的 options
              this.updateAliyunSMSOptions()
            })
          },

          updateAliyunSMSOptions() {
            const aliyunSMSType = this.typeOption.find(item => item.value === "aliyunSMS")
            const aliyunSMSGlobeType = this.typeOption.find(item => item.value === "aliyunSMSGlobe")
            if (aliyunSMSType) {
              const cascaderElement = aliyunSMSType.formElement.find(
                element => element.model === "hierarchicalList"
              )
              if (cascaderElement) {
                cascaderElement.options = this.SMSRegionList
              }
            }
            if (aliyunSMSGlobeType) {
              aliyunSMSGlobeType.formElement.find(
                element => element.model === "hierarchicalList"
              ).options = this.SMSRegionList
            }
          },
          cascaderHandleChange(methodName, value) {
            if (typeof this[methodName] === "function") {
              this[methodName](value)
            } else {
              console.warn(`没有定义此方法`)
            }
          },
          smsRegionChange(value) {
            const selectedOption = this.findOption(this.SMSRegionList, value[1])
            // console.log(value, selectedOption, "选中的值")
            if (selectedOption) {
              const form = this.addDialogVisible ? "addForm" : "editForm"
              const formType = this[form].type
              this[form][formType] = {
                ...this[form][formType],
                region: selectedOption.region,
                endpointOverride: selectedOption.endpointOverride
              }
            }
          },
          findOption(options, value) {
            for (let option of options) {
              if (option.value === value) {
                return option
              }
              if (option.children) {
                const found = this.findOption(option.children, value)
                if (found) return found
              }
            }
            return null
          },
          /**
           * 处理单选框输入框获得焦点事件
           * @param {Event} event - 事件对象,包含了触发事件的元素信息
           * @param {String} configType - 配置类型值,用于标识当前操作的配置项(如 memberAccountManagement)
           * @param {String} fieldName - 数据模型字段名,用于标识具体要修改的属性(如 memberExpRadio/pointExpRadio)
           * @param {String|Number} selectedValue - 要设置的新值,表示单选框选中的值
           */
          handleRadioInputFocus(event, configType, fieldName, selectedValue) {
            let form = this.addDialogVisible ? "addForm" : "editForm"
            switch (fieldName) {
              case "smtpRadio":
                this[form][configType].smtpRadio = "smtpDiy"
                break
              case "memberExpRadio":
                this[form][configType].memberExpDate = null
                break
              case "pointExpRadio":
                this[form][configType].pointExpDate = null
                break
              default:
                break
            }
            this[form][configType][fieldName] = selectedValue
          },
          // 参数同上
          handleRadioInputBlur(e, configType, fieldName, selectedValue) {
            let form = this.addDialogVisible ? "addForm" : "editForm"
            switch (fieldName) {
              case "smtpRadio":
                this[form][configType].smtpRadio = "smtpDiy"
                this[form][configType].smtpHost = e
                break
              default:
                break
            }
          },
          handleRadioFormItemChange(configType, fieldName) {
            let form = this.addDialogVisible ? "addForm" : "editForm"
            this.$nextTick(() => {
              switch (configType) {
                case "memberAccountManagement":
                  this.$refs[form].clearValidate(`${configType}.${fieldName}`)
                  break
                default:
                  break
              }
            })
          },
          // 参数同上
          handleRadioDatePickerFocus(e, configType, fieldName, selectedValue) {
            let form = this.addDialogVisible ? "addForm" : "editForm"
            switch (fieldName) {
              case "memberExpRadio":
                this[form][configType].memberExpDay = null
                break
              case "pointExpRadio":
                this[form][configType].pointExpDay = null
                break
              default:
                break
            }
            this[form][configType][fieldName] = selectedValue
          },

          isOtherSmtpHost(form) {
            let { smtpRadio } = this[form].emailSenderManagement
            let res = ["smtp.gmail.com", "smtpdm-ap-southeast-1.aliyun.com"].includes(smtpRadio)
            return res
          }
        }
      })
    </script>
  </body>
</html>
