const SSEControlMixin = {
  data() {
    return {
      sseManager: null,
      SSEId: sessionStorage.getItem("sseid") || "", //sse建立连接的id,
      itemCtrlMap: {
        previous: {},
        current: {}
      }, // 每次SSE ItemCtrl的数据Map
      updateInventoryCodes: [], //需要更新库存的code
      updateItemCtrlDescendantsCodes: [], //需要更新库存的code的后代code
      updateItemCtrlCallID: null, // SSE更新ItemCtrl的调用ID,若未使用传给cancelIdleCallback可取消update
      danmuManager: null, //danmu 管理
      syncCartOffline: false, // 同步接口失败后的重试锁
      tryingAgainSyncCartTimer: null, //重试同步购物车的计时器
      delayedRetryTimer: null, // 延迟重试同步接口定时器
      cartHashRecord: [], //上一次购物车的更改记录hash
      syncCartCounter: parseInt(sessionStorage.getItem("syncCartCounter") || 0), // 同步购物车的操作计数器,每次添加数量都会更新
      sseCartVersion: sessionStorage.getItem("sseCartVersion"), // SSE同步购物车数据版本
      syncCartUpdateRecordList:
        JSON.parse(sessionStorage.getItem("syncCartUpdateRecordList")) || [], //将每次更新购物车的参数合并,防抖后发出请求
      cartUpdateTimer: null, //更新购物车定时器
      cartSyncLocked: false, //购物车同步锁
      sseCartSyncTaskQueue: [], //SSE回调队列
      sseCartSyncUpdateProcessing: false, //是否正在执行SSE回调
      cartSyncLoadQueue: new Set(), //正在请求请求的api接口的key
      mergeAfterSyncApi: false //同步购物车接口后是合并qty/idSet
    }
  },
  computed: {
    enableSSE() {
      return this.openTable.sseConnect
    },
    enableSSEItemCtrl() {
      return this.enableSSE && this.openTable.sseConnect.itemCtrl
    },
    enableSSECartSync() {
      return (
        this.enableSSE && this.openTable.sseConnect.shopCartSync && this.openTable.performType === 1
      )
    },
    mergeSameFoodOnDiffType() {
      return this.openTable.groupedFoodsByTypeCode
    }
  },
  watch: {
    syncCartUpdateRecordList(val) {
      sessionStorage.setItem("syncCartUpdateRecordList", JSON.stringify(val))
      // 正在重试延迟重试中,排队...
      if (this.delayedRetryTimer && this.delayedRetryTimer.isRunning) return
      // 网络已断开,正在重连中,排队...
      if (this.syncCartOffline) return
      this.cartUpdateDebounce(this.requestCartSync, 300)
    },
    // 模拟为网络连接状态,根据此状态来实现重试同步购物车接口逻辑
    syncCartOffline(val) {
      if (val) {
        this.sseManager && this.sseManager.disconnect()
        // 若网络断开,则10秒重试一次
        this.tryingAgainSyncCartTimer =
          this.tryingAgainSyncCartTimer || new AccurateTimer(this.reconnectSSE, 10000)
        this.tryingAgainSyncCartTimer.start()
      } else {
        this.tryingAgainSyncCartTimer && this.tryingAgainSyncCartTimer.stop()
      }
    }
  },
  methods: {
    // 保存一份hashRecord数据,用户对比是哪个food有更新
    initCartHashRecord() {
      this.cartHashRecord = JSON.parse(
        JSON.stringify(
          this.shopCartList.map(it => {
            return {
              record: it.hashRecord,
              data: {
                ...this.getFoodSelectedTree(it),
                index: it.index,
                deleteIdSet: it.deleteIdSet || [],
                addIdSet: it.addIdSet || []
              }
            }
          })
        )
      )
    },

    /**
     * 更新food数据的修改记录
     *
     * food的hashRecord为数组类型,记录了上一次修改与当前food的已选数据的hash值;
     * 此处用于记录food的更改记录用于购物车同步与SSE合并;
     * 扩展:可用于购物车add，edit的主food合并,及若购物车有hash与food的hash一致，则视为细项一致
     * */
    updateHashRecord(data) {
      if (!data) return null
      const tree = this.getOrderStructure(this.getFoodSelectedTree(data))
      const hash = this.fnv1a(stableStringify(tree))
      if (Array.isArray(data.hashRecord) && data.hashRecord.length) {
        // 若hash与qty1都一致,则没有任何修改,不更新hash
        const last = data.hashRecord[data.hashRecord.length - 1]
        if (last.hash === hash && last.qty1 === data.qty1) return
        // hashRecord最大长度为2，仅保留上一次和当前的hash
        data.hashRecord = [last, { hash, qty1: data.qty1 }]
      } else data.hashRecord = [{ hash, qty1: data.qty1 }]
    },
    // 获取最后操作购物车的记录
    getCartUpdateRecord() {
      const lastRecord = this.cartHashRecord.map(it => it.record)
      const currentRecord = this.shopCartList.map(it => it.hashRecord || [])
      const diff = (arr1, arr2) => {
        const arr1StrList = [...new Set(arr1.map(it => stableStringify(it)))]
        const arr2StrList = arr2.map(it => stableStringify(it))
        const arr1Diff = arr1.filter(it => {
          const str = stableStringify(it)
          return !arr2StrList.includes(str)
        }) //删除
        const arr2Diff = arr2.filter(it => !arr1StrList.includes(stableStringify(it))) //增加
        const validRecord = []
        // 依次判断Diff中的是增加/修改/删除
        for (const arr1Element of arr1Diff) {
          const lastItem = arr1Element[arr1Element.length - 1]
          // 判断再arr2中是否存在
          const record = arr2Diff.find(it => {
            const strList = it.map(it => stableStringify(it))
            return strList.includes(stableStringify(lastItem))
          })
          // 存在则可能是增加、减少、编辑,排除了删除
          if (record) {
            const [last, curr] = record
            // hash 与qty1一致,跳过
            if (last.hash === curr.hash && last.qty1 === curr.qty1) continue
            //type:add delete edit qty
            const result = {
              record
            }

            //hash一致,为数量有更改
            if (last.hash === curr.hash) result.type = "qty"
            else {
              result.type = "edit"
              // edit 需要保留编辑前的数据，接口需要
              const recordTree = this.cartHashRecord.find(
                it => stableStringify(it.record) === stableStringify(arr1Element)
              )
              if (!recordTree) continue
              result.data = recordTree.data
            }
            if (result.type) validRecord.push(result)
          } else {
            const recordTree = this.cartHashRecord.find(
              it => stableStringify(it.record) === stableStringify(arr1Element)
            )
            if (!recordTree) continue
            //只有删除有data,其余的再购物车可以拿到
            validRecord.push({
              type: "delete",
              data: recordTree.data,
              record: arr1Element
            })
          }
        }

        for (const arr2Element of arr2Diff) {
          if (arr2Element.length === 1) {
            //是新增的
            validRecord.push({
              type: "add",
              record: arr2Element
            })
          }
        }
        return validRecord
      }
      return diff(lastRecord, currentRecord)
    },
    // 获取food的最小化已选细项关系树,rec = 是否在递归内
    getFoodSelectedTree(foodData = null, rec = false) {
      if (!foodData) return null
      const codeKey = foodData.fCode ? "fCode" : "code"

      const result = {
        [codeKey]: foodData[codeKey]
      }

      if (rec) {
        // food细项内需要qty1字段，food自身qty1表现为addIdSet长度
        result.qty1 = foodData.qty1.toString()
      } else {
        //传递ftCode兼容不同type下相同food、细项的合并控制
        result.ftCode = foodData.ftCode
      }

      result.newOrderItemFoodList = this.getFoodTreeFormatHelper(foodData.newOrderItemFoodList)
      result.newOrderItemFoodTypeList = this.getFoodTreeFormatHelper(
        foodData.newOrderItemFoodTypeList
      )
      result.newOrderItemMListList = this.getFoodTreeFormatHelper(foodData.newOrderItemMListList)
      result.newOrderItemMTypeList = this.getFoodTreeFormatHelper(foodData.newOrderItemMTypeList)
      // 删除空列表的key
      for (const key in result) {
        if (Array.isArray(result[key]) && !result[key].length) {
          delete result[key]
        }
      }
      return result
    },
    // 处理orderItemList的数据
    getFoodTreeFormatHelper(list = []) {
      if (!list || !list.length) return []
      return list
        .sort((a, b) => a.finalSort - b.finalSort)
        .reduce((previous, current) => {
          const codeKey = current.fCode ? "fCode" : "code"
          const isType = current.code && !current.qty1
          const typeKey =
            isType && current.newOrderItemFoodList
              ? "newOrderItemFoodList"
              : "newOrderItemMListList"
          let result = {
            [codeKey]: current[codeKey]
          }
          if (isType) {
            result[typeKey] = this.getFoodTreeFormatHelper(current[typeKey])
          } else {
            result = this.getFoodSelectedTree(current, true)
          }
          previous.push(result)
          return previous
        }, [])
    },
    //fnv hash
    fnv1a(str) {
      let hval = 0x811c9dc5

      for (let i = 0; i < str.length; ++i) {
        hval ^= str.charCodeAt(i)
        hval += (hval << 1) + (hval << 4) + (hval << 7) + (hval << 8) + (hval << 24)
      }

      return (hval >>> 0).toString(16)
    },
    initDanmuLib() {
      if (!this.enableSSECartSync) return false
      if (!window.Danmu) return false
      if (this.danmuManager) {
        this.danmuManager.clear()
        this.danmuManager.unmount()
      }
      const { create } = window.Danmu
      const manager = create({
        trackHeight: "48%",
        plugin: {
          $createNode(danmaku) {
            danmaku.node.textContent = danmaku.data
          }
        },
        speed: 0.1
      })

      const styles = {
        "background-color": "#fff",
        color: "var(--styleColor, purple)", // 使用主题色或默认紫色
        padding: "5px 12px",
        "border-radius": "10px",
        "font-size": "0.35rem",
        "font-weight": "600",
        border: "1px solid rgba(0,0,0,0.05)",
        "box-shadow": "0 4px 12px rgba(0, 0, 0, 0.1)",
        width: "fit-content",
        "white-space": "nowrap"
      }

      for (const key in styles) {
        manager.setStyle(key, styles[key])
      }
      const container = document.querySelector(".danmu")
      manager.mount(container)
      manager.startPlaying()
      this.danmuManager = manager
    },
    // 同步购物车接口1102时重试逻辑
    delayedRetryCartSync() {
      this.delayedRetryTimer =
        this.delayedRetryTimer ||
        new RetryTimer(
          () => {
            return new Promise((resolve, reject) => {
              this.requestCartSync().then(res => {
                const { statusCode } = res || {}
                // 仍然是1102则继续重试
                if (statusCode === 1102) reject(false)
                else {
                  //其余任意情况都停止重试
                  resolve(true)
                  this.delayedRetryTimer.stop()
                  this.delayedRetryTimer = null
                }
              })
            })
          },
          {
            initialInterval: 3000, // 初始延迟3s
            backoffFactor: 1.5, // 之后递增延迟的倍率
            maxInterval: 60000, // 最大延迟时间1min
            maxRetries: 25, // 最大重试次数
            onRetry: (attempt, delay) => {
              console.log(`Code:1102,将在${delay / 1000}秒后进行第${attempt + 1}次重试`)
            }
          }
        )
      if (!this.delayedRetryTimer.isRunning) {
        this.delayedRetryTimer.start().catch()
      }
    },
    // 若list中包含errorType字段,modifiedByOther
    verifyDataConsistency(list = []) {
      if (!list || !list.length) return false
      const items = list.reduce((prev, curr) => {
        const include = curr.errorType && curr.errorType === "modifiedByOther"
        if (include) {
          const foodItem = this.findMainFoodByCode(
            this.allDataList.filter(it => it.code === curr.ftCode),
            curr.fCode
          )
          foodItem && prev.push(this.inListTitle(foodItem))
        }
        return prev
      }, [])
      if (items.length) {
        // 【】〔 〕「」 、，
        const title = `<span style="color: var(--styleColor)">
                        ${[...new Set(items)].join("、")}</span>`
        const tips = this.systemLanguage.editCartFoodHasUpdated.replace("#foodName", title)
        layer.alert(tips, {
          skin: "defaultLayer",
          btn: [this.systemLanguage.confirmBtn],
          title: false,
          // closeBtn: 0,
          yes: i => {
            layer.close(i)
            //关闭购物车编辑状态
            // this.cartEditXi = false
            // this.showFoodWarp = false
            // this.joinType = ""
          }
        })
      }
    },

    cartSyncApi(params) {
      return new Promise((resolve, reject) => {
        $.ajax({
          url: "../sse/shoppingCartSync",
          type: "POST",
          xhrFields: {
            responseType: "json"
          },
          headers: {
            "Content-Type": "application/json"
          },
          data: JSON.stringify(params),
          success: res => {
            switch (res.statusCode) {
              case 200:
                resolve(res)
                break
              case 400:
                //参数不符合条件
                break
              case 401: {
                //SSE未连接
                this.reconnectSSE()
                break
              }
              case 403:
                this.openTable.sseConnect.shopCartSync = false
                this.syncCartUpdateRecordList = []
                this.delayedRetryTimer = null
                this.cartHashRecord = []
                sessionStorage.setItem("openTable", JSON.stringify(this.openTable))
                break
              case 1101: {
                const version = res.data
                if (this.sseCartVersion !== version.toString()) {
                  this.syncCartUpdateRecordList = []
                  this.reconnectSSE()
                }
                break
              }
              case 1102: {
                //延迟重试此接口
                this.delayedRetryCartSync()
                break
              }
              default:
                //服务器错误 / 其余错误
                break
            }
            reject(res)
          },
          beforeSend: () => {
            this.cartSyncLoadQueue.add(params)
          },
          complete: () => {
            this.cartSyncLoadQueue.delete(params)
          },
          error: err => {
            reject(err)
          }
        })
        // setTimeout(() => {
        //   resolve()
        // }, 100)
      })
    },

    // 在此方法中发送http请求
    requestCartSync() {
      if (!this.enableSSE) return
      if (this.cartSyncLocked) return
      if (!!this.cartSyncLoadQueue.size) return // 如果正在请求，则不执行
      if (!this.syncCartUpdateRecordList || !this.syncCartUpdateRecordList.length) return
      // 始终拿到的是最新的操作记录,符合防抖合并
      const params = {
        data: JSON.parse(JSON.stringify(this.syncCartUpdateRecordList)),
        id: this.SSEId,
        version: this.sseCartVersion,
        storeNumber: this.openTable.storeNumber
      }
      return this.cartSyncApi(params)
        .then(r => {
          const { data: { data = [], version } = {} } = r
          //成功后才移除请求的参数
          this.syncCartUpdateRecordList = this.removeSyncRequestParams(params.data)
          // 版本不一致则跳过此更改
          if (version.toString() !== this.sseCartVersion) return
          // 若请求完成,list还有数据且还有请求,则不重置
          this.sseCartVersion = version.toString()
          //无效操作需要提示
          this.verifyDataConsistency(data)
          // 同步成功后，先合并正在请求的参数再diff,为了解决连续请求qty闪烁及合并滞后
          const mergedList = this.mergeRequestingRecordList(data)
          const diffRecord = this.diffRecordWithCart(mergedList)
          this.reorganizeCartByUpdateRecord(diffRecord)

          // update index and idSet
          this.updateCartIndexAndIdSet(data, false)
          this.initCartHashRecord()
          this.sendDanmu(diffRecord)
          // 若仍然有请求发送中,则后续都需要合并
          this.mergeAfterSyncApi = !!this.cartSyncLoadQueue.size
        })
        .catch(err => {
          // 网络断开,使用watch的断网重连逻辑
          this.syncCartOffline = !!(err && err.status === 0)
          if (this.syncCartOffline) {
            // sseManager有自动重连逻辑，但无法准确知道已断开
            this.sseManager && this.sseManager.disconnect()
          }
          return err
        })
        .finally(() => {
          if (
            !!this.syncCartUpdateRecordList.length && // 如果队列中还有任务
            !this.syncCartOffline && // 非离线状态
            !this.delayedRetryTimer && // 非1102延迟重试中
            !this.cartUpdateTimer // 非防抖300ms中
          ) {
            this.requestCartSync()
          }
        })
    },
    // 从syncCartUpdateRecordList中移除list数据
    removeSyncRequestParams(list = []) {
      //若还有请求在执行时,需要将正在请求的参数合并再去重
      const requestingTask = []
      this.cartSyncLoadQueue.forEach(value => {
        requestingTask.push(...(value.data || []))
      })
      const strList = list.concat(requestingTask).map(it => stableStringify(it))
      return this.syncCartUpdateRecordList.filter(it => {
        const str = stableStringify(it)
        return !strList.includes(str)
      })
    },

    //收集 同步购物车的请求参数,在watch cart执行
    collectCartSyncParam() {
      if (!this.enableSSECartSync) return
      if (this.cartSyncLocked) return
      const record = this.getCartUpdateRecord()

      // 根据type排序,预期同时存在delete和qty时，delete优先
      record.sort((a, b) => a.type.localeCompare(b.type))

      // 根据record匹配购物车的hashRecord,获取tree
      const params = record.reduce((prev, curr) => {
        return prev.concat(this.combineParamByRecord(curr))
      }, [])

      if (!params.length) return

      // 保存计数器
      sessionStorage.setItem("syncCartCounter", (this.syncCartCounter || 0) + "")

      this.updateCartIndexAndIdSet(params.slice(params.length - 1))

      this.initCartHashRecord()

      this.syncCartUpdateRecordList = this.mergeLastUpdateRecord(params)
    },
    // 尝试将record与syncCartUpdateRecordList的最后一个合并
    mergeLastUpdateRecord(list = []) {
      // A编辑时,编辑item被B修改,最终合并后可能多一条数据导致后端计算错误
      if (list.length === 2) {
        const [firstRecord, lastRecord] = list
        const oldFood = lastRecord.oldFood
        // 固定结构为first无oldFood,second有oldFood
        if (firstRecord.oldFood || !oldFood) return this.syncCartUpdateRecordList.concat(list)
        const lastSet = this.removeEqualId(firstRecord.addIdSet, oldFood.deleteIdSet)
        // oldFood 与addIdSet能全部相消且addIdSet相同,移除firstRecord
        if (
          !lastSet.length &&
          stableStringify(firstRecord.addIdSet) === stableStringify(oldFood.addIdSet)
        ) {
          return this.syncCartUpdateRecordList.concat(lastRecord)
        }
        return this.syncCartUpdateRecordList.concat(list)
      }
      const length = this.syncCartUpdateRecordList.length
      if (!length) {
        return list
      }
      const lastItem = JSON.parse(JSON.stringify(this.syncCartUpdateRecordList[length - 1]))
      const [record] = list

      // 有oldFood字段则直接跳过
      if (lastItem.oldFood || record.oldFood) {
        return this.syncCartUpdateRecordList.concat(list)
      } else {
        // 尝试合并
        const lastHash = this.fnv1a(stableStringify(this.getOrderStructure(lastItem)))
        const recordHash = this.fnv1a(stableStringify(this.getOrderStructure(record)))
        if (lastHash === recordHash) {
          this.mergeCartIdSet(lastItem, record)
          // 替换最后一个record
          return [...this.syncCartUpdateRecordList.slice(0, length - 1), lastItem]
        }
      }
      return this.syncCartUpdateRecordList.concat(list)
    },
    cartUpdateDebounce(fn, delay = 200) {
      if (this.cartUpdateTimer) {
        clearTimeout(this.cartUpdateTimer)
      }
      this.cartUpdateTimer = setTimeout(() => {
        fn.apply(this, arguments) // 透传 this和参数
        this.cartUpdateTimer = 0
      }, delay)
    },

    combineParamByRecord(cartRecord) {
      //oldData是指上一次更改的数据,仅用于删除/编辑
      const { type, record = [], data: oldData = {} } = cartRecord

      /*
       * type:delete 移除购物车的某项
       * type:qty 更改购物车的某项数量
       * type:add 新增新的项目
       * type:edit 编辑购物车的某项
       *
       * type:edit时,表现为先delete原项，再add新项
       * record同时包含delete和qty:编辑A后与B合并，A被删除，B更新qty，表现与type:edit一致
       *
       * */

      const findCartItem = record => {
        return this.shopCartList.find(
          it => stableStringify(it.hashRecord) === stableStringify(record)
        )
      }
      const cartItem = findCartItem(record)
      const incrementId = (list = [], count = 0) => {
        const selfId = this.SSEId
        let counter = this.syncCartCounter
        const newIdSet = Array.from(
          { length: count },
          (_, idx) => selfId + "_" + (counter + idx + 1)
        )
        this.syncCartCounter += count
        return list.concat(newIdSet)
      }
      const decreaseId = (addList = [], delList = [], count = 0) => {
        //delList仅在断网情况才会有值(每次同步\sse连接都会清空del)
        // 需要移除已经存在del的id
        const newAddList = addList.filter(_ => !delList.includes(_))
        const deleteIdSet = Array.from(
          { length: count },
          (_, idx) => newAddList[newAddList.length - idx - 1]
        )
        return delList.concat(deleteIdSet).filter(Boolean)
      }
      const lastRecord = record[record.length - 1]
      const list = []
      switch (type) {
        case "delete":
          list.push({
            ...oldData,
            index: oldData.index,
            deleteIdSet: decreaseId(oldData.addIdSet, oldData.deleteIdSet, lastRecord.qty1)
          })
          break
        case "qty":
          const isAdd = record[0].qty1 < lastRecord.qty1
          const sets = isAdd
            ? { addIdSet: incrementId(cartItem.addIdSet, lastRecord.qty1 - record[0].qty1) }
            : {
                deleteIdSet: decreaseId(
                  cartItem.addIdSet,
                  cartItem.deleteIdSet,
                  record[0].qty1 - lastRecord.qty1
                )
              }
          list.push({
            ...this.getFoodSelectedTree(cartItem),
            index: cartItem.index,
            addIdSet: cartItem.addIdSet || [],
            deleteIdSet: cartItem.deleteIdSet || [],
            ...sets
          })
          break
        case "add":
          list.push({
            ...this.getFoodSelectedTree(cartItem),
            addIdSet: incrementId([], cartItem.qty1),
            deleteIdSet: []
          })
          break
        case "edit":
          const items = {
            ...this.getFoodSelectedTree(cartItem),
            addIdSet: incrementId([], cartItem.qty1),
            deleteIdSet: [],
            oldFood: {
              ...oldData,
              addIdSet: this.removeEqualId(oldData.addIdSet, oldData.deleteIdSet),
              deleteIdSet: decreaseId(oldData.addIdSet, oldData.deleteIdSet, record[0].qty1)
            }
          }
          if (!oldData.addIdSet || !oldData.addIdSet.length) {
            delete items.oldFood
          }
          list.push(items)
          break
      }
      return list
    },

    //更新购物车中的addIdSet/deleteIdSet,index字段
    // 在同步购物车数据后执行、SSE回调
    // localUpdate: list为本地数据,而非api返回
    updateCartIndexAndIdSet(list = [], localUpdate = true) {
      list.forEach(it => {
        const tree = this.getOrderStructure(it)
        const hash = this.fnv1a(stableStringify(tree))

        const cartItem = this.findCartEqualHashItem(this.shopCartList, { hash, data: it })
        if (cartItem) {
          if (localUpdate) {
            cartItem.addIdSet = it.addIdSet || []
            cartItem.deleteIdSet = it.deleteIdSet || []
            // 编辑后的数据不应该有index
            if (it.oldFood) delete cartItem.index
          } else {
            //list为接口数据才更新购物车index/qty1
            //仅更新index,qty/idSet以本地为主
            this.$set(cartItem, "index", it.index)

            this.mergeCartIdSet(cartItem, it)
            this.updateHashRecord(cartItem)
          }
        }
      })
      sessionStorage.setItem("shopCartList", JSON.stringify(this.shopCartList))
    },
    //将购物车的idSet合并至list中
    mergeCartIdSet(data, target) {
      if (!target) return data
      const targetItem = target.addIdSet ? target : target.data
      const addIdList = [...new Set((data.addIdSet || []).concat(targetItem.addIdSet || []))]
      const deleteIdList = [
        ...new Set((data.deleteIdSet || []).concat(targetItem.deleteIdSet || []))
      ]
      const addIdSet = this.removeEqualId(addIdList, deleteIdList)
      data.addIdSet = addIdSet
      data.deleteIdSet = deleteIdList
      data.qty1 = addIdSet.length
      return data
    },
    userHasPlacedOrderTip() {
      layer.open({
        skin: "defaultLayer layui-layer-dialog",
        btn: [this.systemLanguage.confirmBtn],
        type: 1,
        title: false,
        content: this.systemLanguage.userHasPlacedOrderTip,
        // closeBtn: 0,
        yes: i => {
          layer.close(i)
          //关闭购物车编辑状态
          this.cartEditXi = false
          // 若为编辑,则关闭详情页(购物车已清空,修改无效)
          if (this.showFoodWarp && this.joinType === "edit") {
            this.showFoodWarp = false
            this.joinType = ""
          }
        }
      })
    },
    //检测页面是否活跃
    detectPageActivity() {
      document.addEventListener("mousedown", this.dispatchActivity)
      // document.addEventListener("keydown", dispatchActivity)
      // document.addEventListener("mousemove", dispatchActivity)
      // document.addEventListener("scroll", dispatchActivity, {
      //   passive: true
      // })
      //仅检测移动端设备
      document.addEventListener("touchstart", this.dispatchActivity)

      this.$nextTick(this.dispatchActivity)
    },
    dispatchActivity() {
      if (!this.enableSSE) return false
      // const lastActivityTime = Date.now()
      // 抛出自定义事件，通知其他监听者有用户活动
      window.dispatchEvent(
        new CustomEvent("userActivity", {
          detail: {
            // timestamp: lastActivityTime
            // 可以根据需要添加更多详细信息，例如：
            // eventType: 'click', // 如果你想区分不同类型的活动
            // mouseX: event.clientX
          }
        })
      )
    },
    handleSSEActivity() {
      if (!this.enableSSE) return false
      const intervalTimeMinutes =
        (this.openTable.sseConnect && this.openTable.sseConnect.inactiveTime) || 0
      if (!intervalTimeMinutes) return false
      this.detectPageActivity()
      let accurateTimer = null
      let closeByInactive = false
      window.addEventListener("userActivity", () => {
        // 每次活跃时，开启新的定时器，到时间后终止SSE连接且不再重连
        // const activityTimestamp = event.detail.timestamp

        if (closeByInactive && !this.sseManager) {
          // 链接已经关闭,则重新打开链接
          this.createSSE()
          closeByInactive = false
        }

        const intervalTime = intervalTimeMinutes * 60 * 1000
        accurateTimer && accurateTimer.destroy()
        accurateTimer = new AccurateTimer(() => {
          closeByInactive = true
          accurateTimer.destroy()
          this.sseManager && this.sseManager.disconnect()
          this.sseManager = null
        }, intervalTime)
        accurateTimer.start()
      })
    },
    // 主动关闭SSE连接
    closeSSE() {
      this.sseManager && this.sseManager.disconnect()
      navigator.sendBeacon(`../sse/close?id=${this.SSEId}`)
    },
    // 创建SSE连接
    createSSE() {
      if (!this.enableSSE) return false
      if (!"EventSource" in window) return false
      // why function:
      // 重连时需要带上最新的id,但默认重连是复用旧链接
      const url = () => {
        const paramsObj = {
          storeNumber: this.openTable.storeNumber,
          tableNumber: this.openTable.tableNumber,
          tableKey: this.openTable.tableKey || null,
          id: this.SSEId || null,
          itemCtrlUpdateTime: this.itemCtrlUpdateTime || null
        }
        for (const key in paramsObj) {
          if (paramsObj[key] === null) delete paramsObj[key]
        }
        return `../sse/createConnection?${new URLSearchParams(paramsObj).toString()}`
      }
      // --- SSE ---
      this.sseManager = connectSSE(url, {
        onMessage: () => {},
        onOpen: () => {
          if (this.syncCartOffline) {
            this.$nextTick(this.checkDiscountAgain)
          }
          this.syncCartOffline = false
          this.tryingAgainSyncCartTimer && this.tryingAgainSyncCartTimer.stop()
        },
        onError: () => {},
        onClose: this.closeSSE,
        onErrorCode: event => {
          const code = event && event.data
          switch (code) {
            case "5003":
              layer.msg(this.systemLanguage.errorTableKey)
              break
            default:
              break
          }
        },
        onRegister: event => {
          const { id = "" } = JSON.parse(event.data)
          this.SSEId = id
          sessionStorage.setItem("sseid", id)
        },
        onItemCtrl: event => {
          /**
           * getFoodType：获取菜单数据 -> 递归合并菜单 -> 处理ItemCtrl逻辑
           * onSSE: onItemCtrl -> 更新Map数据 -> 替换menu数据 -> 递归合并菜单 -> 处理ItemCtrl逻辑
           * */
          cancelIdleCallback(this.updateItemCtrlCallID)
          const data = JSON.parse(event.data)
          this.itemCtrlMap.previous = JSON.parse(JSON.stringify(this.itemCtrlMap.current))
          this.itemCtrlMap.current = data
          // 错开进入页面渲染高峰期导致动画卡顿
          this.updateItemCtrlCallID = requestIdleCallback(this.updateItemCtrl.bind(this, data), {
            timeout: 2000
          })
        },
        onShoppingCart: event => {
          if (!this.enableSSECartSync) return
          const data = JSON.parse(event.data)
          this.updateCartData(data)
        },
        initialRetryDelay: 8000, // 初始重试延迟ms
        backoffFactor: 1,
        maxRetryAttempts: 100 // 比如最多尝试 3 次
      })
    },
    //重新连接SSE
    reconnectSSE() {
      this.sseManager && this.sseManager.disconnect()
      this.sseManager = null
      this.createSSE()
      // 重连SSE时成功后,若之前有失败的同步请求,则重试
    },
    // 更新购物车数据
    updateCartData(data = []) {
      // 根据data重组购物车数据
      //danmuManager push 操作弹幕
      const { data: cartList, version } = data

      console.log("on SSE Cart:", JSON.parse(JSON.stringify(cartList)))

      const versionInvalid = this.sseCartVersion !== version.toString()

      const hasSSEVersion = !!this.sseCartVersion

      this.sseCartVersion = version.toString() // 重置版本号
      sessionStorage.setItem("sseCartVersion", version.toString())

      const retry = this.findCartUnexpectedRecord(cartList)
      if (retry.length) {
        console.log("存在(未同步||未响应)的操作:", retry)
        this.syncCartUpdateRecordList.push(...retry)
      }

      // 版本不一致时，既有用户已经下单，清空购物车
      if (this.sseCartVersion && versionInvalid) {
        //加锁,使更新购物车内容时不触发同步接口
        this.cartSyncLocked = true
        // 有版本号,且不一致则有落单
        if (hasSSEVersion) {
          this.syncCartUpdateRecordList = []
          // 重新获取新的历史订单
          this.getHistoryOrder()
            .then(this.formatHistoryOrderData)
            .catch(() => {})
          this.userHasPlacedOrderTip()
        }
      }

      if (!!this.cartSyncLoadQueue.size) {
        // 若触发SSE回调时,且正在请求同步购物车接口,则需要在同步完成后合并qty/idSet:
        // 正常情况是同步完成仅更新index,但此时应以api结果优先
        this.mergeAfterSyncApi = true
      } else if (this.syncCartUpdateRecordList.length) {
        // 若待请求队列有数据，且未在同步购物车则重试
        this.mergeAfterSyncApi = true
        // 未在防抖中
        if (!this.cartUpdateTimer) {
          this.requestCartSync()
        }
      }

      // 差异日志
      let diffRecord = []
      if (versionInvalid) {
        //版本过期,覆盖购物车的所有数据
        diffRecord = cartList.reduce((prev, curr) => {
          if (curr.addIdSet.length)
            prev.push({
              type: "add",
              hash: this.fnv1a(stableStringify(this.getOrderStructure(curr))),
              data: curr
            })
          return prev
        }, [])

        this.cartSyncLocked = true
        this.clearShopCartData()
      } else {
        const mergeList = this.mergeIdSetOfEqualBaseData(cartList)
        // 还需要合并待请求的参数
        const newMergeList = this.mergeRequestingRecordList(mergeList)
        // 购物车与cartList匹配，找出需要删除、修改、添加的数据
        diffRecord = this.diffRecordWithCart(newMergeList)
      }

      this.reorganizeCartByUpdateRecord(diffRecord)
      // 保存hashRecord
      this.initCartHashRecord()
      this.sendDanmu(diffRecord)
    },
    sendDanmu(diffRecord) {
      if (!diffRecord.length) return
      // 在此更新弹幕
      const transformRecord = this.transformCartUpdateRecord(diffRecord)
      if (!transformRecord) return
      const danmuList = this.transformRecordToDanmu(transformRecord)
      danmuList.forEach(text => {
        text && this.danmuManager && this.danmuManager.push(text)
      })
    },
    //将list中的相同数据的idSet合并
    mergeIdSetOfEqualBaseData(list = []) {
      const map = {}
      for (const item of list) {
        const hash = this.fnv1a(stableStringify(this.getOrderStructure(item)))
        if (!map[hash]) map[hash] = [item]
        else map[hash].push(item)
      }
      const result = []
      for (const hash in map) {
        const sortedList = map[hash].sort((a, b) => b.addIdSet.length - a.addIdSet.length)
        const mergeRes = sortedList.reduce((p, c) => this.mergeCartIdSet(p, c))
        result.push(mergeRes)
      }

      return result
    },
    mergeIdSetOfEqualData(list = []) {
      if (!list || !list.length) return []
      const clone = _ => JSON.parse(JSON.stringify(_))
      const cloneList = clone(list)
      // 这是有index的编辑item
      const hasEditIndexSet = cloneList.filter(it => it.oldFood && isNumeric(it.oldFood.index))
      const result = []
      const noIndexOldSet = []
      const idSet = []
      const calc = (arr = []) => {
        for (const item of arr) {
          const addIdSetStr = stableStringify(item.addIdSet)
          const lastAddItem = cloneList.reduce((prev, curr) => {
            const hash = this.fnv1a(stableStringify(this.getOrderStructure(prev)))
            const currHash = this.fnv1a(stableStringify(this.getOrderStructure(curr)))
            if (curr.oldFood) {
              const currOldHash = this.fnv1a(stableStringify(this.getOrderStructure(curr.oldFood)))
              if (
                currOldHash === hash &&
                stableStringify(prev.addIdSet) === stableStringify(curr.oldFood.addIdSet)
              ) {
                return curr
              }
              noIndexOldSet.push(clone(curr))
              return prev
            } else {
              if (currHash === hash) {
                return this.mergeCartIdSet(clone(prev), clone(curr))
              }
              idSet.push(...curr.addIdSet)
              return prev
            }
          }, item)
          result.push({ ...lastAddItem, oldFood: item.oldFood })
        }
      }
      calc(hasEditIndexSet)

      // 处理没有index的edit(断网新增food,然后edit)
      const hasEditSet = noIndexOldSet.filter(it => it.oldFood && !isNumeric(it.oldFood.index))
      calc(hasEditSet)

      // 处理剩余的更新
      const otherSet = idSet.length
        ? cloneList.filter(it => {
            return idSet.some(_ => it.addIdSet.includes(_))
          })
        : cloneList
      result.push(...this.mergeIdSetOfEqualBaseData(otherSet))

      return result
    },
    //修复未被同步的操作记录,同步保底机制
    findCartUnexpectedRecord(list = []) {
      const retryList = []
      // 需要将list与购物车数据做对比,找出购物车addIdSet多出来的Id
      // 且Id不包含在待同步数组中（syncCartUpdateRecordList），则将其移除||再次同步
      let cloneList = JSON.parse(JSON.stringify(list))

      // 将syncCartUpdateRecordList与list合并
      cloneList = this.mergeRequestingRecordList(cloneList)
      // 将cloneList与shopCart数据做对比,找出cart多出来的数据
      for (const record of cloneList) {
        const cartRecordItem = this.findCartEqualHashItem(this.cartHashRecord, {
          data: record,
          hash: this.fnv1a(stableStringify(this.getOrderStructure(record)))
        })

        if (cartRecordItem) {
          const addIdSet = cartRecordItem.data.addIdSet
          const list = this.removeEqualId(addIdSet, record.addIdSet)
          const unexpectedIdSet = this.removeEqualId(list, record.deleteIdSet)
          if (unexpectedIdSet.length) {
            const includedItem = this.syncCartUpdateRecordList.find(it => {
              const _hash = this.fnv1a(stableStringify(this.getOrderStructure(it)))
              return _hash === cartRecordItem.hash
            })
            if (!includedItem) {
              retryList.push(cartRecordItem.data)
            }
          }
        }
      }
      return this.uniqueBySet(retryList)
    },
    // 将syncCartUpdateRecordList合并至targetList中
    mergeRequestingRecordList(list = []) {
      const recordList = this.mergeIdSetOfEqualData(this.syncCartUpdateRecordList)
      for (const record of recordList) {
        const hash = this.fnv1a(stableStringify(this.getOrderStructure(record)))
        const item = list.find(it => {
          const itHash = this.fnv1a(stableStringify(this.getOrderStructure(it)))
          if (this.mergeSameFoodOnDiffType) return itHash === hash
          return it.ftCode === record.ftCode && itHash === hash
        })
        // !addIdSet:已删除
        if (item && item.addIdSet.length) {
          this.mergeCartIdSet(item, record)
        } else if (record.oldFood) {
          // 处理item以被移除逻辑,需要与oldFood对比
          const hash = this.fnv1a(stableStringify(this.getOrderStructure(record.oldFood)))
          const newItem = list.find(it => {
            const itHash = this.fnv1a(stableStringify(this.getOrderStructure(it)))
            if (this.mergeSameFoodOnDiffType) return itHash === hash
            return it.ftCode === record.ftCode && itHash === hash
          })
          if (newItem && newItem.addIdSet.length) {
            const idx = list.findIndex(it => it === newItem)
            // SSE返回的数据是过时的,跳过本次更新,后续有同步接口兜底
            list.splice(idx, 1)
            console.log("SSE返回的数据无效,跳过更新:", newItem)
            // 直接合并food太过繁琐
            //先合并oldFood
            // const mergedOldFood = this.mergeCartIdSet(newItem, record.oldFood)
            // this.mergeCartIdSet(mergedOldFood, record)
          }
        }
      }
      return list
    },
    // 对象去重
    uniqueBySet(arr) {
      const jsonSet = new Set(arr.map(item => stableStringify(item)))
      return Array.from(jsonSet).map(item => JSON.parse(item))
    },
    //转换diffRecordWithCart返回的结构,
    transformCartUpdateRecord(list = []) {
      if (!list.length) return null
      const group = groupBy(list, it => it.type)
      const same = (a, b) => a.ftCode === b.ftCode && a.fCode === b.fCode

      if (Array.isArray(group.delete)) {
        const editList = []
        // 将delete+add 转换为edit
        // 找到add中的index，删除并添加至edit
        group.delete = group.delete.filter(it => {
          const data = it.data
          const idx = (group.add || []).findIndex(_ => same(_.data, data))
          if (idx !== -1) {
            const target = group.add[idx]
            editList.push({ ...target, type: "edit" })
            group.add.splice(idx, 1)
          }
          return idx === -1
        })
        if (editList.length) {
          group.edit = (group.edit || []).concat(editList)
        }
      }
      return group
    },
    // 转换为弹幕列表
    transformRecordToDanmu(data) {
      const list = []
      const { deleteCartItemTip, addCartItemTip, editCartItemTip } = this.systemLanguage
      const textMap = {
        add: addCartItemTip,
        edit: editCartItemTip,
        delete: deleteCartItemTip
      }
      const createDanmuText = (type, name, qty = 1) => {
        return textMap[type].replace("#name", name).replace("#qty", Math.abs(qty))
      }

      const addAll = (list = [], type) => {
        switch (type) {
          case "delete":
            return list.map(_ => {
              const item = this.reorganizeCartByFoodCodeTree(_.data)
              const title = this.inListTitle(item, this.openTable.language)
              const qty = (_.data.addIdSet || []).length
              //无Qty
              return createDanmuText(type, title, qty)
            })
          case "qty":
            return list.map(_ => {
              if (_.qty === 0) return
              const isAdd = _.qty > 0
              const item = this.findCartEqualHashItem(this.shopCartList, _)
              const updateType = isAdd ? "add" : "delete"
              const title = this.inListTitle(item, this.openTable.language)
              return createDanmuText(updateType, title, _.qty)
            })
          case "add":
            return list.map(_ => {
              const item = this.findCartEqualHashItem(this.shopCartList, _)
              const title = this.inListTitle(item, this.openTable.language)
              return createDanmuText(type, title, item.qty1)
            })
          case "edit":
            return list.map(_ => {
              const item = this.findCartEqualHashItem(this.shopCartList, _)
              const title = this.inListTitle(item, this.openTable.language)
              return createDanmuText(type, title, item.qty1)
            })
          default:
            return []
        }
      }

      for (const type in data) {
        list.push(...addAll(data[type], type))
      }
      return list
    },
    getOrderStructure(data, ignoreTypeCode = this.mergeSameFoodOnDiffType) {
      const result = {
        fCode: data.fCode
      }
      if (!ignoreTypeCode) {
        // 默认情况ftCode是不参与hash计算的，
        // 需要严格相等时，如findCartUnexpectedRecord函数中
        result.ftCode = data.ftCode
      }
      if (data.newOrderItemFoodList) {
        result.newOrderItemFoodList = data.newOrderItemFoodList
      }
      if (data.newOrderItemMListList) {
        result.newOrderItemMListList = data.newOrderItemMListList
      }
      if (data.newOrderItemFoodTypeList) {
        result.newOrderItemFoodTypeList = data.newOrderItemFoodTypeList
      }
      if (data.newOrderItemMTypeList) {
        result.newOrderItemMTypeList = data.newOrderItemMTypeList
      }
      return result
    },
    findCartEqualHashItem(list = [], record = {}) {
      const { hash, data } = record
      return list.find(it => {
        //兼容cartHashRecord与shopCartList
        const list = it.record || it.hashRecord
        if (!list) return false
        const lastHash = list[list.length - 1].hash
        if (this.mergeSameFoodOnDiffType) {
          return lastHash === hash
        }
        const ftCode = it.ftCode || it.data.ftCode
        return data.ftCode === ftCode && lastHash === hash
      })
    },
    //根据record查找index, 仅判断最后一个record是否一致(为兼容普通模式)
    findIndexCartEqualHashItemByAllRecord(list = [], record = {}) {
      const { hash, data } = record
      return list.findIndex(it => {
        const hashList = it.record || it.hashRecord
        if (!hashList) return false
        const lastRecord = hashList[hashList.length - 1]
        if (!lastRecord) return false

        const lastHash = lastRecord.hash
        if (this.mergeSameFoodOnDiffType) {
          return lastHash === hash
        }
        const ftCode = it.ftCode || it.data.ftCode
        return data.ftCode === ftCode && lastHash === hash
      })
    },
    //根据SSE返回的购物车更改内容与购物车对比,计算更改类型:edit/add/delete/qty
    diffRecordWithCart(list = []) {
      const diffRecord = []
      list.forEach(it => {
        const record = {
          data: it
        }
        const tree = this.getOrderStructure(it)
        record.hash = this.fnv1a(stableStringify(tree))
        const cartTarget = this.findCartEqualHashItem(this.cartHashRecord, record)
        const cartLastRecord = cartTarget && cartTarget.record[cartTarget.record.length - 1]
        const cartQty = (cartTarget && cartLastRecord.qty1) || 0
        record.data = this.mergeCartIdSet(it, cartTarget)
        // 后端会返回addIdSet为[]的数据
        if (!cartTarget) {
          //购物车没有此数据则为新增的
          if (it.addIdSet.length) record.type = "add"
        } else if (it.qty1 <= 0) {
          record.type = "delete"
          // 保留删除前的数据，用于弹幕qty
          if (cartTarget.data) record.data = cartTarget.data
        } else if (it.qty1 - cartQty !== 0) {
          // 有qty更改
          record.type = "qty"
          record.qty = it.qty1 - cartQty
        } else {
          // 可能仅有idSet更改,长度为更改
          this.updateCartIndexAndIdSet([it], false)
        }
        //未进入以上分支的为无效数据，没有type
        if (record.type) {
          diffRecord.push(record)
        }
      })
      if (diffRecord.length) console.log("Update cart:", diffRecord)
      return diffRecord
    },
    // 对比2个list，找出有那些更改
    diffRecordWithList(list = [], changedList = []) {
      const oldList = this.removeDuplicateRecord(list)
      const newChangedList = this.removeDuplicateRecord(changedList)
      const recordList = []
      for (const newItem of newChangedList) {
        const oldItem = oldList.find(
          it => it.ftCode === newItem.ftCode && it.fCode === newItem.fCode
        )
        const result = {
          data: newItem
        }
        if (oldItem) {
          // 存在，则判断是否与data有更改
          const newHash = this.fnv1a(stableStringify(this.getOrderStructure(newItem)))
          const oldHash = this.fnv1a(stableStringify(this.getOrderStructure(oldItem)))
          result.hash = oldHash
          if (newHash !== oldHash) {
            result.type = "edit"
          } else {
            const oldQty = this.removeEqualId(oldItem.addIdSet, oldItem.deleteIdSet).length
            const newQty = this.removeEqualId(newItem.addIdSet, newItem.deleteIdSet).length
            if (oldQty !== newQty) {
              if (newQty === 0) {
                result.type = "delete"
                result.data = oldItem
              } else {
                result.type = "qty"
                result.qty = newQty - oldQty
              }
            }
          }
        } else result.type = "add"
        if (result.type) recordList.push(result)
      }
      return recordList
    },
    //移除list中的无效数据
    removeDuplicateRecord(list = []) {
      if (!list || !list.length) return []
      const result = []
      const group = groupBy(list, it => it.ftCode)
      for (const code in group) {
        const list = group[code]
        const fCodeGroup = (group[code] = groupBy(list, it => it.fCode))
        for (const key in fCodeGroup) {
          const lastItem = fCodeGroup[key][fCodeGroup[key].length - 1]
          result.push(lastItem)
        }
      }
      return result
    },
    // 重组购物车内容
    reorganizeCartByUpdateRecord(recordList = []) {
      if (!recordList.length) return
      let shopCartList = JSON.parse(JSON.stringify(this.shopCartList))
      for (const record of recordList) {
        const { type, data, hash } = record

        const cartItem = this.findCartEqualHashItem(shopCartList, { data, hash })

        switch (type) {
          case "add":
            const addItem = this.reorganizeCartByFoodCodeTree(data)
            if (!addItem) break
            this.updateHashRecord(addItem)
            shopCartList.push(addItem)
            break
          case "delete":
            const index = shopCartList.findIndex(it => it === cartItem)
            if (index === -1) break
            shopCartList.splice(index, 1)
            break
          case "edit":
            // SSE重组edit被分离为delete+add
            // 此为cartSync重组逻辑
            const idx = shopCartList.findIndex(it => it === cartItem)
            const newItem = this.reorganizeCartByFoodCodeTree(data)
            if (idx === -1) shopCartList.push(newItem)
            else shopCartList.splice(idx, 1, newItem)
            break
          case "qty":
            cartItem.addIdSet = this.removeEqualId(data.addIdSet, data.deleteIdSet)
            cartItem.deleteIdSet = this.removeEqualId(data.deleteIdSet, data.addIdSet)
            cartItem.qty1 = cartItem.addIdSet.length
            cartItem.index = data.index
            this.updateHashRecord(cartItem)
            break
          default:
            throw Error("Unknown shopping cart update type")
        }
      }
      // 此次购物车更改不会触发同步接口
      this.cartSyncLocked = true
      shopCartList.sort((a, b) => {
        const isNumA = typeof a.index === "number" && !isNaN(a.index)
        const isNumB = typeof b.index === "number" && !isNaN(b.index)
        if (isNumA && isNumB) {
          return a.index - b.index
        } else if (isNumA) {
          return -1
        } else if (isNumB) {
          return 1
        } else {
          return 0
        }
      })
      this.shopCartList = shopCartList.filter(Boolean)

      sessionStorage.setItem("shopCartList", JSON.stringify(this.shopCartList))
    },
    // 合并购物车的qty1, addIdSet与deleteIdSet的差集
    removeEqualId(list = [], target = []) {
      return [...new Set(list.filter(it => !target.includes(it)))]
    },
    // 根据已选的food树重组购物车数据
    reorganizeCartByFoodCodeTree(tree) {
      const list = this.allDataList.filter(it => it.code === tree.ftCode)
      const temp = this.findMainFoodByCode(list, tree.fCode)
      if (!temp) return null
      const food = JSON.parse(JSON.stringify(temp))
      // food下的所有orderItemList都可以在mockTypeList下找到,findMainFoodByCode
      const validAddIdSet = this.removeEqualId(tree.addIdSet, tree.deleteIdSet)
      const completeData = restoreCartItemData(tree, food)
      if (!completeData) return null
      return Object.assign(completeData, {
        qty1: validAddIdSet.length,
        addIdSet: validAddIdSet,
        deleteIdSet: this.removeEqualId(tree.deleteIdSet, tree.addIdSet),
        index: tree.index,
        storeNumber: this.openTable.storeNumber
      })
    },
    // previous:上一次事件的数据的get函数
    updateItemCtrl({ F = {}, M = {} }) {
      /*
       * qty>0:有数量限制
       * qty=0:无限制
       * qty<0:售罄
       * */
      const { F: _F = {}, M: _M = {} } = this.itemCtrlMap.previous
      // 使用此次数据与上一次对比,若有code移除,则init差异对应数据
      const FDiff = Object.fromEntries(Object.entries(_F).filter(([key]) => !(key in F)))
      const MDiff = Object.fromEntries(Object.entries(_M).filter(([key]) => !(key in M)))

      // 先将上一次售罄状态的map清空
      this.updateItemCtrlMap(true)
      this.updateItemCtrlHelper(F)
      this.updateItemCtrlHelper(M)
      this.updateItemCtrlHelper(FDiff, true)
      this.updateItemCtrlHelper(MDiff, true)
      // 未区分foodListCode/mListCode做判断
      // 待更新ItemCtrl的codes
      this.updateInventoryCodes = Object.keys({
        ...F,
        ...M,
        ..._F,
        ..._M,
        ...window.itemCtrlList
      })
      //重置与updateInventoryCodes有关联的codes,后续在递归时push
      this.updateItemCtrlDescendantsCodes = []
      //递归合并菜单,其中有处理更新ItemCtrl相关前置逻辑
      this.updateItemCtrlMenuOptimizer()
      //更新菜单中的ItemCtrl逻辑
      handleMenuItemCtrl(this.allDataList)
      this.updateItemCtrlMap()
      // 若在food详情页,重置当前页数据
      //此外,还需要处理第1/2/3层选中的数据售罄
      if (this.showFoodWarp) {
        this.combineMenuHelper(this.foodInfoItem, "foodMap")
        this.recursiveToDealWith(this.foodInfoItem)
        calculateEffectiveItemCtrl([this.foodInfoItem], true)
      }
      // 更新购物车的库存提示
      if (this.shopCartList.length) {
        this.updateCartItemCtrl()
      }
      this.$forceUpdate()
    },
    // 更新商品售罄字段,clear:清除还原
    updateItemCtrlHelper(map, clear = false) {
      const KEY = "inventory" // 自定义剩余库存key
      for (const key in map) {
        const food = this.menuMap["foodMap"][key]
        const modify = this.menuMap["mListMap"][key]
        const qty = map[key]
        // qty > 0 :显示数量, = 0 :不限制数量, < 0: 已售罄
        if (food) {
          food[KEY] = clear ? null : qty > 0 ? qty : null
          food["itemCtrl"] = clear ? null : qty < 0
        }
        if (modify) {
          modify[KEY] = clear ? null : qty > 0 ? qty : null
          modify["itemCtrl"] = clear ? null : qty < 0
        }
      }
    },
    //需要每次在handleMenuItemCtrl函数后调用更新Map数据
    updateItemCtrlMap(clear = false) {
      //从calculateEffectiveItemCtrl函数获取的所有已售罄的code:itemCtrlList
      //某些food因固定细项、可选type售罄而售罄，也需要设置food的map状态
      const list = window.itemCtrlList || []
      if (!list.length) return
      const map = list.reduce((prev, curr) => {
        prev[curr] = -1
        return prev
      }, {})
      this.updateItemCtrlHelper(map, clear)
    },
    //计算itemCtrl的剩余库存是否满足
    checkItemCtrlQtyCount(item, addNum, source = "normal", hasTip = true) {
      // source :cart:购物车修改 ，normal:其余
      // 若是cart，则不需计算购物车点击item的qty1
      // 若是edit进入foodInfo，无需计算购物车editItem的qty1
      if (!this.enableSSEItemCtrl) return false
      const unique =
        source === "cart" ? item.unique : this.joinType === "edit" ? this.editCartItemUnique : null
      let { inventory = Infinity, qty1, fCode, itemCtrl } = item
      if (itemCtrl) return true
      inventory = !inventory ? Infinity : inventory
      const cartCount = this.calcSelectedItemCount(fCode, unique)
      const addedCount = cartCount + qty1 + addNum
      if (addedCount > inventory) {
        if (hasTip) {
          if (inventory > 0) {
            this.layerDia(this.systemLanguage.insufficientInventory.replace("#qty", inventory))
          } else {
            this.layerDia(this.systemLanguage.soldOutTip)
          }
        }
        return true
      }
    },
    //计算购物车的某个food的总数
    calcSelectedItemCount(code, exclude) {
      const shopCartList = this.shopCartGroup[this.openTable.storeNumber]
      if (!shopCartList) return 0
      return shopCartList
        .filter(it => it.fCode === code)
        .reduce((count, target) => {
          const notSum = exclude && exclude === target.unique
          return count + (notSum ? 0 : target.qty1 || 0)
        }, 0)
    },
    //从allDataList中获取itemCtrl的最新状态
    getItemCtrlStatus({ ftCode, fCode }) {
      const typeItem = this.allDataList.find(type => type.code === ftCode)
      if (!typeItem) return false
      const item = typeItem.foodList.find(it => it.fCode === fCode)
      if (!item && typeItem.item_ctrl_model === 1) return true
      return item._effectiveItemCtrl || item.itemCtrl
    },
    //更新购物车中的itemCtrl数据
    updateCartItemCtrl() {
      if (!this.enableSSEItemCtrl) return false
      // 注意区分foodCourt的多店铺购物车
      const { storeNumber } = this.openTable
      const shopCartList = this.shopCartGroup[storeNumber]
      // 购物车数据结构已变,so仅更新第一层的itemCtrl状态
      this.updateCartItemCtrlHelper(shopCartList)
      this.updateCartItemCtrlHelper(this.shopCartList)
      this.updateCartItemCtrlHelper(this.shopCartSourceList)
      this.editCartItemUnique = null
    },
    //无法复用updateItemCtrlHelper函数
    updateCartItemCtrlHelper(list = []) {
      const KEY = "inventory" // 自定义剩余库存key
      const { storeNumber } = this.openTable
      for (let item of list) {
        if (storeNumber !== item.storeNumber) continue
        if (
          !this.updateInventoryCodes
            .concat(this.updateItemCtrlDescendantsCodes)
            .includes(item.fCode || item.code)
        )
          continue
        const mapKey = item.fCode ? "foodMap" : "mListMap"
        const mapItem = this.menuMap[mapKey][item.fCode || item.code]
        if (!mapItem) continue
        //与原值不同才赋值
        if (item[KEY] !== mapItem[KEY]) item[KEY] = mapItem[KEY]
        const itemCtrlStatus = this.getItemCtrlStatus(item)
        if (item.itemCtrl !== itemCtrlStatus) item.itemCtrl = itemCtrlStatus
      }
    },
    // 根据item的子级库存更新自身库存
    updateFoodInventoryAccordingChild(item, typeItem) {
      if (!this.enableSSEItemCtrl || !this.combineMenuCompleted) return
      const codeKey = item.fCode ? "fCode" : "code"
      const mapKey = item.fCode ? "foodMap" : "mListMap"
      // 该item下的所有固定细项的codes
      const codes = (this.menuMustItemMap[typeItem.code] || {})[item[codeKey]] || []
      //注意:未区分foodListCode/mListCode
      const updateItemCtrlMap = Object.values(this.itemCtrlMap.current).reduce(
        (prev, curr) => ({ ...prev, ...curr }),
        {}
      )
      const updateItemCtrlCodes = Object.keys(updateItemCtrlMap)
      const intersect = new Set([...codes].filter(x => updateItemCtrlCodes.includes(x)))
      if (intersect.size) {
        let itemInventory = []
        let completed = false
        // item下有固定细项设置库存数量，修改item库存数量
        // 主food下的固定细项数量推至主food且优先显示，超出数量则主food售罄
        intersect.forEach(code => {
          if (updateItemCtrlMap[code] <= 0) return // 不限制数量/售罄则跳过
          if (completed) return
          // 分别判断codes中code数量是否超出itemCtrl库存数量
          // 超出则售罄item，未超出则最后去最少的库存作为item的库存
          const count = codes.filter(_ => code === _).length
          if (count > updateItemCtrlMap[code]) {
            // 售罄
            item.itemCtrl = true
            item.inventory = null
            completed = true
          } else {
            // 保存每个code对应主food的有效库存
            itemInventory.push(Math.floor(updateItemCtrlMap[code] / count))
          }
        })
        if (!completed) {
          // 自身库存与根据child计算的可用库存比较,取min
          const selfInventory = updateItemCtrlMap[item[codeKey]] || Infinity
          item.inventory = Math.min(...itemInventory, selfInventory)
        }
        this.updateInventoryCodes.push(item[codeKey])
      } else {
        //复原自身库存
        item.inventory = updateItemCtrlMap[item[codeKey]]
      }
      this.menuMap[mapKey][item[codeKey]].inventory = item.inventory
      this.menuMap[mapKey][item[codeKey]].itemCtrl = item.itemCtrl
    },

    //根据itemCtrl更新菜单数据
    updateItemCtrlMenuOptimizer() {
      const intersect = (a, b) => a.filter(v => b.indexOf(v) > -1)
      // 找到所有需要更新的type，替换foodList进入递归
      const replacer = []
      for (const key in this.menuDescendantMap) {
        const val = intersect(this.updateInventoryCodes, this.menuDescendantMap[key])
        if (val.length) {
          replacer.push(key)
        }
      }
      const codes = []
      this.allDataList.forEach(typeItem => {
        if (replacer.includes(typeItem.code)) {
          const target = this.menuSourceList.filter(t => t.code === typeItem.code)
          if (!target.length) return
          const cloneData = JSON.parse(JSON.stringify(target[0].foodList))
          // 此处数据为最原始数据,需要先组合Map的itemCtrl/inventory
          cloneData.forEach(item => {
            const mapKey = item.fCode ? "foodMap" : "mListMap"
            const mapItem = this.menuMap[mapKey][item.fCode || item.code]
            if (!mapItem) return
            item.inventory = mapItem.inventory
            item.itemCtrl = mapItem.itemCtrl
            // 需要photoSuffix、photoTime，此字段在Map中，需合并完成才会有
            item.photoSuffix = mapItem.photoSuffix
            item.photoTime = mapItem.photoTime
          })
          this.$set(typeItem, "foodList", cloneData)
          codes.push(...this.menuDescendantMap[typeItem.code])
          this.formatMenuFoodItem(typeItem)
        }
      })
      this.updateItemCtrlDescendantsCodes = [...new Set(codes)]
      //仅将有itemCtrl影响的type更新,其余传入empty函数
      const taskList = Array.from({ length: this.allDataList.length }, (it, idx) => {
        const code = this.allDataList[idx].code
        const ignore = !replacer.includes(code)
        //传递的引用,会更新allDataList
        return ignore ? () => {} : this.recursiveMergeMenu.bind(this, [this.allDataList[idx]])
      })
      createTaskScheduler(taskList)
    }
  }
}
