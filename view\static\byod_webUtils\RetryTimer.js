class RetryTimer {
  /**
   * @param {() => Promise<any> | boolean} action The function to execute. It should return a promise or a boolean. The retry stops if it returns true or the promise resolves.
   * @param {object} options Configuration options.
   * @param {number} options.initialInterval Initial delay in milliseconds (e.g., 3000).
   * @param {number} [options.backoffFactor=1.5] The multiplier for the delay.
   * @param {number} [options.maxInterval=60000] The maximum delay in milliseconds.
   * @param {number} [options.maxRetries=Infinity] The maximum number of retry attempts.
   * @param {(attempt: number, delay: number) => void} [options.onRetry] Optional callback executed before each retry attempt.
   */
  constructor(
    action,
    {
      initialInterval,
      backoffFactor = 1.5,
      maxInterval = 60000,
      maxRetries = Infinity,
      onRetry = null
    }
  ) {
    if (typeof action !== "function") {
      throw new Error('The "action" must be a function.')
    }
    if (typeof initialInterval !== "number" || initialInterval <= 0) {
      throw new Error('The "initialInterval" must be a positive number.')
    }

    this.action = action
    this.initialInterval = initialInterval
    this.backoffFactor = backoffFactor
    this.maxInterval = maxInterval
    this.maxRetries = maxRetries
    this.onRetry = onRetry

    this.retryCount = 0
    this.currentInterval = this.initialInterval
    this.timerId = null
    this.isRunning = false

    // For promise-based control
    this._rejectPromise = null
  }

  /**
   * Starts the retry timer.
   * @returns {Promise<any>} A promise that resolves with the action's return value on success, or rejects on failure (max retries reached or manual stop).
   */
  start() {
    if (this.isRunning) {
      console.warn("Timer is already running.")
      return Promise.reject("Timer is already running.")
    }

    this.isRunning = true

    return new Promise((resolve, reject) => {
      this._rejectPromise = reject // Store reject for manual stop
      this._scheduleRetry(resolve, reject)
    })
  }

  /**
   * Stops the timer and rejects the promise returned by start().
   */
  stop() {
    if (!this.isRunning) {
      return
    }
    // console.log("Timer stopped manually.")
    this.clear()
    if (this._rejectPromise) {
      this._rejectPromise(new Error("Timer was stopped manually."))
    }
  }

  /**
   * Alias for stop(). Clears any pending retries and resets the timer's state.
   */
  clear() {
    clearTimeout(this.timerId)
    this.timerId = null
    this.isRunning = false
    this.retryCount = 0
    this.currentInterval = this.initialInterval
    this._rejectPromise = null
  }

  _scheduleRetry(resolve, reject) {
    if (!this.isRunning) {
      return
    }

    // Check if max retries have been reached BEFORE scheduling the next one.
    if (this.retryCount >= this.maxRetries) {
      const errorMessage = `Failed after ${this.maxRetries} attempts.`
      console.error(errorMessage)
      this.clear()
      reject(new Error(errorMessage))
      return
    }

    // const delay = this.retryCount === 0 ? 0 : this.currentInterval // First attempt can be immediate
    const delay = this.currentInterval // First attempt can be immediate

    if (this.retryCount > 0 && typeof this.onRetry === "function") {
      this.onRetry(this.retryCount, delay)
    }

    this.timerId = setTimeout(async () => {
      try {
        // console.log(`Attempt #${this.retryCount + 1}...`)
        const result = await Promise.resolve(this.action())

        // If action returns true or resolves, it's a success
        if (result === true) {
          // console.log("Action succeeded.")
          this.clear()
          resolve(result)
        } else {
          // Action returned a falsy value (but didn't throw), so we retry
          this._prepareNextRetry(resolve, reject)
        }
      } catch (error) {
        // Action threw an error or returned a rejected promise
        // console.warn(`Attempt #${this.retryCount + 1} failed:`, error.message)
        this._prepareNextRetry(resolve, reject)
      }
    }, delay)
  }

  _prepareNextRetry(resolve, reject) {
    this.retryCount++

    // Calculate next interval
    const nextInterval = this.currentInterval * this.backoffFactor
    this.currentInterval = Math.min(nextInterval, this.maxInterval)

    // Schedule the next attempt
    this._scheduleRetry(resolve, reject)
  }
}
