#review-order-view {
    box-sizing: border-box;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-top: 0.8rem;
    /*padding-bottom: 60px;*/
}
#review-order-view .header{
    width: 100%;
    background-color: var(--styleColor);
    position: absolute;
    font-size: 0.35rem;
    color: #fff;
    height: 1.2rem;
    display: flex;
    align-items: center;
    top: 0;
    left: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
}
.pc #review-order-view .header{
    font-size: 16px;
}
#review-order-view .main{
    overflow: auto;
    /*padding: 0.4rem 0;*/
    outline: none;
}
#review-order-view .footer{
    width: 100%;
    /*height: 1.3rem;*/
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.2rem;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    z-index: 100;
    background-color: #FFFFFF;
}
#review-order-view .footer .total-price{
    flex: 1;
}
#review-order-view .footer .total-price .label {
    font-size: 0.38rem;
}
#review-order-view .footer .total-price .amount {
    font-size: 0.48rem;
    font-weight: 700;
    color: #ee0a24;
    margin-left: 0.4rem;
}


.your-cart,.past-orders{
    /*padding: 0.1rem;*/
    min-height: 5rem;
}


/*--------------- button   start----------------*/
#review-order-view .btn {
    border: none;
    background-color: var(--styleColor);
    color: white;
    padding: 10px 0.35rem;
    border-radius: 0.2rem;
    /*font-weight: 600;*/
    font-size: 0.45rem;
    cursor: pointer;
    transition:  box-shadow 0.2s;
    min-width: min(140px,4rem);
    height: 1.1rem;
}
#review-order-view .btn:hover {
    opacity: 0.9;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}
#review-order-view .btn-icon {
    background: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
#review-order-view .btn-icon:hover {
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: none;
}
/*--------------- button   end----------------*/


/*--------------- Card   start----------------*/

#review-order-view .card {
    background-color: #fff;
    /*border: 1px solid #eee;*/
    border-radius: 12px;
    /*box-shadow: 0 4px 6px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.08);*/
    margin-bottom: 0.4rem;
    overflow: hidden;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}
#review-order-view  .card:hover {
    /*transform: translateY(-1px);*/
    /*box-shadow: 0 8px 12px rgba(0, 0, 0, 0.06), 0 3px 6px rgba(0, 0, 0, 0.1);*/
}
#review-order-view .card .card-title {
    margin: 0.5rem 0;
    font-size: 0.5rem;
    /*border-bottom: 1px solid #eee;*/
    color: var(--styleColor);
}
#review-order-view .card .cart_food_null_txt{
    padding: 0.7rem 0.4rem;
    color: #cccccc;
    width: 100%;
    font-size: 0.35rem;
}
/*--------------- Card   end----------------*/


/*--------------- List   start----------------*/
.list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.2rem 0.3rem;
    border-bottom: 1px solid #eee;
}
.list-item:last-child {
    border-bottom: none;
}
.list-item-content {
    flex-grow: 1;
}
.list-item-title {
    font-weight: 550;
    font-size: 0.4rem;
}
.list-item-subtitle {
    font-size: 0.3rem;
    color: #666;
    margin-top: 4px;
}
.list-item-sub-items {
    font-size: 0.35rem;
    color: #666;
    padding-left: 0.1rem;
    margin-top: 0.2rem;
}
.list-item-action {
    font-weight: 500;
}

/*--------------- List   end----------------*/

/*--------------- food   layout start----------------*/
#review-order-view .food-item-layout{
    padding: 0.3rem 0.2rem;
}

.food-item-layout .food-item{
    display: flex;
    flex-direction: column;
    padding: 0.1rem 0.5rem;
    color: rgba(0, 0, 0, 0.7);
}
.food-item .food-title {
    font-size: 0.35rem;
}
.food-item .food-sub-item{
    font-size: 0.28rem;
    padding: 0.05rem 0.2rem 0.05rem 0.2rem;
}
.food-item .food-price-qty{
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    font-size: 0.3rem;
}
.food-price-qty .food-price{
    color: #ee0a24;
    font-size: 0.35rem;
}
.food-price-qty .food-qty{
     font-size: 0.4rem;
 }
.create-order-time{
    position: relative;
    height: 0.5rem;
    margin: 0 0.4rem;
}
.past-orders .create-order-time{
    margin-top: 0.3rem;
}
.create-order-time i{
    display: block;
    height: 1px;
    background: #e1e1e1;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
}
.create-order-time p{
    font-size: 0.35rem;
    color: #c1c1c1;
    background: white;
    padding: 0 0.5rem;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}
/*--------------- food   layout end----------------*/
